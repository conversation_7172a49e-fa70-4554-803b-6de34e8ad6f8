package cms.utils;

import lombok.extern.slf4j.Slf4j;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.FFmpegLogCallback;
import org.bytedeco.javacv.Frame;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/9/6 16:02
 */
@Slf4j
public class Mp4Util {

    public static InputStream mp4Code(InputStream is) throws Exception {
        FFmpegFrameGrabber frameGrabber = new FFmpegFrameGrabber(is);
        Frame captured_frame = null;
        FFmpegFrameRecorder recorder = null;
        String fileName = System.getProperty("user.dir") + UUIDUtil.getUUID22() + ".mp4";
        try {
            frameGrabber.start();
            recorder = new FFmpegFrameRecorder(fileName, frameGrabber.getImageWidth(), frameGrabber.getImageHeight(), frameGrabber.getAudioChannels());
            recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
            recorder.setFormat("mp4");
            recorder.setFrameRate(frameGrabber.getFrameRate());
            recorder.setVideoBitrate(frameGrabber.getVideoBitrate());
            recorder.setAudioBitrate(frameGrabber.getAudioBitrate());
            recorder.setAudioOptions(frameGrabber.getAudioOptions());
            recorder.setAudioQuality(0);
            recorder.setSampleRate(frameGrabber.getSampleRate());
            recorder.setAudioCodec(frameGrabber.getAudioCodec());
            FFmpegLogCallback.set();
            recorder.start();
            while (true) {
                try {
                    captured_frame = frameGrabber.grabFrame();

                    if (captured_frame == null) {
                        log.info("!!! Failed cvQueryFrame");
                        break;
                    }
                    recorder.record(captured_frame);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            recorder.stop();
            recorder.release();
            frameGrabber.stop();
            frameGrabber.release();
            recorder.close();
            frameGrabber.close();

            File file = new File(fileName);
            FileInputStream fis = new FileInputStream(file);
            file.delete();
            return fis;
        } catch (Exception e) {
            log.error("mp4Code conver error!", e);
            throw e;
        } finally {
        }
    }
}
