package cms.bean.api;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/29 17:53
 */
@Data
public class TopicApi implements Serializable {

    private static final long serialVersionUID = -684257451052921859L;

    private Long tagId;

    private List<Long> twoTagId;

    private String title;

    private String content;

    private Long operationId;
}
