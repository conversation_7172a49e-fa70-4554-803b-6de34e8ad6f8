package cms.bean.sys;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/25 16:39
 */
@Entity
@Table(name = "operation_log")
@Data
public class OperationLog implements Serializable {

    private static final long serialVersionUID = -29531301029127372L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long userId;

    @Column(nullable = false)
    private Date postTime;

    private String requestPath;
}
