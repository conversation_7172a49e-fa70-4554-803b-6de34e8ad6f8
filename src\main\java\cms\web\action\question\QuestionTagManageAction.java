package cms.web.action.question;


import cms.bean.*;
import cms.bean.question.QuestionTag;
import cms.bean.topic.Tag;
import cms.constraints.validateGroups.Add;
import cms.constraints.validateGroups.Update;
import cms.handle.CustomException;
import cms.service.question.QuestionTagService;
import cms.service.setting.SettingService;
import cms.utils.JsonUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

import static cms.constant.Constant.QUESTION_TAG_GRADE_ONE;
import static cms.constant.ErrorCode.*;


/**
 * 问题标签
 */
@Controller
@RequestMapping("/control/questionTag/manage")
public class QuestionTagManageAction {

    @Resource
    QuestionTagService questionTagService;
    @Resource
    QuestionTagManage questionTagManage;


    @Resource
    SettingService settingService;

    /**
     * 问题标签   添加界面显示
     */
    @ResponseBody
    @RequestMapping(params = "method=add", method = RequestMethod.GET)
    public String addUI(ModelMap model, Tag tag, Long parentId,
                        HttpServletRequest request, HttpServletResponse response) throws Exception {
        //错误
        Map<String, String> error = new HashMap<String, String>();
        Map<String, Object> returnValue = new HashMap<String, Object>();
        if (parentId != null) {//判断父类ID是否存在;
            QuestionTag _tag = questionTagService.findById(parentId);
            if (_tag != null) {
                returnValue.put("parentTag", _tag);//返回消息


                Map<Long, String> navigation = new LinkedHashMap<Long, String>();

                List<QuestionTag> parentProductTypeList = questionTagService.findAllParentById(_tag);
                for (QuestionTag p : parentProductTypeList) {
                    navigation.put(p.getId(), p.getName());
                }
                navigation.put(_tag.getId(), _tag.getName());
                returnValue.put("navigation", navigation);//标签导航
            } else {
                error.put("parentId", "父类不存在");
            }


        }

        if (error.size() > 0) {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
        } else {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, returnValue));
        }
    }

    /**
     * 问题标签  添加
     */
    @ResponseBody
    @RequestMapping(params = "method=add", method = RequestMethod.POST)
    public String add(ModelMap model, @Validated(Add.class) QuestionTag questionTag,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {
        Long parentId = Optional.ofNullable(questionTag.getParentId()).orElse(0L);
        if (questionTagService.findAllQuestionTag_cache().stream().filter(o -> parentId.equals(o.getParentId())).filter(o -> o.getName().equals(questionTag.getName())).count() > 0) {
            throw new CustomException(C_1_0003_0004, "name");
        }
        Optional<QuestionTag> parentTag = Optional.ofNullable(questionTag.getParentId()).map(o -> Optional.ofNullable(questionTagService.findById(o)).orElseThrow(() -> new CustomException(C_1_0003_0002, "parentId")));
        String parentIdGroup = parentTag.map(o -> o.getParentIdGroup() + o.getId() + ",").orElse(",0,");
        Integer grade = parentTag.map(QuestionTag::getGrade).map(o -> Optional.of(o).filter(x -> x <= 1).map(x -> x + 1).orElseThrow(() -> new CustomException(C_1_0003_0001, "parentId"))).orElse(1);

        questionTag.setId(questionTagManage.nextNumber());
        questionTag.setParentId(parentId);
        questionTag.setParentIdGroup(parentIdGroup);
        questionTag.setGrade(grade);
        questionTag.setChildNodeNumber(Optional.ofNullable(questionTag.getChildNodeNumber()).orElse(0));
        questionTagService.saveQuestionTag(questionTag);
        QuestionTagManage.addNewOneTagCount(questionTag);

        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
    }


    /**
     * 标签   修改界面显示
     */
    @ResponseBody
    @RequestMapping(params = "method=edit", method = RequestMethod.GET)
    public String editUI(ModelMap model, Long questionTagId,
                         HttpServletRequest request, HttpServletResponse response) throws Exception {
        //错误
        Map<String, String> error = new HashMap<String, String>();
        Map<String, Object> returnValue = new HashMap<String, Object>();

        if (questionTagId != null) {//判断ID是否存在;
            QuestionTag tag = questionTagService.findById(questionTagId);
            if (tag != null) {
                returnValue.put("tag", tag);//返回消息

                int i = 0;
                Map<Long, String> navigation = new LinkedHashMap<Long, String>();
                List<QuestionTag> parentTagList = questionTagService.findAllParentById(tag);
                for (QuestionTag p : parentTagList) {
                    navigation.put(p.getId(), p.getName());
                    i++;
                    if (i == 1) {
                        returnValue.put("parentTag", p);

                    }

                }
                returnValue.put("navigation", navigation);//分类导航
            }
        } else {
            error.put("questionTagId", "标签Id不能为空");
        }
        if (error.size() > 0) {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
        } else {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, returnValue));
        }
    }

    /**
     * 标签   修改
     */
    @ResponseBody
    @RequestMapping(params = "method=edit", method = RequestMethod.POST)
    public String edit(ModelMap model, Long questionTagId, @Validated(Update.class) QuestionTag questionTag,
                       HttpServletRequest request, HttpServletResponse response) throws Exception {
        QuestionTag tag = Optional.ofNullable(questionTagId).map(o -> Optional.ofNullable(questionTagService.findById(o)).orElse(null)).orElseThrow(() -> new CustomException(C_1_0003_0003, "questionTagId"));
        if (questionTagService.findAllQuestionTag_cache().stream().filter(o -> tag.getParentId().equals(o.getParentId())).filter(o -> !o.getId().equals(questionTagId)).filter(o -> o.getName().equals(questionTag.getName())).count() > 0) {
            throw new CustomException(C_1_0003_0004, "name");
        }
        questionTag.setId(questionTagId);
        questionTagService.updateQuestionTag(questionTag);
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
    }

    /**
     * 标签 删除
     */
    @ResponseBody
    @RequestMapping(params = "method=delete", method = RequestMethod.POST)
    public String delete(ModelMap model, Long questionTagId,
                         HttpServletRequest request, HttpServletResponse response) throws Exception {
        //错误
        Map<String, String> error = new HashMap<String, String>();
        if (questionTagId != null && questionTagId > 0L) {
            QuestionTag tag = questionTagService.findById(questionTagId);

            //根据标签Id查询子标签(下一节点)
            List<QuestionTag> childTagList = questionTagService.findChildTagById(questionTagId);

            List<Long> tagIdList = new ArrayList<Long>();
            tagIdList.add(tag.getId());
            if (childTagList != null && childTagList.size() > 0) {
                for (QuestionTag t : childTagList) {
                    tagIdList.add(t.getId());
                }
            }

            int i = questionTagService.deleteQuestionTag(tag);
            if (i > 0) {
                return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
            }

        } else {
            error.put("questionTagId", "标签Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    /**
     * 问题标签 查询所有标签
     */
    @ResponseBody
    @RequestMapping(params = "method=allTag", method = RequestMethod.GET)
    public String queryAllTag(ModelMap model,
                              HttpServletRequest request, HttpServletResponse response) throws Exception {

        List<QuestionTag> questionTagList = questionTagService.findAllQuestionTag();
        List<QuestionTag> new_questionTagList = new ArrayList<QuestionTag>();//排序后标签

        if (questionTagList != null && questionTagList.size() > 0) {

            //组成排序数据
            Iterator<QuestionTag> questionTag_iter = questionTagList.iterator();
            while (questionTag_iter.hasNext()) {
                QuestionTag questionTag = questionTag_iter.next();

                //如果是根节点
                if (questionTag.getParentId().equals(0L)) {

                    new_questionTagList.add(questionTag);
                    questionTag_iter.remove();
                }
            }
            //组合子标签
            for (QuestionTag questionTag : new_questionTagList) {
                questionTagManage.childQuestionTag(questionTag, questionTagList);
            }
            //排序
            questionTagManage.questionTagSort(new_questionTagList);
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, new_questionTagList));
    }


    /**
     * 标签 查询标签分页
     */
    @ResponseBody
    @RequestMapping(params = "method=questionTagPage", method = RequestMethod.GET)
    public String queryQuestionTagPage(ModelMap model, PageForm pageForm, Long parentId,
                                       HttpServletRequest request, HttpServletResponse response) throws Exception {
        //错误
        Map<String, String> error = new HashMap<String, String>();
        Map<String, Object> returnValue = new HashMap<String, Object>();


        StringBuffer jpql = new StringBuffer();
        //存放参数值
        List<Object> params = new ArrayList<Object>();

        //如果所属父类有值
        if (parentId != null && parentId > 0L) {
            jpql.append(" and o.parentId=?" + (params.size() + 1));//所属父类的ID;(params.size()+1)是为了和下面的条件参数兼容
            params.add(parentId);//设置o.parentId=?2参数
        } else {//如果没有父类
            jpql.append(" and o.parentId=?" + (params.size() + 1));
            params.add(0L);
        }

        PageView<QuestionTag> pageView = new PageView<QuestionTag>(settingService.findSystemSetting_cache().getBackstagePageNumber(), pageForm.getPage(), 10);
        //当前页
        int firstindex = (pageForm.getPage() - 1) * pageView.getMaxresult();
        //排序
        LinkedHashMap<String, String> orderby = new LinkedHashMap<String, String>();

        orderby.put("sort", "desc");//根据sort字段降序排序

        //删除第一个and
        String _jpql = org.apache.commons.lang3.StringUtils.difference(" and", jpql.toString());

        //调用分页算法类
        QueryResult<QuestionTag> qr = questionTagService.getScrollData(QuestionTag.class, firstindex, pageView.getMaxresult(), _jpql, params.toArray(), orderby);

        pageView.setQueryResult(qr);
        returnValue.put("pageView", pageView);

        //分类导航
        if (parentId != null && parentId > 0L) {
            Map<Long, String> navigation = new LinkedHashMap<Long, String>();
            QuestionTag questionTag = questionTagService.findById(parentId);
            if (questionTag != null) {
                List<QuestionTag> questionTagList = questionTagService.findAllParentById(questionTag);
                for (QuestionTag p : questionTagList) {
                    navigation.put(p.getId(), p.getName());
                }
                navigation.put(questionTag.getId(), questionTag.getName());
            }
            returnValue.put("navigation", navigation);
        }

        if (error.size() > 0) {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
        } else {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, returnValue));
        }
    }

}
