package cms.bean.topic;

import cms.bean.ErrorView;
import cms.constant.ErrorCode;
import cms.service.messageSource.ErrorMessageService;
import cms.utils.SpringUtils;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 隐藏标签名称
 */
@Deprecated
public class HideTagName {
    private static Map<Integer, Integer> map = new LinkedHashMap<Integer, Integer>();

    static {
        map.put(HideTagType.PASSWORD.getName(), ErrorCode.C_2_0001_0003);
        map.put(HideTagType.COMMENT.getName(), ErrorCode.C_2_0001_0004);
        map.put(HideTagType.GRADE.getName(), ErrorCode.C_2_0001_0005);
        map.put(HideTagType.POINT.getName(), ErrorCode.C_2_0001_0006);
        map.put(HideTagType.AMOUNT.getName(), ErrorCode.C_2_0001_0007);
    }

    public static String getKey(Integer key) {
        return Optional.ofNullable(map.get(key)).map(SpringUtils.getBean(ErrorMessageService.class)::getMessageNo500).map(o -> o.orElse(null)).orElse(null);
    }

}
