package cms.service.operation;

import cms.bean.operation.Operation;
import cms.service.besa.DaoSupport;
import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.persistence.Query;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/25 12:10
 */
@Service
@Slf4j
public class OperationService extends DaoSupport<Operation> {

    @Cacheable(value = "api_operation_id_cache", key = "'getOperation_'+#id")
    public Optional<Operation> getById(Long id) {
        Query query = em.createQuery("select o from Operation o where o.id=?1")
                .setParameter(1, id);
        return Optional.ofNullable(query.getResultList()).filter(CollUtil::isNotEmpty).map(l -> (Operation) l.get(0));
    }
}
