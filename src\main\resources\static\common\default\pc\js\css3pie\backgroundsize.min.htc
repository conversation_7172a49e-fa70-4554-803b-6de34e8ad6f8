<!-- background-size-polyfill v0.2.0 | (c) 2012-2013 <PERSON><PERSON><PERSON><PERSON><PERSON> | MIT License -->
<PUBLIC:COMPONENT lightWeight="true">
<PUBLIC:ATTACH EVENT="oncontentready" ONEVENT="o.init()" />
<PUBLIC:ATTACH EVENT="ondocumentready" ONEVENT="o.init()" />
<PUBLIC:ATTACH EVENT="onpropertychange" ONEVENT="o.handlePropertychange()" />
<PUBLIC:ATTACH EVENT="ondetach" ONEVENT="o.restore()" />
<PUBLIC:ATTACH EVENT="onresize" FOR="window" ONEVENT="o.handleResize()" />
<PUBLIC:EVENT NAME="onbackgroundupdate" ID="updateEvent" />
<script type="text/javascript">
var o;!function(a,b){var c=/url\(["']?(.*?)["']?\)/,d=/^\s\s*/,e=/\s\s*$/,f=/\s\s*/g,g=/%$/,h={top:0,left:0,bottom:1,right:1,center:.5},i=a.document,j="data:image/gif;base64,R0lGODlhAQABAIABAP///wAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",k="background-size-polyfill",l=function(){},m=100,n,p,q,r;function s(){var b=i.createElement("div"),c=i.createElement("img"),d=b.style,e=a.style,f=a.currentStyle,g=a.bgsExpando,h=a.firstChild;g&&(g.restore&&(e.backgroundImage=g.restore.backgroundImage,e.position=g.restore.position,e.zIndex=g.restore.zIndex),h&&"DIV"===(h.nodeName||"").toUpperCase()&&h.className===k&&a.removeChild(h)),t(b),b.className=k,d.top=d.right=d.bottom=d.left=0,d.position="fixed",t(c),c.alt="",b.appendChild(c),a.insertBefore(b,a.firstChild),a.bgsExpando=g={wrapper:b,img:c,restore:{backgroundImage:e.backgroundImage,position:e.position,zIndex:e.zIndex},current:{},next:null,processing:!1,loadImg:null,display:!1,changed:!1,ignore:!1,canFixed:"BODY"===a.nodeName.toUpperCase()&&b.offsetHeight>0},d.position="absolute","auto"===f.zIndex&&(e.zIndex=0),"static"===f.position&&(e.position="relative"),o={init:l,handlePropertychange:D,restore:F,handleResize:E},D()}function t(a){var b=a.style;b.position="absolute",b.display="block",b.zIndex=-1,b.overflow="hidden",b.visibility="inherit",b.width=b.height=b.top=b.right=b.bottom=b.left=b.cursor="auto",b.margin=b.padding=b.border=b.outline=b.minWidth=b.minHeight=0,b.background=b.maxWidth=b.maxHeight="none",b.fontSize=b.lineHeight="1em"}function u(a,c,d){var e;c?(e=i.createElement("img"),e.onload=e.onerror=function(){var c=this.width,e=this.height;"error"===b.event.type&&(c=e=0),a.loadImg=this.onload=this.onerror=null,d(c,e)},e.src=c):e={callbackId:b.setTimeout(function(){a.loadImg=null,d(0,0)},0)},a.loadImg=e,e=null}function v(a){var b=o.handlePropertychange;o.handlePropertychange=l,a(),o.handlePropertychange=b}function w(a,b){var c=a.currentStyle.display;return c!==b.display&&(b.display=c,b.changed=!0),"none"!==c}function x(a,b){var d=a.style,e=a.currentStyle,f=b.restore,i=y(e["background-size"]),k=i.split(" "),l={innerWidth:a.offsetWidth-(parseFloat(e.borderLeftWidth)||0)-(parseFloat(e.borderRightWidth)||0),innerHeight:a.offsetHeight-(parseFloat(e.borderTopWidth)||0)-(parseFloat(e.borderBottomWidth)||0),size:i,sizeIsKeyword:"contain"===i||"cover"===i,sizeX:k[0],sizeY:k.length>1?k[1]:"auto",posX:e.backgroundPositionX,posY:e.backgroundPositionY,attachment:e.backgroundAttachment,src:"",imgWidth:0,imgHeight:0};return l.sizeIsKeyword||((parseFloat(l.sizeX)>=0||"auto"===l.sizeX)&&(parseFloat(l.sizeY)>=0||"auto"===l.sizeY)||(l.sizeX=l.sizeY="auto"),g.test(l.sizeX)&&(l.sizeX=(l.innerWidth*parseFloat(l.sizeX)/100||0)+"px"),g.test(l.sizeY)&&(l.sizeY=(l.innerHeight*parseFloat(l.sizeY)/100||0)+"px")),(l.posX in h||g.test(l.posX))&&(l.posX=h[l.posX]||parseFloat(l.posX)/100||0),(l.posY in h||g.test(l.posY))&&(l.posY=h[l.posY]||parseFloat(l.posY)/100||0),(c.exec(d.backgroundImage)||[])[1]===j?v(function(){d.backgroundImage=f.backgroundImage}):f.backgroundImage=d.backgroundImage,l.src=(c.exec(e.backgroundImage)||[])[1],v(function(){d.backgroundImage="url("+j+")"}),l}function y(a){return String(a).replace(d,"").replace(e,"").replace(f," ")}function z(a,c){var d=c.next;function e(){p=b.setTimeout(function(){c.processing=!1,z(a,c)},0)}!c.processing&&d&&(c.next=null,c.processing=!0,u(c,d.src,function(b,f){d.imgWidth=b,d.imgHeight=f,A(c,d)?B(a,c,d,e):e()}))}function A(a,b){var c=a.current,d=!1,e;if(a.changed)a.changed=!1,d=!0;else for(e in b)if(b[e]!==c[e]){d=!0;break}return d}function B(a,c,d,e){var f=c.img,g=f.style,h=d.size,i=d.innerWidth,j=d.innerHeight,k=d.imgWidth,l=d.imgHeight,m=d.posX,n=d.posY,o="number"==typeof m,p="number"==typeof n,s="none",t=0,u=0,v="auto",w="auto",x="px",y="100%",z,A;i&&j&&k&&l&&(c.wrapper.style.position="fixed"===d.attachment&&c.canFixed?"fixed":"absolute",f.src=d.src,d.sizeIsKeyword?(z=i/j,A=k/l,"contain"===h&&A>z||"cover"===h&&z>A?(u=C((j-i/A)*n)+x,v=y):(t=C((i-j*A)*m)+x,w=y),g.left=o?t:m,g.top=p?u:n,g.width=v,g.height=w,s="block"):(g.display="block",g.width=d.sizeX,g.height=d.sizeY,k=f.width,l=f.height,k&&l&&(g.left=o?C((i-k)*m)+x:m,g.top=p?C((j-l)*n)+x:n,s="block"))),g.display=s,c.current=d,q=b.setTimeout(function(){r=b.setTimeout(e,0),updateEvent.fire()},0)}function C(a){var b=0>a;return a=Math.floor(Math.abs(a)),b?-a:a}function D(){var c=a.bgsExpando,d=(b.event||{}).propertyName,e="style.backgroundImage";c.ignore&&(c.ignore=!1,d===e)||(d===e&&a.style.backgroundImage&&(c.ignore=!0),w(a,c)&&(c.next=x(a,c),z(a,c)))}function E(){b.clearTimeout(n),n=b.setTimeout(D,m)}function F(){var c=a.bgsExpando,d,e,f;o={init:l,handlePropertychange:l,restore:l,handleResize:l},b.clearTimeout(n),b.clearTimeout(p),b.clearTimeout(q),b.clearTimeout(r);try{c&&(d=c.loadImg,d&&(d.onload=d.onerror=null,b.clearTimeout(d.callbackId)),e=a.style,f=c.restore,e&&(e.backgroundImage=f.backgroundImage,e.position=f.position,e.zIndex=f.zIndex),a.removeChild(c.wrapper)),a.bgsExpando=null}catch(g){}a=b=i=l=null}o={init:"print"!==i.media?s:l,handlePropertychange:l,restore:l,handleResize:l},"complete"===a.readyState&&o.init()}(element,window);</script>
<script type="text/vbscript"></script>
</PUBLIC:COMPONENT>
