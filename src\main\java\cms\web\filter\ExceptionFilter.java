package cms.web.filter;

import cms.handle.CustomException;
import cms.handle.ExceptionHandle;
import cms.utils.SpringUtils;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.Enumeration;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/24 10:59
 */
@Slf4j
public class ExceptionFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            chain.doFilter(request, response);
        } catch (CustomException e) {
            ExceptionHandle exceptionHandle = SpringUtils.getBean(ExceptionHandle.class);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().print(exceptionHandle.validate(e, Optional.empty()));
            response.flushBuffer();
            logError((HttpServletRequest) request, e);
        } catch (Throwable e) {
            logError((HttpServletRequest) request, e);
            throw e;
        } finally {

        }
    }

    public void logError(HttpServletRequest request, Throwable e) {
        log.error(String.format("head: %s ,form: %s ,json: %s", getAllHeads(request), getForm(request), getFormJson(request)), e);
    }

    public String getAllHeads(HttpServletRequest request) {
        StringBuffer heads = new StringBuffer();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            heads.append(headerName).append(":").append(headerValue);
        }
        return heads.toString();
    }

    public String getForm(HttpServletRequest request) {
        StringBuffer form = new StringBuffer("");
        request.getParameterMap().entrySet().forEach(e -> {
            form.append(e.getKey()).append(":").append(Optional.ofNullable(e.getValue()).filter(ar -> ar.length > 0).map(JSONUtil::toJsonStr).orElseGet(() -> Strings.EMPTY));
        });
        return form.toString();
    }

    public String getFormJson(HttpServletRequest request) {
        StringBuffer form = new StringBuffer("");

        try {
            BufferedReader reader = request.getReader();
            String line;
            while (null != (line = reader.readLine())) {
                form.append(line);
            }
        } catch (Exception e) {
            log.error("filter read error!", e);
        }

        return form.toString();
    }
}
