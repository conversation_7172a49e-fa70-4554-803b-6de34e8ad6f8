package cms.service.messageSource;

import cms.handle.CustomException;
import cms.handle.MsgException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

import static cms.constant.ErrorCode.DEFAULT_CODE;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/17 11:39
 */
@Service
@Slf4j
public class ErrorMessageService {
    private static final String DEFAULT_MSG = "not find error,system error！";

    @Resource
    private MessageSource messageSource;

    public String customExceptionMsg(CustomException e) {
        String msg = Optional.ofNullable(e.getCode()).map(String::valueOf).map(o -> getErrorCode(o)).map(this::getMessage).filter(o -> !DEFAULT_MSG.equals(o))
                .orElseGet(() -> Optional.ofNullable(e.getDefaultCode()).map(String::valueOf).map(o -> getErrorCode(o)).map(this::getMessage).orElseGet(() -> this.getMessage(DEFAULT_CODE)));
        return msg;
    }

    public String getMessage(int code) {
        String msg = Optional.ofNullable(code).map(String::valueOf).map(o -> getErrorCode(o)).map(this::getMessage).filter(o -> !DEFAULT_MSG.equals(o)).orElseGet(() -> this.getMessage(DEFAULT_CODE));
        return msg;
    }

    public String getMessage(int code, String... args) {
        String msg = Optional.ofNullable(code).map(String::valueOf).map(o -> getErrorCode(o)).map(o -> this.getMessage(o, args)).filter(o -> !DEFAULT_MSG.equals(o)).orElseGet(() -> this.getMessage(DEFAULT_CODE));
        return msg;
    }

    public String getMessage(int code, Object[] args) {
        String msg = Optional.ofNullable(code).map(String::valueOf).map(o -> getErrorCode(o)).map(o -> this.getMessage(o, args)).filter(o -> !DEFAULT_MSG.equals(o)).orElseGet(() -> this.getMessage(DEFAULT_CODE));
        return msg;
    }

    public MsgException getExcMessage(int code) {
        return new MsgException(this.getMessage(code));
    }

    public MsgException getExcMessage(int code, String... args) {
        return new MsgException(this.getMessage(code, args));
    }

    public Optional<String> getMessageNo500(int code) {
        return Optional.ofNullable(code).map(String::valueOf).map(o -> getErrorCode(o)).map(this::getMessage);
    }

    public Optional<String> getMessageNo500(int code, String[] args) {
        return Optional.ofNullable(code).map(String::valueOf).map(o -> getErrorCode(o)).map(o -> this.getMessage(o, args));
    }

    public String getMessage(String o) {
        try {
            return messageSource.getMessage(o, null, LocaleContextHolder.getLocale());
        } catch (Exception e) {
            log.error("getMessage String error!" + o, e);
            return DEFAULT_MSG;
        }
    }

    public String getMessage(String o, String[] args) {
        try {
            return messageSource.getMessage(o, args, LocaleContextHolder.getLocale());
        } catch (Exception e) {
            log.error("getMessage String error!" + o, e);
            return DEFAULT_MSG;
        }
    }

    public String getMessage(String o, Object[] args) {
        try {
            return messageSource.getMessage(o, args, LocaleContextHolder.getLocale());
        } catch (Exception e) {
            log.error("getMessage String error!" + o, e);
            return DEFAULT_MSG;
        }
    }

    public static String getErrorCode(String errorCode) {
        if ("500".equals(errorCode)) {
            return errorCode;
        }
        String result = Optional.of(errorCode).filter(o -> o.length() >= 2).map(o -> {
            char first = o.charAt(0);
            switch (first) {
                case 49:
                case 50:
                    return Optional.of(o).filter(s -> s.length() >= 1).map(s -> String.format("%s.%s.%s", s.charAt(0), s.substring(1, 5), s.substring(5))).orElse(errorCode);
                case 51:
                    return String.format("%s.%s", o.charAt(0), o.substring(1));
                default:
                    return "500";
            }
        }).orElse(errorCode);
        return result;
    }
}
