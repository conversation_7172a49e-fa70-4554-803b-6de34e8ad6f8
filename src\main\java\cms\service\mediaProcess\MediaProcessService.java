package cms.service.mediaProcess;

import cms.bean.mediaProcess.MediaProcessQueue;
import cms.bean.mediaProcess.MediaProcessSetting;
import cms.service.besa.DAO;

import java.util.List;

/**
 * 没有实现
 *
 * <AUTHOR>
 */
public interface MediaProcessService extends DAO<MediaProcessQueue> {
    /**
     * 没有实现
     *
     * @return
     */
	MediaProcessSetting findMediaProcessSetting();

    /**
     * 没有实现
     *
     * @return
     */
	MediaProcessSetting findMediaProcessSetting_cache();

    /**
     * 没有实现
     *
     * @param mediaProcessSetting
     * @return
     */
	void updateMediaProcessSetting(MediaProcessSetting mediaProcessSetting);

    /**
     * 没有实现
     *
     * @param mediaProcessQueueId 媒体处理队列Id
     */
	MediaProcessQueue findMediaProcessQueueById(Long mediaProcessQueueId);

    /**
     * 没有实现
     *
     * @param mediaProcessQueue 媒体处理队列
     */
	void saveMediaProcessQueueList(List<MediaProcessQueue> mediaProcessQueueList);

    /**
     * 没有实现
     *
     * @param mediaProcessQueueId 媒体处理Id
     * @param ip                  申请任务的客户端IP
     * @return
     */
	Integer updateMediaProcessQueue(Long mediaProcessQueueId, String ip);

    /**
     * 没有实现
     *
     * @param mediaProcessQueueId 媒体处理Id
     * @param processProgress     处理进度
     * @return
     */
	Integer updateMediaProcessQueue(Long mediaProcessQueueId, Double processProgress);

    /**
     * 没有实现
     *
     * @param mediaProcessQueueId 媒体处理Id
     * @param errorInfo           错误信息
     * @return
     */
	Integer addMediaErrorInfo(Long mediaProcessQueueId, String errorInfo);

    /**
     * 没有实现
     *
     * @param mediaProcessQueueId 媒体处理队列Id
     */
	Integer deleteMediaProcessQueue(Long mediaProcessQueueId);

    /**
     * 没有实现
     *
     * @param fileNameList 文件名称集合
     */
	Integer deleteMediaProcessQueue(List<String> fileNameList);

    /**
     * 没有实现
     *
     * @return
     */
	MediaProcessQueue findMediaProcessQueueByFileName(String fileName);

    /**
     * 没有实现
     *
     * @param firstIndex 索引开始,即从哪条记录开始
     * @param maxResult  获取多少条数据
     * @return
     */
	List<MediaProcessQueue> findPendingMedia(int firstIndex, int maxResult);

    /**
     * 没有实现
     *
     * @param firstIndex 索引开始,即从哪条记录开始
     * @param maxResult  获取多少条数据
     * @return
     */
	List<MediaProcessQueue> findPendingMedia_cache(int firstIndex, int maxResult);
}
