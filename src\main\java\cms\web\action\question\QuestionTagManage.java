package cms.web.action.question;

import cms.bean.question.QuestionTag;
import cms.service.question.QuestionService;
import cms.service.question.QuestionTagService;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static cms.constant.Constant.QUESTION_TAG_GRADE_ONE;

/**
 * 问题标签
 */
@Component("questionTagManage")
@Slf4j
public class QuestionTagManage {
    private AtomicInteger number = new AtomicInteger(new Random().nextInt(88888));//AtomicInteger 的最大值是2147483647，超过这个数字在递增的话就变成-2147483648
    private DateTime begin = new DateTime(2010, 01, 01, 01, 01, 01, 0);

    private static ConcurrentMap<Long, AtomicInteger> oneTagCount = Maps.newConcurrentMap();
    private static boolean isQueryData = false;

    @Autowired
    private QuestionTagService questionTagService;
    @Autowired
    private QuestionService questionService;

    /**
     * 取得下一个Id
     * 商品分类Id组成(2010年后的年月日时分秒+本机Id五位)
     */
    public Long nextNumber() {
        //这里是atoNum到MAX_VALUE=9999的时候重新设成0
        int MAX_VALUE = 99999;
        number.compareAndSet(MAX_VALUE, 0);
        DateTime end = new DateTime();
        //计算区间毫秒数
        Duration d = new Duration(begin, end);
        long second = d.getStandardSeconds();//秒

        return Long.parseLong(second + (String.format("%05d", number.incrementAndGet())));
    }


    /**
     * 标签放入标签中(递归)
     *
     * @param new_questionTag 新标签
     * @param questionTagList 未存入子标签集合
     * @return
     */
    public void childQuestionTag(QuestionTag new_questionTag, List<QuestionTag> questionTagList) {
        if (new_questionTag != null && new_questionTag.getChildNodeNumber() > 0) {
            for (QuestionTag questionTag : questionTagList) {
                if (questionTag.getParentId().equals(new_questionTag.getId())) {
                    new_questionTag.addChildTag(questionTag);
                    this.childQuestionTag(questionTag, questionTagList);

                }
            }
        }
    }

    /**
     * 问题标签排序(递归)
     *
     * @param questionTagList 已存入子标签集合
     * @return
     */
    public void questionTagSort(List<QuestionTag> questionTagList) {
        if (questionTagList != null && questionTagList.size() > 0) {
            this.questionTagItemSort(questionTagList);
            for (QuestionTag questionTag : questionTagList) {
                List<QuestionTag> childQuestionTag = questionTag.getChildTag();
                if (childQuestionTag != null && childQuestionTag.size() > 0) {
                    this.questionTagSort(childQuestionTag);
                }
            }
        }
    }

    /**
     * 问题标签项排序
     *
     * @param questionTagList
     */
    private void questionTagItemSort(List<QuestionTag> questionTagList) {
        Collections.sort(questionTagList, new Comparator<QuestionTag>() {
            @Override
            public int compare(QuestionTag o1, QuestionTag o2) {
                long s_1 = o1.getSort();
                long s_2 = o2.getSort();
                if (s_1 < s_2) {
                    return 1;

                } else {
                    if (s_1 == s_2) {
                        return 0;
                    } else {
                        return -1;
                    }
                }
            }
        });
    }

    public boolean isQueryData(boolean o) {
        isQueryData = o;
        return isQueryData;
    }

    public List<OneQuestionTagCount> getOneTagNameCount() {
        Map<Long, Integer> map = Optional.of(isQueryData).filter(o -> o).map(o -> questionService.findOneTagCount())
                .orElseGet(() -> oneTagCount.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, o -> o.getValue().get())));
        List<QuestionTag> questionTags = questionTagService.findAllQuestionTag_cache().stream().filter(o -> o.getGrade() == QUESTION_TAG_GRADE_ONE).sorted(Comparator.comparing(QuestionTag::getSort, Comparator.reverseOrder())).collect(Collectors.toList());
        return questionTags.stream().map(o -> new OneQuestionTagCount(o.getId(), o.getName(), Optional.ofNullable(map.get(o.getId())).orElse(0))).collect(Collectors.toList());
    }

    public static void initOneTagNumAdd(ConcurrentMap<Long, AtomicInteger> map) {
        oneTagCount = map;
        log.info("initTwoTagNumAdd \n{}", oneTagCount.entrySet().stream().map(o -> new StringBuffer(">>>>>>>> ").append(o.getKey()).append(" -> ").append(o.getValue()).append(" <<<<<<<<\n")).collect(Collectors.joining("")));
    }

    public static void addNewOneTagCount(QuestionTag tag) {
        Optional.ofNullable(tag).filter(o -> o.getGrade() == QUESTION_TAG_GRADE_ONE).ifPresent(o -> oneTagCount.putIfAbsent(o.getId(), new AtomicInteger(0)));
    }

    public static void incDecOneTagQuestionNum(List<Long> oldTagIds, List<Long> newTagIds) {
        Optional<List<Long>> oldIds = Optional.ofNullable(oldTagIds).map(l -> l.stream().distinct().collect(Collectors.toList())).filter(CollUtil::isNotEmpty);
        Optional<List<Long>> newIds = Optional.ofNullable(newTagIds).map(l -> l.stream().distinct().collect(Collectors.toList())).filter(CollUtil::isNotEmpty);
        oldIds.map(l -> newIds.map(ns -> l.stream().filter(o -> !ns.contains(o)).collect(Collectors.toList())).orElse(l)).filter(CollUtil::isNotEmpty).ifPresent(QuestionTagManage::decOneTagQuestionNum);
        newIds.map(l -> oldIds.map(os -> l.stream().filter(n -> !os.contains(n)).collect(Collectors.toList())).orElse(l)).filter(CollUtil::isNotEmpty).ifPresent(QuestionTagManage::incOneTagQuestionNum);
    }

    public static void incOneTagQuestionNum(List<Long> tagIds) {
        countOneTagQuestionNum(tagIds.stream().distinct().collect(Collectors.toList()), o -> o.incrementAndGet());
    }

    public static void decOneTagQuestionNum(List<Long> tagIds) {
        countOneTagQuestionNum(tagIds.stream().distinct().collect(Collectors.toList()), o -> {
            Optional.ofNullable(o.decrementAndGet()).filter(x -> x < 0).ifPresent(x -> isQueryData = true);
        });
    }

    private static void countOneTagQuestionNum(List<Long> tagIds, Consumer<AtomicInteger> consumer) {
        tagIds.stream().filter(Objects::nonNull).forEach(tagId -> {
            Optional.ofNullable(oneTagCount.get(tagId)).ifPresent(consumer::accept);
        });
    }

    @Data
    public static class OneQuestionTagCount {
        private Long id;
        private String name;
        private Integer count;

        public OneQuestionTagCount(Long id, String name, Integer count) {
            this.id = id;
            this.name = name;
            this.count = count;
        }
    }
}

