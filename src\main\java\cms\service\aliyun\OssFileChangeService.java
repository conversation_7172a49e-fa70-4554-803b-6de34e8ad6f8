package cms.service.aliyun;

import cms.bean.setting.EditorTag;
import cms.bean.staff.SysUsers;
import cms.bean.user.User;
import cms.constant.ErrorCode;
import cms.constant.FileConstant;
import cms.handle.MsgException;
import cms.service.fileCloud.FileCloudService;
import cms.service.messageSource.ErrorMessageService;
import cms.service.staff.StaffService;
import cms.service.user.UserService;
import cms.utils.FileUtil;
import cms.utils.JsonUtils;
import cms.utils.UUIDUtil;
import cms.web.action.TextFilterManage;
import cms.web.action.mediaProcess.MediaProcessQueueManage;
import cms.web.action.user.UserRoleManage;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.logging.log4j.util.Strings;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cms.constant.Constant.MEDIA_IMAGE_SUFFIX;
import static cms.constant.Constant.UPLOAD_MEDIA_IMAGE_DIR;
import static cms.constant.ErrorCode.*;
import static cms.constant.FileConstant.SIZE_SPLIT;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/5 9:24
 */
@Service
@Slf4j
public class OssFileChangeService {

    @Resource
    TextFilterManage textFilterManage;
    @Resource
    MediaProcessQueueManage mediaProcessQueueManage;
    @Resource
    StaffService staffService;
    @Resource
    UserService userService;
    @Resource
    UserRoleManage userRoleManage;
    @Autowired
    private FileCloudService fileCloudService;
    @Autowired
    private ErrorMessageService errorMessageService;

    public String upload(MultipartFile file, String type, String userName, Boolean isStaff, String business) {
        DateTime dateTime = new DateTime();
        String date = dateTime.toString("yyyy-MM-dd");
        return this.upload(null, file,
                Optional.ofNullable(FileConstant.getUploadTypeMap(type)), Optional.ofNullable(this.generateFileNumber(userName, isStaff)),
                business, Optional.ofNullable(type).filter(Strings::isNotEmpty).map(o -> new StringBuffer(date).append(OssService.separator).append(o).toString()));
    }

    public String upload(Consumer<ErrorMessageService> predicate, MultipartFile file,
                         Optional<List<String>> formatList,
                         String userName, Boolean isStaff, String business, Optional<String> pathZone) {
        return this.upload(predicate, file, formatList, Optional.ofNullable(this.generateFileNumber(userName, isStaff)), business, pathZone);
    }

    public String upload(Consumer<ErrorMessageService> predicate, MultipartFile file, Optional<EditorTag> editorTagOptional,
                         String type, Optional<String> numberOtl, String business, Optional<String> pathZone) {
        final Long size = file.getSize();
        try {
            EditorTag editorTag = editorTagOptional.orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0019));
            Optional.ofNullable(type).orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0016));
            return this.upload(errorMessageService -> {
                Optional.ofNullable(predicate).ifPresent(o -> o.accept(errorMessageService));
                // 权限不足
                Optional.ofNullable(FileConstant.getResourceEnum(type)).map(userRoleManage::isPermission).filter(o -> o).orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0002));
                // 是否能上传
                Optional.ofNullable(FileConstant.isUpload(editorTag, type)).filter(o -> o).orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0018));
                // 文件大小校验
                Long fileSize = FileConstant.getUploadSize(editorTag, type);
                Optional.ofNullable(fileSize).filter(o -> size / SIZE_SPLIT <= o).orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0013, String.valueOf(Optional.ofNullable(fileSize).orElse(0L))));
            }, file, Optional.ofNullable(FileConstant.getUploadTypeMap(editorTag, type)), numberOtl, business, pathZone);
        } catch (MsgException e) {
            Map<String, Object> returnJson = Maps.newHashMap();
            returnJson.put("error", 1);
            returnJson.put("message", e.getMsg());
            return JsonUtils.toJSONString(returnJson);
        }
    }

    public String upload(Consumer<ErrorMessageService> predicate, MultipartFile file,
                         Optional<List<String>> formatList, Optional<String> numberOtl,
                         String business, Optional<String> pathZone) {
        Map<String, Object> returnJson = Maps.newHashMap();
        String errorMessage = null;
        try {
            //0成功  1错误
            returnJson.put("error", 0);
            returnJson.put("url", uploadFile(predicate, file, Optional.empty(), formatList, numberOtl, business, pathZone));
            returnJson.put("title", file.getOriginalFilename());//旧文件名称
            return JsonUtils.toJSONString(returnJson);
        } catch (MsgException e) {
            errorMessage = e.getMsg();
        } catch (Exception e) {
            errorMessage = errorMessageService.getMessage(C_2_0001_0011);
            log.error(String.format("%s！%s %s %s %s ", errorMessage, JSON.toJSONString(formatList), numberOtl, business, pathZone), e);
        }
        returnJson.put("error", 1);
        returnJson.put("message", errorMessage);
        return JsonUtils.toJSONString(returnJson);
    }

    public String uploadFile(Consumer<ErrorMessageService> predicate, MultipartFile file, Optional<String> fileName,
                             Optional<List<String>> formatList, Optional<String> numberOtl,
                             String business, Optional<String> pathZone) throws Exception {
        Optional.ofNullable(predicate).ifPresent(o -> o.accept(errorMessageService));
        pathZone.filter(Strings::isNotEmpty).filter(o -> null != file).filter(o -> Strings.isNotEmpty(business)).orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0009));
        //当前文件名称
        String sourceFileName = fileName.orElse(file.getOriginalFilename());
        //取得文件后缀
        String suffix = FileUtil.getExtension(sourceFileName).toLowerCase();
        formatList.filter(o -> FileUtil.validateFileSuffix(sourceFileName, o)).orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0010));

        //文件保存目录;分多目录主要是为了分散图片目录,提高检索速度
        StringBuffer pathDir = this.getPathDir(business, pathZone);
        //构建文件名称
        String newFileName = fileName.orElseGet(() -> this.getNewFileName(suffix, numberOtl.orElse(null)));

        return fileCloudService.uploadByMp4Code264(file.getInputStream(), pathDir.append(newFileName).toString());
    }

    public String uploadFile(InputStream is, Optional<String> fileName,
                             Optional<String> suffix, Optional<String> number,
                             String business, Optional<String> pathZone) {
        StringBuffer pathDir = this.getPathDir(business, pathZone);
        //构建文件名称
        String newFileName = fileName.orElseGet(() -> this.getNewFileName(suffix.orElse(null), number.orElse(null)));
        try {
            return fileCloudService.uploadByMp4Code264(is, pathDir.append(newFileName).toString());
        } catch (Exception e) {
            return null;
        }
    }

    public StringBuffer getPathDir(String business, Optional<String> pathZone) {
        pathZone.filter(Strings::isNotEmpty).filter(o -> Strings.isNotEmpty(business)).orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0009));
        return OssFileChangeService.getPathDirStatic(business, pathZone.get());
    }

    public static StringBuffer getPathDirStatic(String business, String pathZone) {
        return new StringBuffer("file").append(OssService.separator).append(business).append(OssService.separator).append(pathZone).append(OssService.separator);
    }

    public static String getBusiness(String[] pathArray) {
        // 对应getPathDirStatic的顺序
        return pathArray[1];
    }

    public static String getPathZone(String[] pathArray) {
        // 对应getPathDirStatic的顺序
        return Optional.ofNullable(pathArray.length).filter(i -> i > 2).map(i -> IntStream.range(2, i).mapToObj(o -> pathArray[o]).collect(Collectors.joining(OssService.separator))).orElse("");
    }

    public String getNewFileName(String suffix, String number) {
        Optional.ofNullable(suffix).filter(Strings::isNotEmpty).filter(o -> Strings.isNotEmpty(number)).orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0009));
        return new StringBuffer(UUIDUtil.getUUID32()).append(number).append(".").append(suffix).toString();
    }

    public String getOssUrl() {
        return new StringBuffer(fileCloudService.getUploadStartPath()).append(fileCloudService.getTitlePath()).toString();
    }

    public void fileChange(String old_content, String readPathItem) {
        this.fileChange(old_content, readPathItem, Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList());
    }

    public void fileChange(String old_content, String readPathItem,
                           List<String> finalImageNameList, List<String> finalFlashNameList,
                           List<String> finalMediaNameList, List<String> finalFileNameList) {
        Object[] obj = textFilterManage.readPathName(old_content, readPathItem);
        if (obj != null && obj.length > 0) {
            List<String> oldPathFileList = Lists.newArrayList();//旧路径文件
            //删除旧媒体处理任务
            List<String> delete_mediaProcessFileNameList = Lists.newArrayList();//文件名称

            //旧图片
            Optional.ofNullable((List<String>) obj[0]).filter(CollectionUtil::isNotEmpty).map(l -> {
                l.removeAll(finalImageNameList);
                return l;
            }).filter(CollectionUtil::isNotEmpty).ifPresent(oldPathFileList::addAll);

            //旧Flash
            Optional.ofNullable((List<String>) obj[1]).filter(CollectionUtil::isNotEmpty).map(l -> {
                l.removeAll(finalFlashNameList);
                return l;
            }).filter(CollectionUtil::isNotEmpty).ifPresent(oldPathFileList::addAll);

            //旧影音
            Optional.ofNullable((List<String>) obj[2]).filter(CollectionUtil::isNotEmpty).map(l -> {
                l.removeAll(finalMediaNameList);
                return l;
            }).filter(CollectionUtil::isNotEmpty).ifPresent(l -> {
                l.stream().map(o -> FileUtil.getFullPath(o) + UPLOAD_MEDIA_IMAGE_DIR + OssService.separator + FileUtil.getName(o)).forEach(oldPathFileList::add);
                oldPathFileList.addAll(l);
                l.stream().map(FileUtil::getName).forEach(delete_mediaProcessFileNameList::add);
            });

            //旧文件
            Optional.ofNullable((List<String>) obj[3]).filter(CollectionUtil::isNotEmpty).map(l -> {
                l.removeAll(finalFileNameList);
                return l;
            }).filter(CollectionUtil::isNotEmpty).ifPresent(oldPathFileList::addAll);

            //删除旧媒体处理任务
            Optional.of(delete_mediaProcessFileNameList).filter(CollectionUtil::isNotEmpty).ifPresent(l -> l.stream().forEach(mediaProcessQueueManage::delete_cache_findMediaProcessQueueByFileName));

            //删除旧路径文件
            delOldFile(oldPathFileList);
        }
    }

    public void ossImgFileChange(String old_content, String readPathItem,
                                 List<String> finalImageNameList) {
        List<String> old_ImageName = textFilterManage.readImageName(old_content, readPathItem);
        Optional.ofNullable(old_ImageName).filter(CollectionUtil::isNotEmpty).map(l -> {
            l.removeAll(finalImageNameList);
            return l;
        }).filter(CollectionUtil::isNotEmpty).ifPresent(this::delOldFile);
    }

    public void delOldFile(List<String> oldFilePath) {
        fileCloudService.del(oldFilePath.stream().map(o -> o.replace(fileCloudService.getUploadStartPath(), "")).collect(Collectors.toList()));
    }

    public void delFileNoTile(List<String> oldFilePath) {
        fileCloudService.del(oldFilePath.stream().map(o -> fileCloudService.getTitlePath() + o).collect(Collectors.toList()));
    }

    /**
     * 生成上传文件编号
     *
     * @param userName 用户名称
     * @param isStaff  是否是员工   true:员工   false:会员
     * @return
     */
    private String generateFileNumber(String userName, Boolean isStaff) {
        String number = "";

        if (isStaff != null && userName != null && !"".equals(userName.trim())) {
            if (isStaff == true) {//员工

                SysUsers sysUsers = staffService.findByUserAccount(userName.trim());
                if (sysUsers != null) {
                    number = "a" + sysUsers.getUserId();
                }
            } else {//会员
                User user = userService.findUserByUserName(userName.trim());
                if (user != null) {
                    number = "b" + user.getId();
                }
            }
        }
        return number;
    }

    public String initMediaImage(String mediaUrl) {
        Map<String, Object> returnJson = Maps.newHashMap();
        String message = null;
        try {
            String urlTitle = this.getOssUrl();
            String path = Optional.ofNullable(mediaUrl).map(o -> o.replaceAll("\\\\", OssService.separator)).filter(o -> o.startsWith(urlTitle)).map(o -> o.replace(urlTitle, "")).orElseThrow(() -> new MsgException(errorMessageService.getMessage(C_2_0001_0024)));
            String[] pathArray = FileUtil.getFullPath(path).split(OssService.separator);
            String fileName = FileUtil.getBaseName(mediaUrl) + "." + MEDIA_IMAGE_SUFFIX;
            String business = OssFileChangeService.getBusiness(pathArray);
            String pathDir = Optional.ofNullable(OssFileChangeService.getPathZone(pathArray)).map(o -> o + OssService.separator + UPLOAD_MEDIA_IMAGE_DIR).filter(Strings::isNotEmpty).orElseThrow(() -> new MsgException(errorMessageService.getMessage(C_2_0001_0024)));
            ByteArrayInputStream is = new ByteArrayInputStream(fileCloudService.getMediaImage(fileCloudService.getTitlePath() + path));
            returnJson.put("error", 0);
            returnJson.put("url", this.uploadFile(is, Optional.of(fileName), Optional.of(MEDIA_IMAGE_SUFFIX), Optional.empty(), business, Optional.of(pathDir)));
            return JsonUtils.toJSONString(returnJson);
        } catch (MsgException e) {
            log.error("uploadMediaImage error!", e);
            message = e.getMsg();
        } catch (Exception e) {
            log.error("uploadMediaImage exception!", e);
            message = errorMessageService.getMessage(C_2_0001_0500);
        }
        returnJson.put("error", 1);
        returnJson.put("message", message);
        return JsonUtils.toJSONString(returnJson);
    }
}
