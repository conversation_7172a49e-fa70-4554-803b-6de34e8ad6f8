package cms.bean.thirdParty;

import java.io.Serializable;

/**
 * 微信openid
 *
 * <AUTHOR>
 */
public class WeiXinOpenId implements Serializable {
    private static final long serialVersionUID = 9152688025746476435L;

    /**
     * 用户的唯一标识 每个用户对每个公众号的OpenID是唯一的。对于不同公众号，同一用户的openid不同
     **/
    private String openId = "";

    /**
     * 错误代码
     **/
    private String errorCode = "";
    /**
     * 错误信息
     **/
    private String errorMessage = "";

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }


}
