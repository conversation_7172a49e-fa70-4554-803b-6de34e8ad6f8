package cms.web.action.filterWord;

import cms.utils.PathUtil;
import cms.web.action.TextFilterManage;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.util.Strings;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 敏感词过滤管理
 */
@Component("sensitiveWordFilterManage")
public class SensitiveWordFilterManage {
    private static final Logger logger = LogManager.getLogger(SensitiveWordFilterManage.class);
    private static Words words = new Words();
    private static AtomicReference<Words> w = new AtomicReference<Words>();
    //上次执行版本
    private String version = "";

    @Autowired
    private TextFilterManage textFilterManage;

    /**
     * @param strSource 用户输入的字符串
     * @param strFrom   需要替换的字符
     * @param strTo     需要替换的字符替换为该字符串
     * @return
     * @describe:可以替换特殊字符的替换方法,replaceAll只能替换普通字符串,含有特殊字符的不能替换
     */
    private static String replace(String strSource, String strFrom, String strTo) {
        if (strSource == null) {
            return null;
        }
        int i = 0;
        if ((i = strSource.indexOf(strFrom, i)) >= 0) {
            char[] cSrc = strSource.toCharArray();
            char[] cTo = strTo.toCharArray();
            int len = strFrom.length();
            StringBuffer buf = new StringBuffer(cSrc.length);
            buf.append(cSrc, 0, i).append(cTo);
            i += len;
            int j = i;
            while ((i = strSource.indexOf(strFrom, i)) > 0) {
                buf.append(cSrc, j, i - j).append(cTo);
                i += len;
                j = i;
            }
            buf.append(cSrc, j, cSrc.length - j);
            return buf.toString();
        }
        return strSource;
    }

    /**
     * 返回过滤后的字符
     *
     * @param content     输入内容
     * @param replaceChar 敏感词替换字符
     * @return
     */
    public String filterSensitiveWord1(String content, String replaceChar) {
        List<String> findedWords = this.getFindedAllSensitive(content);
        if (findedWords != null && findedWords.size() > 0) {
            for (String str : findedWords) {
                content = StringUtils.replaceIgnoreCase(content, str, replaceChar);//忽略大小写替换； String.replaceAll不能直接替换特殊字符，例如：$/
            }
        }
        return content;
    }

    public String filterSensitiveWord(String content, String replaceChar) {
        String text = textFilterManage.filterText(content);
        Document doc = Jsoup.parseBodyFragment(content);
        Elements elements = doc.body().getAllElements().stream().filter(o -> CollUtil.isEmpty(o.children())).filter(o -> Strings.isNotEmpty(o.text())).collect(Collectors.toCollection(() -> new Elements()));
        List<String> findedWords = this.getFindedAllSensitive(text.toLowerCase());
        if (findedWords != null && findedWords.size() > 0) {
            for (String str : findedWords) {
                elements.stream().forEach(o -> o.text(StringUtils.replaceIgnoreCase(o.text(), str, replaceChar)));
            }
        }
        textFilterManage.processEnd(doc);
        doc.outputSettings().prettyPrint(false);
        return doc.body().html();
    }

    /**
     * 是否包含敏感词
     *
     * @param text 文本
     * @return 是否包含
     */
    public boolean containsSensitive(String text) {
        return words.isMatch(text);
    }

    /**
     * 查找敏感词，返回找到的第一个敏感词
     *
     * @param text 文本
     * @return 敏感词
     */
    public String getFindedFirstSensitive(String text) {
        return words.match(text);
    }

    /**
     * 查找敏感词，返回找到的所有敏感词
     *
     * @param text 文本
     * @return 敏感词
     */
    public List<String> getFindedAllSensitive(String text) {
        return words.matchAll(text);
    }

    /**
     * 查找敏感词，返回找到的所有敏感词<br>
     * 密集匹配原则：假如关键词有 ab,b，文本是abab，将匹配 [ab,b,ab]<br>
     * 贪婪匹配（最长匹配）原则：假如关键字a,ab，最长匹配将匹配[a, ab]
     *
     * @param text           文本
     * @param isDensityMatch 是否使用密集匹配原则
     * @param isGreedMatch   是否使用贪婪匹配（最长匹配）原则
     * @return 敏感词
     */
    public List<String> getFindedAllSensitive(String text, boolean isDensityMatch, boolean isGreedMatch) {
        return words.matchAll(text, -1, isDensityMatch, isGreedMatch);
    }

    /**
     * 判断是否更新  true:已更新  false:没有更新
     *
     * @return
     */
    public boolean isUpdate() {
        String path = "WEB-INF" + File.separator + "data" + File.separator + "filterWord" + File.separator + "word.txt";

        String new_version = "";

        //读取文件
        File file = new File(PathUtil.defaultExternalDirectory() + File.separator + path);
        if (file.exists()) {
            new_version = file.length() + "-" + file.lastModified();
        }
        if (!version.equals(new_version)) {

            version = new_version;
            return true;
        }
        return false;

    }

    /**
     * 读取数据并且更新到词库模型
     */
   /* @Scheduled(fixedDelay = 10000)//10秒
    public void updateData() {
        try {
            //是否有更新
            if (this.isUpdate() == true) {
                //清空词库
                this.clearWord();
                String path = PathUtil.defaultExternalDirectory() + File.separator + "WEB-INF" + File.separator + "data" + File.separator + "filterWord" + File.separator;
                File file = new File(path + "word.txt");
                if (file.exists()) {
                    List<String> wordList = FileUtil.readLines(file, "utf-8");
                    if (wordList != null && wordList.size() > 0) {
                        for (String word : wordList) {
                            if (word != null && !"".equals(word.trim())) {
                                words.addWord(word.toLowerCase().trim());
                            }
                        }
                    }

                }
            }
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            if (logger.isErrorEnabled()) {
                logger.error("读取敏感词数据并且更新到词库模型", e);
            }
        }
    }*/
    private void opWords(Consumer<Words> consumer) {
        while (true) {
            Words old = w.get();
            Words n = BeanUtil.copyProperties(old, Words.class);
            consumer.accept(n);
            if (w.compareAndSet(old, n)) {
                words = w.get();
                break;
            }
        }
    }

    public void addWords(Collection<String> keyWords) {
        List<String> ad = keyWords.stream().map(String::toLowerCase).collect(Collectors.toList());
        this.opWords(o -> o.addWords(ad));
    }

    public void delWord(String word) {
        String dw = word.toLowerCase();
        this.opWords(o -> o.removeWord(dw));
    }

    public void initWord(Collection<String> keyWords) {
        List<String> ad = keyWords.stream().filter(Strings::isNotEmpty).map(String::toLowerCase).collect(Collectors.toList());
        this.opWords(o -> {
            o.clear();
            o.addWords(ad);
        });
    }

    public void clearWord() {
        this.opWords(o -> {
            o.clear();
        });
    }

    public List<String> getAllKeywords() {
        Words words = w.get();
        List<String> result = getAllKeywords(new StringBuffer(""), words);
        return result;
    }

    private List<String> getAllKeywords(StringBuffer title, Words words) {
        List<String> result = Lists.newArrayList();
        for (Character character : words.keySet()) {
            StringBuffer titleF = new StringBuffer(title).append(character);
            words.get(character).getEndCharacterSet().stream().map(new StringBuffer(titleF)::append).map(StringBuffer::toString).forEach(result::add);
            Optional.ofNullable(words.get(character)).filter(o -> !o.isEmpty()).map(o -> getAllKeywords(titleF, o)).filter(CollectionUtil::isNotEmpty).ifPresent(result::addAll);
        }
        return result;
    }
}
