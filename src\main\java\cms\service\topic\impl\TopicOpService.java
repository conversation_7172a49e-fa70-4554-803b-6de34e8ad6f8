package cms.service.topic.impl;

import cms.bean.redEnvelope.GiveRedEnvelope;
import cms.bean.topic.Tag;
import cms.bean.topic.Topic;
import cms.bean.topic.TopicIndex;
import cms.bean.topic.TopicTagAssociation;
import cms.bean.user.User;
import cms.service.aliyun.OssFileChangeService;
import cms.service.topic.TagService;
import cms.service.topic.TopicIndexService;
import cms.service.topic.TopicService;
import cms.service.user.UserService;
import cms.web.action.topic.TopicManage;
import cms.web.action.user.UserManage;
import cn.hutool.core.collection.CollUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static cms.constant.Constant.TOPIC_TAG_GRADE_TWO;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/25 16:59
 */
@Service
public class TopicOpService {

    @Autowired
    private TopicService topicService;
    @Resource
    private UserManage userManage;
    @Resource
    private UserService userService;
    @Resource
    private TopicIndexService topicIndexService;
    @Autowired
    private TopicTagAssociationService topicTagAssociationService;
    @Resource
    TagService tagService;
    @Resource
    private TopicManage topicManage;
    @Autowired
    private OssFileChangeService ossFileChangeService;

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void delete(List<Long> topicIds) {
        List<Topic> topicList = topicService.findByIdList(topicIds);
        if (topicList != null && topicList.size() > 0) {
            Map<Long, Tag> tagMap = tagService.findAllTag_cache().stream().collect(Collectors.toMap(o -> o.getId(), o -> o));
            for (Topic topic : topicList) {
                if (topic.getStatus() < 100) {//标记删除
                    if (null == TopicFormService.topicUpdateLock.putIfAbsent(topic.getId(), Boolean.FALSE)) {
                        try {
                            Optional<List<Long>> tagIds = Optional.ofNullable(topic.getStatus()).filter(o -> o.equals(20)).map(x -> topicTagAssociationService.findByTopicId(topic.getId()).stream().map(TopicTagAssociation::getTagId).map(tagMap::get).filter(Objects::nonNull).filter(o -> o.getGrade() == TOPIC_TAG_GRADE_TWO).map(Tag::getId).collect(Collectors.toList())).filter(CollUtil::isNotEmpty);
                            int i = topicService.markDelete(topic.getId());

                            User user = userManage.query_cache_findUserByUserName(topic.getUserName());
                            if (i > 0 && user != null) {
                                //修改话题状态
                                userService.softDeleteUserDynamicByTopicId(user.getId(), topic.getUserName(), topic.getId());
                            }
                            //更新索引
                            topicIndexService.addTopicIndex(new TopicIndex(String.valueOf(topic.getId()), 2));
                            topicManage.deleteTopicCache(topic.getId());//删除缓存
                            topicManage.delete_cache_postNumByUserName(topic.getUserName());
                            Optional.ofNullable(topic).filter(o -> null != o.getEssence() && o.getEssence()).map(Topic::getStatus).filter(o -> o.equals(20)).ifPresent(o -> topicManage.delete_essenceTop10_cache());
                            tagIds.ifPresent(l -> {
                                TopicManage.decTwoTagTopicNum(l);
                                l.stream().map(tagMap::get).map(Tag::getName).forEach(topicManage::delete_cache_twoTagNum);
                            });
                        } finally {
                            TopicFormService.topicUpdateLock.remove(topic.getId());
                        }
                    }
                } else {//物理删除
                    GiveRedEnvelope giveRedEnvelope = null;
                    String userName = null;
                    BigDecimal amount = null;
                    Object paymentLogObject = null;

                    String fileNumber = topicManage.generateFileNumber(topic.getUserName(), topic.getIsStaff());

                    int i = topicService.deleteTopic(topic.getId(), giveRedEnvelope, userName, amount, paymentLogObject);

                    if (i > 0) {
                        //根据话题Id删除用户动态(话题下的评论和回复也同时删除)
                        userService.deleteUserDynamicByTopicId(topic.getId());
                    }

                    topicManage.deleteTopicCache(topic.getId());//删除缓存

                    topicManage.delete_cache_markUpdateTopicStatus(topic.getId());//删除 标记修改话题状态

                    //更新索引
                    topicIndexService.addTopicIndex(new TopicIndex(String.valueOf(topic.getId()), 3));
                    ossFileChangeService.fileChange(topic.getContent(), "topic");
                }
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void unDelete(List<Long> topicIds) {
        List<Topic> topicList = topicService.findByIdList(topicIds);
        if (topicList != null && topicList.size() > 0) {
            int i = topicService.reductionTopic(topicList);
            Map<Long, Tag> tagMap = tagService.findAllTag_cache().stream().collect(Collectors.toMap(o -> o.getId(), o -> o));
            for (Topic topic : topicList) {
                Optional<List<Long>> tagIds = Optional.ofNullable(topic.getStatus()).filter(o -> o.equals(120)).map(x -> topicTagAssociationService.findByTopicId(topic.getId()).stream().map(tagMap::get).filter(Objects::nonNull).filter(o -> o.getGrade() == TOPIC_TAG_GRADE_TWO).map(Tag::getId).collect(Collectors.toList())).filter(CollUtil::isNotEmpty);
                User user = userManage.query_cache_findUserByUserName(topic.getUserName());
                if (i > 0 && user != null) {
                    //修改话题状态
                    userService.reductionUserDynamicByTopicId(user.getId(), topic.getUserName(), topic.getId());
                }

                //更新索引
                topicIndexService.addTopicIndex(new TopicIndex(String.valueOf(topic.getId()), 2));
                topicManage.delete_cache_postNumByUserName(topic.getUserName());
                topicManage.deleteTopicCache(topic.getId());//删除缓存
                Optional.ofNullable(topic).filter(o -> null != o.getEssence() && o.getEssence()).map(Topic::getStatus).filter(o -> o.equals(120)).ifPresent(o -> topicManage.delete_essenceTop10_cache());
                tagIds.ifPresent(l -> {
                    TopicManage.incTwoTagTopicNum(l);
                    l.stream().map(tagMap::get).map(Tag::getName).forEach(topicManage::delete_cache_twoTagNum);
                });
            }
        }
    }
}
