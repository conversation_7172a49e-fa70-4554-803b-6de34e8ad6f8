解决文件上传自动填充文件名
7580行urlBox.val(url);下面加入titleBox.val(data.title);

----------------------------------------------------------------------------------

kindeditor.js文件修改以支持<base href="${config:url()}"></base>标签显示图片
233行加入:
function _getPageBasePath() {   
    var els = document.getElementsByTagName('base');   
    for (var i = 0, len = els.length; i < len; i++) {   
        var href = els[i].href;   
        if (href) {   
            return href;   
        }   
    }   
    return '';   
}   
K.pageBasePath = _getPageBasePath();  

3613行加入:(K.pageBasePath === '' ? '' : '<base href="' + K.pageBasePath + '">'),

----------------------------------------------------------------------------------

kindeditor不支持输入emoji表情结尾,必须加空格结尾才可以完整提交

----------------------------------------------------------------------------------

1566行修改为y = (parseInt(box.top) < 0 ? 0 : box.top)  + pos.y;
解决粘贴内容时自动滚动到顶部

----------------------------------------------------------------------------------

3665行增加'.ke-content .prettyprint {',
		'	background:#f8f8f8;',
		'	border:1px solid #ddd;',
		'	padding:5px;',
		'}',
		
		
突出编辑框的代码

----------------------------------------------------------------------------------

隐藏功能右键标签
kindeditor.js
6246行增加 editHide : '隐藏标签属性',
		  deleteHide : '删除隐藏标签',


5871行增加
self.plugin.getSelectedHide = function() {
		return self.cmd.commonAncestor('hide');
	};
	//隐藏可见 右键
	_each('hide'.split(','), function(i, name) {
		
		var uName = name.charAt(0).toUpperCase() + name.substr(1);

		_each('edit,delete'.split(','), function(j, val) {
			console.log("getSelected" + uName);
			self.addContextmenu({
				title : self.lang(val + uName),
				click : function() {
					self.loadPlugin(name, function() {
						
						self.plugin[name][val]();
						self.hideMenu();
					});
				},
				cond : self.plugin['getSelected' + uName],
				width : 150,
				iconClass : val == 'edit' ? 'ke-icon-' + name : undefined
			});
		});
		self.addContextmenu({ title : '-' });
	});








----------------------------------------------------------------------------------

4798行增加
//处理回车跳不出标签
		if(tagName == 'hide'){
			var hide = self.cmd.commonAncestor('hide');
			hide.remove(true);
		}
		//处理回车跳不出标签
		if(tagName == 'pre'){
			var pre = self.cmd.commonAncestor('pre');
			pre.remove(true);
		}




---------------------------------------------
3618行增加
//处理IE8以下CSS不支持自定义标签问题
'<!--[if (IE 6)|(IE 7)|(IE 8)]>',
		'<script type="text/javascript">',
		'(function() {',
		'var a = ["hide"];',
		'for (var i = 0, j = a.length; i < j; i++) {',
		'document.createElement(a[i]);',
		'}',
		'})();',
		'</script>',
		'<![endif]-->',

---------------------------------------------
4496行修改
if(tab.title != ''){
			var li = K('<li class="ke-tabs-li">' + tab.title + '</li>');
			li.data('tab', tab);
			liList.push(li);
			ul.append(li);
			
		}else{//如果标题为空，则不使用样式
			var li = K('<li class="">' + tab.title + '</li>');
			li.data('tab', tab);
			liList.push(li);
			ul.append(li);
		}

-----------------------------------------------		
4790行修改 解决图片上传按钮错位
'<span class="ke-button-common">',
修改为
'<span class="ke-button-common  ke-button-outer">',	

7428行		
var uploadbutton = K.uploadbutton({
			button : K('.ke-upload-button', div)[0],
			fieldName : filePostName,
			form : K('.ke-form', div),
			target : target,
			width: 60,	
				
将width: 60,改为width: 70,			
			
			
		
		
		