homeManageAction.deleteFavoriteByQuestionId.questionId=Question ID
homeManageAction.deleteFavoriteByTopicId.topicId=Topic ID
homeManageAction.deleteFollowByUserName.userName=Username
homeManageAction.deleteLikeByTopicId.topicId=Topic ID
questionTag.name=Question tag name
questionTag.sort=Sequence
searchAction.execute.sortFiled=Sorting field
searchAction.execute.sortWay=Sorting method
sysUsers.repeatPassword=Duplicate password
sysUsers.userAccount=Account
sysUsers.userPassword=Password
tag.name=Tag name
tag.sort=Sequence
user.account=Account
user.allowUserDynamic=Display User Trends
user.email=Email address
user.nickname=Nickname
user.password=Password
user.point=Points
user.state=User state
user.validateCode=Verification code
userLogin.account=Account
userLogin.password=Password
validateMsg.ContainsObject=must be {1}.
validateMsg.Length=cannot be greater than {1} characters.
validateMsg.Max=cannot be greater than {1}.
validateMsg.Min=must be a number greater than or equal to {1}.
validateMsg.NotEmpty=cannot be empty.
validateMsg.NotNull=cannot be empty.
validateMsg.PatternString=format is incorrect.
validateMsg.typeMismatch=format is incorrect.
