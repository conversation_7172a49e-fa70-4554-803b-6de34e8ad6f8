package cms.web.action.common;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import static cms.constant.Constant.MARK_UPDATE_STATUS_QUESTION;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/9/7 9:41
 */
@Component
public class CommonManage {

    /**
     * 查询缓存 标记修改话题状态
     *
     * @param key          Id
     * @param randomNumber 随机数
     * @return
     */
    @Cacheable(value = "commonManage_cache_markUpdateStatus", key = "#key")
    public Integer query_cache_markUpdateStatus(String key, Integer randomNumber) {
        return randomNumber;
    }

    /**
     * 删除缓存 标记修改话题状态
     *
     * @param key Id
     * @return
     */
    @CacheEvict(value = "commonManage_cache_markUpdateStatus", key = "#key")
    public void delete_cache_markUpdateStatus(String key) {
    }
}
