package cms.web.action.topic;

import cms.bean.*;
import cms.bean.setting.SystemSetting;
import cms.bean.staff.SysUsers;
import cms.bean.topic.*;
import cms.bean.user.User;
import cms.bean.user.UserGrade;
import cms.service.aliyun.OssFileChangeService;
import cms.service.setting.SettingService;
import cms.service.topic.CommentService;
import cms.service.topic.TagService;
import cms.service.topic.TopicIndexService;
import cms.service.topic.TopicService;
import cms.service.topic.impl.TopicManageService;
import cms.service.topic.impl.TopicOpService;
import cms.service.topic.impl.TopicTagAssociationService;
import cms.service.user.UserGradeService;
import cms.service.user.UserService;
import cms.utils.IpAddress;
import cms.utils.JsonUtils;
import cms.utils.SecureLink;
import cms.web.action.TextFilterManage;
import cms.web.action.fileSystem.FileManage;
import cms.web.action.user.UserManage;
import cms.web.action.user.UserRoleManage;
import cms.web.taglib.Configuration;
import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

import static cms.constant.Constant.TOPIC_TAG_GRADE_TWO;


/**
 * 话题管理
 */
@Controller
@RequestMapping("/control/topic/manage")
public class TopicManageAction {

    @Resource
    TopicService topicService;
    @Autowired
    private TopicTagAssociationService topicTagAssociationService;
    @Autowired
    private TopicOpService topicOpService;
    @Resource
    TextFilterManage textFilterManage;
    @Resource
    SettingService settingService;
    @Resource
    TagService tagService;
    @Resource
    CommentManage commentManage;
    @Resource
    CommentService commentService;
    @Resource
    TopicManage topicManage;
    @Resource
    FileManage fileManage;
    @Resource
    TopicIndexService topicIndexService;

    @Resource
    UserGradeService userGradeService;

    @Resource
    UserManage userManage;
    @Resource
    UserService userService;
    @Resource
    UserRoleManage userRoleManage;

    @Autowired
    private TopicManageService topicManageService;
    @Autowired
    private OssFileChangeService ossFileChangeService;

    /**
     * 话题   查看
     *
     * @param topicId
     * @param model
     * @param page
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(params = "method=view", method = RequestMethod.GET)
    public String view(Long topicId, Long commentId, ModelMap model, Integer page,
                       HttpServletRequest request, HttpServletResponse response) throws Exception {

        Map<String, String> error = new HashMap<String, String>();
        Map<String, Object> returnValue = new LinkedHashMap<String, Object>();


        if (topicId != null && topicId > 0L) {
            Topic topic = topicService.findById(topicId);
            if (topic != null) {
                if (topic.getIp() != null && !"".equals(topic.getIp().trim())) {
                    topic.setIpAddress(IpAddress.queryAddress(topic.getIp().trim()));
                }
                if (topic.getContent() != null && !"".equals(topic.getContent().trim())) {
                    //处理富文本路径
                    topic.setContent(fileManage.processRichTextFilePath(topic.getContent(), "topic"));
                }

                SystemSetting systemSetting = settingService.findSystemSetting_cache();
                if (topic.getContent() != null && !"".equals(topic.getContent().trim()) && systemSetting.getFileSecureLinkSecret() != null && !"".equals(systemSetting.getFileSecureLinkSecret().trim())) {
                    List<String> serverAddressList = fileManage.fileServerAllAddress(request);
                    //解析上传的文件完整路径名称
                    Map<String, String> analysisFullFileNameMap = textFilterManage.analysisFullFileName(topic.getContent(), "topic", serverAddressList);
                    if (analysisFullFileNameMap != null && analysisFullFileNameMap.size() > 0) {


                        Map<String, String> newFullFileNameMap = new HashMap<String, String>();//新的完整路径名称 key: 完整路径名称 value: 重定向接口
                        for (Map.Entry<String, String> entry : analysisFullFileNameMap.entrySet()) {

                            newFullFileNameMap.put(entry.getKey(), Configuration.getUrl(request) + SecureLink.createDownloadRedirectLink(entry.getKey(), entry.getValue(), -1L, systemSetting.getFileSecureLinkSecret()));
                        }

                        topic.setContent(textFilterManage.processFullFileName(topic.getContent(), "topic", newFullFileNameMap, serverAddressList));

                    }


                }
                if (topic.getContent() != null && !"".equals(topic.getContent().trim())) {
                    //处理视频播放器标签
//                    String content = textFilterManage.processVideoPlayer(Configuration.getUrl(request), topic.getContent(), -1L, systemSetting.getFileSecureLinkSecret());
//                    topic.setContent(content);
                }

                if (topic.getIsStaff() == false) {//会员
                    User user = userManage.query_cache_findUserByUserName(topic.getUserName());
                    if (user != null) {
                        topic.setAccount(user.getAccount());
                        topic.setNickname(user.getNickname());
                        topic.setAvatarPath(user.getAvatarPath(fileManage.fileOssAddress()));
                        topic.setAvatarName(user.getAvatarName());

                        List<String> userRoleNameList = userRoleManage.queryUserRoleName(user.getUserName());
                        if (userRoleNameList != null && userRoleNameList.size() > 0) {
                            topic.setUserRoleNameList(userRoleNameList);//用户角色名称集合
                        }
                    }

                } else {
                    topic.setAccount(topic.getUserName());//员工用户名和账号是同一个

                }

                List<String> topic_roleNameList = userRoleManage.queryAllowViewTopicRoleName(topic.getTagId());
                if (topic_roleNameList != null && topic_roleNameList.size() > 0) {
                    topic.setAllowRoleViewList(topic_roleNameList);
                }

                Map<Long, Tag> tagMap = tagService.findAllTag_cache().stream().collect(Collectors.toMap(Tag::getId, o -> o));
                Optional.ofNullable(topic.getAssociations()).ifPresent(l -> l.stream().forEach(o -> o.setGrade(Optional.ofNullable(tagMap.get(o.getTagId())).map(Tag::getGrade).orElse(0))));

                returnValue.put("topic", topic);

                returnValue.put("availableTag", commentManage.availableTag());

                PageForm pageForm = new PageForm();
                pageForm.setPage(page);

                if (commentId != null && commentId > 0L && page == null) {
                    Long row = commentService.findRowByCommentId(commentId, topicId);
                    if (row != null && row > 0L) {

                        Integer _page = Integer.parseInt(String.valueOf(row)) / settingService.findSystemSetting_cache().getBackstagePageNumber();
                        if (Integer.parseInt(String.valueOf(row)) % settingService.findSystemSetting_cache().getBackstagePageNumber() > 0) {//余数大于0要加1
                            _page = _page + 1;
                        }
                        pageForm.setPage(_page);
                    }
                }


                //评论
                StringBuffer jpql = new StringBuffer();
                //存放参数值
                List<Object> params = new ArrayList<Object>();
                PageView<Comment> pageView = new PageView<Comment>(settingService.findSystemSetting_cache().getBackstagePageNumber(), pageForm.getPage(), 10);


                //当前页
                int firstindex = (pageForm.getPage() - 1) * pageView.getMaxresult();
                //排序
                LinkedHashMap<String, String> orderby = new LinkedHashMap<String, String>();

                if (topicId != null && topicId > 0L) {
                    jpql.append(" o.topicId=?" + (params.size() + 1));//所属父类的ID;(params.size()+1)是为了和下面的条件参数兼容
                    params.add(topicId);//设置o.parentId=?2参数
                }

                orderby.put("postTime", "asc");//根据sort字段降序排序
                QueryResult<Comment> qr = commentService.getScrollData(Comment.class, firstindex, pageView.getMaxresult(), jpql.toString(), params.toArray(), orderby);


                List<Long> commentIdList = new ArrayList<Long>();
                List<Comment> commentList = qr.getResultlist();

                //引用修改Id集合
                List<Long> quoteUpdateIdList = new ArrayList<Long>();
                //重新查询Id
                List<Long> query_quoteUpdateIdList = new ArrayList<Long>();
                //新引用集合
                Map<Long, String> new_quoteList = new HashMap<Long, String>();//key:自定义评论Id value:自定义评论内容(文本)

                if (commentList != null && commentList.size() > 0) {
                    for (Comment comment : commentList) {
                        if (comment.getContent() != null && !"".equals(comment.getContent().trim())) {
                            //处理富文本路径
                            comment.setContent(fileManage.processRichTextFilePath(comment.getContent(), "comment"));
                        }

                        if (comment.getQuoteUpdateId() != null && comment.getQuoteUpdateId().length() > 1) {
                            String[] quoteUpdateId_arr = comment.getQuoteUpdateId().split(",");
                            if (quoteUpdateId_arr != null && quoteUpdateId_arr.length > 0) {
                                for (String quoteUpdateId : quoteUpdateId_arr) {
                                    if (quoteUpdateId != null && !"".equals(quoteUpdateId.trim())) {
                                        Long l = Long.parseLong(quoteUpdateId);
                                        if (!quoteUpdateIdList.contains(l)) {
                                            quoteUpdateIdList.add(l);
                                        }
                                    }
                                }
                            }
                        }


                        if (comment.getIp() != null && !"".equals(comment.getIp().trim())) {
                            comment.setIpAddress(IpAddress.queryAddress(comment.getIp()));
                        }
                    }

                    A:
                    for (Long quoteUpdateId : quoteUpdateIdList) {
                        for (Comment comment : commentList) {
                            if (comment.getId().equals(quoteUpdateId)) {
                                new_quoteList.put(comment.getId(), textFilterManage.filterText(textFilterManage.specifyHtmlTagToText(comment.getContent())));
                                continue A;
                            }
                        }
                        query_quoteUpdateIdList.add(quoteUpdateId);
                    }
                }


                if (query_quoteUpdateIdList != null && query_quoteUpdateIdList.size() > 0) {
                    List<Comment> quote_commentList = commentService.findByCommentIdList(query_quoteUpdateIdList);
                    if (quote_commentList != null && quote_commentList.size() > 0) {
                        for (Comment comment : quote_commentList) {
                            new_quoteList.put(comment.getId(), textFilterManage.filterText(textFilterManage.specifyHtmlTagToText(comment.getContent())));
                        }
                    }
                }

                Map<String, List<String>> userRoleNameMap = new HashMap<String, List<String>>();//用户角色名称 key:用户名称Id 角色名称集合
                if (commentList != null && commentList.size() > 0) {
                    for (Comment comment : commentList) {
                        if (comment.getIsStaff() == false) {//会员
                            User user = userManage.query_cache_findUserByUserName(comment.getUserName());
                            if (user != null) {
                                comment.setAccount(user.getAccount());
                                comment.setNickname(user.getNickname());
                                comment.setAvatarPath(user.getAvatarPath(fileManage.fileOssAddress()));
                                comment.setAvatarName(user.getAvatarName());
                                userRoleNameMap.put(comment.getUserName(), null);
                            }

                        } else {
                            comment.setAccount(comment.getUserName());//员工用户名和账号是同一个

                        }
                        commentIdList.add(comment.getId());
                        if (comment.getQuote() != null && !"".equals(comment.getQuote().trim())) {
                            //旧引用
                            List<Quote> quoteList = JsonUtils.toGenericObject(comment.getQuote(), new TypeReference<List<Quote>>() {
                            });
                            if (quoteList != null && quoteList.size() > 0) {
                                for (Quote quote : quoteList) {
                                    if (new_quoteList.containsKey(quote.getCommentId())) {
                                        quote.setContent(new_quoteList.get(quote.getCommentId()));
                                    }

                                    if (quote.getIsStaff() == false) {//会员
                                        User user = userManage.query_cache_findUserByUserName(quote.getUserName());
                                        if (user != null) {
                                            quote.setAccount(user.getAccount());
                                            quote.setNickname(user.getNickname());
                                            quote.setAvatarPath(user.getAvatarPath(fileManage.fileOssAddress()));
                                            quote.setAvatarName(user.getAvatarName());
                                            userRoleNameMap.put(quote.getUserName(), null);
                                        }

                                    } else {
                                        quote.setAccount(quote.getUserName());//员工用户名和账号是同一个

                                    }


                                }
                            }
                            comment.setQuoteList(quoteList);
                        }


                    }
                }


                if (commentIdList != null && commentIdList.size() > 0) {
                    List<Reply> replyList = commentService.findReplyByCommentId(commentIdList);
                    if (replyList != null && replyList.size() > 0) {
                        for (Comment comment : commentList) {
                            for (Reply reply : replyList) {
                                if (reply.getIsStaff() == false) {//会员
                                    User user = userManage.query_cache_findUserByUserName(reply.getUserName());
                                    if (user != null) {
                                        reply.setAccount(user.getAccount());
                                        reply.setNickname(user.getNickname());
                                        reply.setAvatarPath(user.getAvatarPath(fileManage.fileOssAddress()));
                                        reply.setAvatarName(user.getAvatarName());
                                        userRoleNameMap.put(reply.getUserName(), null);
                                    }

                                } else {
                                    reply.setAccount(reply.getUserName());//员工用户名和账号是同一个

                                }

                                if (comment.getId().equals(reply.getCommentId())) {
                                    comment.addReply(reply);
                                }
                            }

                        }
                    }
                }

                if (userRoleNameMap != null && userRoleNameMap.size() > 0) {
                    for (Map.Entry<String, List<String>> entry : userRoleNameMap.entrySet()) {
                        List<String> roleNameList = userRoleManage.queryUserRoleName(entry.getKey());
                        entry.setValue(roleNameList);
                    }
                }

                if (commentList != null && commentList.size() > 0) {
                    for (Comment comment : commentList) {
                        //用户角色名称集合
                        for (Map.Entry<String, List<String>> entry : userRoleNameMap.entrySet()) {
                            if (entry.getKey().equals(comment.getUserName())) {
                                List<String> roleNameList = entry.getValue();
                                if (roleNameList != null && roleNameList.size() > 0) {
                                    comment.setUserRoleNameList(roleNameList);
                                }
                                break;
                            }
                        }
                        if (comment.getReplyList() != null && comment.getReplyList().size() > 0) {
                            for (Reply reply : comment.getReplyList()) {
                                for (Map.Entry<String, List<String>> entry : userRoleNameMap.entrySet()) {
                                    if (entry.getKey().equals(reply.getUserName())) {
                                        List<String> roleNameList = entry.getValue();
                                        if (roleNameList != null && roleNameList.size() > 0) {
                                            reply.setUserRoleNameList(roleNameList);
                                        }
                                        break;
                                    }
                                }
                            }
                        }
                        if (comment.getQuoteList() != null && comment.getQuoteList().size() > 0) {
                            for (Quote quote : comment.getQuoteList()) {
                                //用户角色名称集合
                                for (Map.Entry<String, List<String>> entry : userRoleNameMap.entrySet()) {
                                    if (entry.getKey().equals(quote.getUserName())) {
                                        List<String> roleNameList = entry.getValue();
                                        if (roleNameList != null && roleNameList.size() > 0) {
                                            quote.setUserRoleNameList(roleNameList);
                                        }
                                        break;
                                    }
                                }


                            }

                        }
                    }
                }

                //将查询结果集传给分页List
                pageView.setQueryResult(qr);
                returnValue.put("pageView", pageView);


                String username = "";//用户名称

                Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
                if (obj instanceof UserDetails) {
                    username = ((UserDetails) obj).getUsername();
                }
                returnValue.put("userName", username);


                return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, returnValue));
            } else {
                error.put("topicId", "话题不存在");
            }
        } else {
            error.put("topicId", "话题Id参数不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    /**
     * 话题   添加界面显示
     */
    @ResponseBody
    @RequestMapping(params = "method=add", method = RequestMethod.GET)
    public String addUI(Topic topic, ModelMap model,
                        HttpServletRequest request, HttpServletResponse response) throws Exception {

        Map<String, Object> returnValue = new HashMap<String, Object>();

        String username = "";//用户名称

        Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (obj instanceof UserDetails) {
            username = ((UserDetails) obj).getUsername();
        }
        returnValue.put("userName", username);
        List<UserGrade> userGradeList = userGradeService.findAllGrade();
        returnValue.put("userGradeList", userGradeList);

        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, returnValue));
    }

    /**
     * 话题  添加
     */
    @ResponseBody
    @RequestMapping(params = "method=add", method = RequestMethod.POST)
    public String add(ModelMap model, Long[] tagId, String tagName, String title, Boolean allow, Integer status, Boolean essence,
                      String content, String sort,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {
        return topicManageService.add(model, tagId, tagName, title, allow, status, essence, content, sort, request, response);
    }


    /**
     * 文件上传
     * <p>
     * 员工发话题 上传文件名为UUID + a + 员工Id
     * 用户发话题 上传文件名为UUID + b + 用户Id
     *
     * @param model
     * @param dir      上传类型，分别为image、flash、media、file
     * @param userName 用户名称
     * @param isStaff  是否是员工   true:员工   false:会员
     * @param fileName 文件名称 预签名时有值
     * @param response
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(params = "method=upload", method = RequestMethod.POST)
    public String upload(ModelMap model, String dir, String userName, Boolean isStaff, String fileName,
                         MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        return ossFileChangeService.upload(file, dir, userName, isStaff, "topic");
    }

    /**
     * 话题   修改界面显示
     */
    @ResponseBody
    @RequestMapping(params = "method=edit", method = RequestMethod.GET)
    public String editUI(ModelMap model, Long topicId,
                         HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, String> error = new HashMap<String, String>();
        Map<String, Object> returnValue = new HashMap<String, Object>();

        if (topicId != null && topicId > 0L) {
            Topic topic = topicService.findById(topicId);
            if (topic != null) {
                if (topic.getContent() != null && !"".equals(topic.getContent().trim())) {
                    //处理富文本路径
                    topic.setContent(fileManage.processRichTextFilePath(topic.getContent(), "topic"));
                }

                Map<Long, Tag> tagMap = tagService.findAllTag_cache().stream().collect(Collectors.toMap(Tag::getId, o -> o));
                Optional.ofNullable(topic.getAssociations()).ifPresent(l -> l.stream().forEach(o -> {
                    Optional<Tag> t = Optional.ofNullable(tagMap.get(o.getTagId()));
                    o.setTagName(t.map(Tag::getName).orElse(""));
                    o.setGrade(t.map(Tag::getGrade).orElse(0));
                }));

                returnValue.put("topic", topic);

                List<UserGrade> userGradeList = userGradeService.findAllGrade();
                returnValue.put("userGradeList", userGradeList);

                return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, returnValue));
            } else {
                error.put("topicId", "话题不存在");
            }


        } else {
            error.put("topicId", "话题Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    /**
     * 话题   修改
     *
     * @param model
     * @param tagId
     * @param title
     * @param content
     * @param sort
     */
    @ResponseBody
    @RequestMapping(params = "method=edit", method = RequestMethod.POST)
    public String edit(ModelMap model, Long topicId, Long[] tagId,
                       String tagName, String title, Boolean allow, Integer status, Boolean essence,
                       String content, String sort,
                       HttpServletRequest request, HttpServletResponse response) throws Exception {
        return topicManageService.edit(model, topicId, tagId, tagName, title, allow, status, essence, content, sort, request, response);
    }

    /**
     * 话题   删除
     *
     * @param model
     * @param topicId  话题Id集合
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(params = "method=delete", method = RequestMethod.POST)
    @ResponseBody//方式来做ajax,直接返回字符串
    public String delete(ModelMap model, Long[] topicId,
                         HttpServletRequest request, HttpServletResponse response) throws Exception {
        //错误
        Map<String, String> error = new HashMap<String, String>();

        String username = "";//用户名称
        String userId = "";//用户Id
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof SysUsers) {
            userId = ((SysUsers) principal).getUserId();
            username = ((SysUsers) principal).getUserAccount();
        }
        if (topicId != null && topicId.length > 0) {
            List<Long> topicIdList = new ArrayList<Long>();
            for (Long l : topicId) {
                if (l != null && l > 0L) {
                    topicIdList.add(l);
                }
            }
            if (topicIdList != null && topicIdList.size() > 0) {
                topicOpService.delete(topicIdList);
                return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
            } else {
                error.put("topicId", "话题Id不能为空");
            }
        } else {
            error.put("topicId", "话题Id不存在");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    /**
     * 还原
     *
     * @param model
     * @param topicId 话题Id集合
     * @return
     * @throws Exception
     */
    @RequestMapping(params = "method=reduction", method = RequestMethod.POST)
    @ResponseBody//方式来做ajax,直接返回字符串
    public String reduction(ModelMap model, Long[] topicId,
                            HttpServletResponse response) throws Exception {
        //错误
        Map<String, String> error = new HashMap<String, String>();

        if (topicId != null && topicId.length > 0) {
            topicOpService.unDelete(Arrays.asList(topicId));
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        } else {
            error.put("topic", "话题Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    /**
     * 审核话题
     *
     * @param model
     * @param topicId 话题Id
     * @return
     * @throws Exception
     */
    @RequestMapping(params = "method=auditTopic", method = RequestMethod.POST)
    @ResponseBody//方式来做ajax,直接返回字符串
    public String auditTopic(ModelMap model, Long topicId,
                             HttpServletResponse response) throws Exception {
        //错误
        Map<String, String> error = new HashMap<String, String>();
        if (topicId != null && topicId > 0L) {
            int i = topicService.updateTopicStatus(topicId, 20);
            Map<Long, Tag> tagMap = tagService.findAllTag_cache().stream().collect(Collectors.toMap(o -> o.getId(), o -> o));
            Optional<List<Long>> tagIds = Optional.ofNullable(topicTagAssociationService.findByTopicId(topicId).stream().map(tagMap::get).filter(Objects::nonNull).filter(o -> o.getGrade() == TOPIC_TAG_GRADE_TWO).map(Tag::getId).distinct().collect(Collectors.toList())).filter(CollUtil::isNotEmpty);
            Topic topic = topicManage.queryTopicCache(topicId);
            if (i > 0 && topic != null) {
                User user = userManage.query_cache_findUserByUserName(topic.getUserName());
                if (user != null) {
                    //修改话题状态
                    userService.updateUserDynamicTopicStatus(user.getId(), topic.getUserName(), topic.getId(), 20);
                }
            }

            //更新索引
            topicIndexService.addTopicIndex(new TopicIndex(String.valueOf(topicId), 2));
            topicManage.deleteTopicCache(topicId);//删除缓存
            tagIds.ifPresent(l -> {
                TopicManage.incTwoTagTopicNum(l);
                l.stream().map(tagMap::get).map(Tag::getName).forEach(topicManage::delete_cache_twoTagNum);
            });
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        } else {
            error.put("topicId", "话题Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }


    @RequestMapping(params = "method=essence", method = RequestMethod.POST)
    @ResponseBody//方式来做ajax,直接返回字符串
    public String essenceTopic(ModelMap model, Long topicId, boolean essence,
                               HttpServletResponse response) throws Exception {
        topicService.updateEssence(topicId, essence);
        topicManage.deleteTopicCache(topicId);//删除缓存
        topicManage.delete_essenceTop10_cache();
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
    }
}
