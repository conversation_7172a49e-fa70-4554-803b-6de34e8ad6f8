package cms.web.action.topic;


import cms.bean.RequestResult;
import cms.bean.ResultCode;
import cms.bean.topic.Tag;
import cms.constant.Constant;
import cms.constant.ErrorCode;
import cms.handle.CustomException;
import cms.service.topic.TagService;
import cms.utils.JsonUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.ui.ModelMap;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.Validator;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 标签
 */
@RestController
@RequestMapping("/control/tag/manage")
public class TagManageAction {

    @Resource
    TagService tagService;
    @Resource
    TagManage tagManage;
    @Autowired
    private TopicManage topicManage;
    @Resource
    MessageSource messageSource;
    @Resource(name = "tagValidator")
    private Validator validator;

    /**
     * 标签   添加界面显示
     */
    @ResponseBody
    @RequestMapping(params = "method=add", method = RequestMethod.GET)
    public String addUI(Tag tag, ModelMap model,
                        HttpServletRequest request, HttpServletResponse response) throws Exception {

        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
    }

    /**
     * 标签  添加
     */
    @RequestMapping(params = "method=add", method = RequestMethod.POST)
    @ResponseBody
    public String add(ModelMap model, Tag formbean, BindingResult result) throws Exception {
        //错误
        Map<String, Object> error = new HashMap<String, Object>();

        //数据校验
        this.validator.validate(formbean, result);
        if (result.hasErrors()) {
            List<FieldError> fieldErrorList = result.getFieldErrors();
            if (fieldErrorList != null && fieldErrorList.size() > 0) {
                for (FieldError fieldError : fieldErrorList) {
                    error.put(fieldError.getField(), messageSource.getMessage(fieldError, null));
                }
            }
        }
        if (error.size() == 0) {
            Tag tag = BeanUtil.copyProperties(formbean, Tag.class);
            tag.setId(tagManage.nextNumber());
            tag.setParentId(Optional.ofNullable(formbean.getParentId()).orElse(0L));
            tag.setGrade(Optional.ofNullable(formbean.getParentId()).map(tagService::findById).map(Tag::getGrade).map(o -> o + 1).orElse(1));
            if (tag.getGrade() > Constant.MAX_GRADE) {
                throw new CustomException(ErrorCode.C_1_0002_0001, "parentId");
            } else if (CollectionUtil.isNotEmpty(tagService.findTagByParentIdAndName(tag.getParentId(), tag.getName()))) {
                throw new CustomException(ErrorCode.C_1_0002_0003, "name");
            }

            tagService.saveTag(tag);
            TopicManage.addNewTwoTagName(tag);
        }

        if (error.size() > 0) {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
        } else {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
    }


    /**
     * 标签   修改界面显示
     */
    @ResponseBody
    @RequestMapping(params = "method=edit", method = RequestMethod.GET)
    public String editUI(ModelMap model, Long tagId,
                         HttpServletRequest request, HttpServletResponse response) throws Exception {
        //错误
        Map<String, Object> error = new HashMap<String, Object>();
        if (tagId != null) {//判断ID是否存在;
            Tag tag = tagService.findById(tagId);
            if (tag != null) {
                return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, tag));
            } else {
                error.put("tag", "标签不存在");
            }
        } else {
            error.put("tag", "标签Id不能为空");
        }

        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));

    }

    /**
     * 标签   修改
     */
    @ResponseBody
    @RequestMapping(params = "method=edit", method = RequestMethod.POST)
    public String edit(ModelMap model, @Validated Tag formbean
            , Long tagId,
                       HttpServletRequest request, HttpServletResponse response) throws Exception {
        //错误
        Map<String, Object> error = new HashMap<String, Object>();

        Tag tag = null;
        if (tagId != null && tagId > 0L) {
            //取得对象
            tag = tagService.findById(tagId);
            if (tag == null) {
                error.put("tag", "标签不存在");
            }
        } else {
            error.put("tag", "标签Id不能为空");
        }

        if (Optional.ofNullable(tagService.findTagByParentIdAndName(tag.getParentId(), tag.getName())).map(l -> l.stream().filter(o -> !tagId.equals(o.getId())).count()).orElse(0L) > 0) {
            throw new CustomException(ErrorCode.C_1_0002_0003, "name");
        }

        //数据校验
        if (error.size() == 0) {
            Tag new_tag = BeanUtil.copyProperties(formbean, Tag.class);
            new_tag.setId(tagId);
            Tag oldTag = tagService.findById(tagId);
            if (null == tagService.updateTag(new_tag)) {
                error.put("tag", "不能多人同时修改同一个标签");
            }
            Optional.ofNullable(oldTag).map(Tag::getName).ifPresent(topicManage::delete_cache_twoTagNum);
            Optional.ofNullable(new_tag).map(Tag::getName).ifPresent(topicManage::delete_cache_twoTagNum);
        }


        if (error.size() > 0) {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
        } else {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
    }

    /**
     * 标签   删除
     *
     * @param model
     * @param tagId    标签Id
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(params = "method=delete", method = RequestMethod.POST)
    @ResponseBody//方式来做ajax,直接返回字符串
    public String delete(ModelMap model, Long tagId,
                         HttpServletRequest request, HttpServletResponse response) throws Exception {
        //错误
        Map<String, String> error = new HashMap<String, String>();
        if (tagId != null && tagId > 0L) {
            Tag tag = tagService.findById(tagId);
            Integer i = tagService.deleteTag(tagId);
            if (null == i) {
                error.put("tag", "不能多人同时修改同一个标签");
            } else if (i > 0) {
                return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
            }
            Optional.ofNullable(tag).map(Tag::getName).ifPresent(topicManage::delete_cache_twoTagNum);
        } else {
            error.put("tagId", " 标签Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }


    /**
     * 标签 查询所有标签
     */
    @ResponseBody
    @RequestMapping(params = "method=allTag", method = RequestMethod.GET)
    public String queryAllTag(ModelMap model,
                              HttpServletRequest request, HttpServletResponse response) throws Exception {

        List<Tag> tagList = tagService.findAllTag();

        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, tagList));
    }


    /**
     * 标签 传销一级标签
     */
    @ResponseBody
    @RequestMapping(params = "method=oneTag", method = RequestMethod.GET)
    public String queryTagByOne(ModelMap model,
                                HttpServletRequest request, HttpServletResponse response) throws Exception {

        List<Tag> allTagList = tagService.findAllTag_cache();
        List<Tag> tagList = allTagList.stream().filter(o -> o.getGrade() == 1).collect(Collectors.toList());
        Map<Long, List<Tag>> tagByParent = allTagList.stream().filter(o -> o.getGrade() == 2).collect(Collectors.groupingBy(Tag::getParentId));
        tagList.forEach(o -> o.setChindrenNums(Optional.ofNullable(tagByParent.get(o.getId())).map(x -> x.size()).orElse(0)));

        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, tagList));
    }


    /**
     * 标签 查询二级标签
     */
    @ResponseBody
    @RequestMapping(params = "method=twoTag", method = RequestMethod.GET)
    public String queryTagByTwo(ModelMap model,
                                HttpServletRequest request, HttpServletResponse response) throws Exception {

        List<Tag> tagList = tagService.findAllTag_cache().stream().filter(o -> o.getGrade() == 2).collect(Collectors.toList());

        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, tagList));
    }


    /**
     * 标签 根据上级Id查询标签
     */
    @ResponseBody
    @RequestMapping(params = "method=tagByParent", method = RequestMethod.GET)
    public String queryAllParent(ModelMap model, Long tagId,
                                 HttpServletRequest request, HttpServletResponse response) throws Exception {

        List<Tag> tagList = tagService.findTagByParentId(tagId);

        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, tagList));
    }

}
