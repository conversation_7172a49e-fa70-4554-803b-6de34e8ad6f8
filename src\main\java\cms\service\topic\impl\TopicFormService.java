package cms.service.topic.impl;

import cms.bean.BaseUserDel;
import cms.bean.ErrorView;
import cms.bean.api.TopicApi;
import cms.bean.message.EmailRemind;
import cms.bean.setting.EditorTag;
import cms.bean.setting.SystemSetting;
import cms.bean.topic.*;
import cms.bean.user.*;
import cms.constant.ErrorCode;
import cms.constant.ErrorHashMap;
import cms.handle.CustomException;
import cms.service.aliyun.OssFileChangeService;
import cms.service.message.impl.EmailRemindService;
import cms.service.messageSource.ErrorMessageService;
import cms.service.operation.OperationService;
import cms.service.operation.OperationTopicService;
import cms.service.setting.SettingService;
import cms.service.template.TemplateService;
import cms.service.topic.TagService;
import cms.service.topic.TopicIndexService;
import cms.service.topic.TopicService;
import cms.service.user.UserGradeService;
import cms.service.user.UserService;
import cms.utils.Base64;
import cms.utils.*;
import cms.utils.threadLocal.AccessUserThreadLocal;
import cms.web.action.AccessSourceDeviceManage;
import cms.web.action.CSRFTokenManage;
import cms.web.action.SystemException;
import cms.web.action.TextFilterManage;
import cms.web.action.filterWord.SensitiveWordFilterManage;
import cms.web.action.follow.FollowManage;
import cms.web.action.setting.SettingManage;
import cms.web.action.topic.TopicManage;
import cms.web.action.user.PointManage;
import cms.web.action.user.UserDynamicManage;
import cms.web.action.user.UserManage;
import cms.web.action.user.UserRoleManage;
import cms.web.taglib.Configuration;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

import static cms.bean.ErrorView._119;
import static cms.constant.Constant.*;
import static cms.constant.ErrorCode.*;
import static cms.constant.enums.RemindTypeEnums.IDOL_TOPIC;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/4 10:43
 */
@Service
@Slf4j
public class TopicFormService {
    private static final int SUMMARY_LENGTH = 180;

    public static ConcurrentMap<Long, Boolean> topicUpdateLock = Maps.newConcurrentMap();

    @Resource
    UserRoleManage userRoleManage;
    @Resource
    AccessSourceDeviceManage accessSourceDeviceManage;
    @Resource
    CSRFTokenManage csrfTokenManage;
    @Resource
    SettingManage settingManage;
    @Resource
    TextFilterManage textFilterManage;
    @Resource
    UserManage userManage;
    @Resource
    SensitiveWordFilterManage sensitiveWordFilterManage;
    @Resource
    FollowManage followManage;
    @Resource
    PointManage pointManage;
    @Resource
    TopicManage topicManage;
    @Resource
    TemplateService templateService;
    @Resource
    SettingService settingService;
    @Resource
    UserService userService;
    @Resource
    UserDynamicManage userDynamicManage;
    @Resource
    TagService tagService;
    @Resource
    UserGradeService userGradeService;
    @Resource
    TopicService topicService;
    @Resource
    TopicIndexService topicIndexService;
    @Autowired
    private TopicTagAssociationService topicTagAssociationService;
    @Autowired
    private OperationService operationService;
    @Autowired
    private OperationTopicService operationTopicService;
    @Autowired
    private ErrorMessageService errorMessageService;
    @Autowired
    private OssFileChangeService ossFileChangeService;

    @Transactional(propagation = Propagation.REQUIRED)
    public String add(ModelMap model, Long tagId, Long[] twoTagIdArray, String title, String content,
                      String token, String captchaKey, String captchaValue, String jumpUrl,
                      Integer sort,
                      RedirectAttributes redirectAttrs,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<Long> twoTagId = Arrays.stream(twoTagIdArray).distinct().collect(Collectors.toList());
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据

        if (tagId != null) {

            //是否有当前功能操作权限
            boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1002000, tagId);
            if (flag_permission == false) {
                if (isAjax == true) {
                    response.setStatus(403);//设置状态码

                    WebUtil.writeToWeb("", "json", response);
                    return null;
                } else {
                    String dirName = templateService.findTemplateDir_cache();

                    String accessPath = accessSourceDeviceManage.accessDevices(request);
                    request.setAttribute("message", errorMessageService.getMessage(ErrorCode.C_2_0001_0002));
                    return "/templates/" + dirName + "/" + accessPath + "/message";
                }
            }
        }

        ErrorHashMap<String> error = new ErrorHashMap<String>();
        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getCloseSite().equals(2)) {
            error.put("topic", ErrorCode.C_2_0001_0001);//只读模式不允许提交数据
        }

        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token, error);

        //验证码
        //如果全局不允许提交话题
        if (systemSetting.isAllowTopic() == false) {
            error.put("topic", ErrorView._110.name());//不允许提交话题
        }

        //如果实名用户才允许提交话题
        if (systemSetting.isRealNameUserAllowTopic() == true) {
            User _user = userManage.query_cache_findUserByUserName(accessUser.getUserName());
            if (_user.isRealNameAuthentication() == false) {
                error.put("topic", ErrorView._109.name());//实名用户才允许提交话题
            }
        }

        User user = userService.findUserByUserName(accessUser.getUserName());//查询用户数据

        //图片地址
        List<ImageInfo> beforeImageList = new ArrayList<ImageInfo>();

        Topic topic = new Topic();
        Date d = new Date();
        topic.setPostTime(d);
        topic.setLastUpdateTime(d);
        topic.setSort(Optional.ofNullable(sort).orElse(0));
        topic.setLastReplyTime(d);

        List<String> imageNameList = null;
        boolean isImage = false;//是否含有图片
        List<String> flashNameList = null;
        boolean isFlash = false;//是否含有Flash
        List<String> mediaNameList = null;
        boolean isMedia = false;//是否含有音视频
        List<String> fileNameList = null;
        boolean isFile = false;//是否含有文件
        boolean isMap = false;//是否含有地图

        //前台发表话题审核状态
        if (systemSetting.getTopic_review().equals(10)) {//10.全部审核 20.特权会员未触发敏感词免审核(未实现) 30.特权会员免审核 40.触发敏感词需审核(未实现) 50.无需审核
            topic.setStatus(10);//10.待审核
        } else if (systemSetting.getTopic_review().equals(30)) {
            if (tagId != null && tagId > 0L) {
                //是否有当前功能操作权限
                boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1006000, tagId);
                if (flag_permission) {
                    topic.setStatus(20);//20.已发布
                } else {
                    topic.setStatus(10);//10.待审核
                }
            }
        } else {
            topic.setStatus(TOPIC_STATUS_RELEASE);//20.已发布
        }

        Optional<Map<Long, Tag>> tagMap = Optional.ofNullable(tagService.findAllTag_cache()).filter(CollUtil::isNotEmpty).map(l -> l.stream().collect(Collectors.toMap(Tag::getId, o -> o)));
        if (tagId == null || tagId <= 0L) {
            error.put("tagId", ErrorCode.C_2_0007_0043);
        } else {
            tagMap.map(m -> m.get(tagId)).filter(o -> o.getGrade() == TOPIC_TAG_GRADE_ONE).ifPresent(o -> {
                topic.setTagId(o.getId());
                topic.setTagName(o.getName());
            });
            if (topic.getTagId() == null) {
                error.put("tagId", C_1_0003_0003);
            }
        }
        if (title != null && !"".equals(title.trim())) {
            if (systemSetting.isAllowFilterWord()) {
                String wordReplace = "";
                if (systemSetting.getFilterWordReplace() != null) {
                    wordReplace = systemSetting.getFilterWordReplace();
                }
                title = sensitiveWordFilterManage.filterSensitiveWord(title, wordReplace);
            }

            topic.setTitle(title);
            if (title.length() > 150) {
                error.put("title", C_2_0007_0044);
            }
        } else {
            error.put("title", ErrorCode.C_2_0007_0045);
        }


        if (content != null && !"".equals(content.trim())) {
            EditorTag editorTag = settingManage.readTopicEditorTag();
            //过滤标签
            content = textFilterManage.filterTag(request, content, editorTag);
            Object[] object = textFilterManage.filterHtml(request, content, "topic", editorTag);
            String value = (String) object[0];
            imageNameList = (List<String>) object[1];
            isImage = (Boolean) object[2];//是否含有图片
            flashNameList = (List<String>) object[3];
            isFlash = (Boolean) object[4];//是否含有Flash
            mediaNameList = (List<String>) object[5];
            isMedia = (Boolean) object[6];//是否含有音视频
            fileNameList = (List<String>) object[7];
            isFile = (Boolean) object[8];//是否含有文件
            isMap = (Boolean) object[9];//是否含有地图

            List<UserGrade> userGradeList = userGradeService.findAllGrade_cache();
            //校正隐藏标签
            String validValue = textFilterManage.correctionHiddenTag(value, userGradeList);

            //允许使用的隐藏标签
            List<Integer> allowHiddenTagList = new ArrayList<Integer>();
            if (editorTag.isHidePassword()) {
                //是否有当前功能操作权限
                boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1020000, topic.getTagId());
                if (flag_permission) {
                    //输入密码可见
                    allowHiddenTagList.add(HideTagType.PASSWORD.getName());
                }
            }
            if (editorTag.isHideComment()) {
                //是否有当前功能操作权限
                boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1021000, topic.getTagId());
                if (flag_permission) {
                    //评论话题可见
                    allowHiddenTagList.add(HideTagType.COMMENT.getName());
                }
            }
            if (editorTag.isHideGrade()) {
                //是否有当前功能操作权限
                boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1022000, topic.getTagId());
                if (flag_permission) {
                    //达到等级可见
                    allowHiddenTagList.add(HideTagType.GRADE.getName());
                }
            }
            if (editorTag.isHidePoint()) {
                //是否有当前功能操作权限
                boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1023000, topic.getTagId());
                if (flag_permission) {
                    //积分购买可见
                    allowHiddenTagList.add(HideTagType.POINT.getName());
                }
            }
            if (editorTag.isHideAmount()) {
                //是否有当前功能操作权限
                boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1024000, topic.getTagId());
                if (flag_permission) {
                    //余额购买可见
                    allowHiddenTagList.add(HideTagType.AMOUNT.getName());
                }
            }

            //解析隐藏标签
            Map<Integer, Object> analysisHiddenTagMap = textFilterManage.analysisHiddenTag(validValue);
            for (Map.Entry<Integer, Object> entry : analysisHiddenTagMap.entrySet()) {
                if (!allowHiddenTagList.contains(entry.getKey())) {
                    error.put("content", errorMessageService.getMessage(ErrorCode.C_2_0007_0046, new String[]{HideTagName.getKey(entry.getKey())}));//隐藏标签
                    break;
                }
            }

            //删除隐藏标签
            String new_content = textFilterManage.deleteHiddenTag(value);

            //不含标签内容
            String text = textFilterManage.filterText(textFilterManage.specifyHtmlTagToText(new_content));
            //清除空格&nbsp;
            String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();
            //摘要
            if (trimSpace != null && !"".equals(trimSpace)) {
                if (systemSetting.isAllowFilterWord()) {
                    String wordReplace = "";
                    if (systemSetting.getFilterWordReplace() != null) {
                        wordReplace = systemSetting.getFilterWordReplace();
                    }
                    trimSpace = sensitiveWordFilterManage.filterSensitiveWord(trimSpace, wordReplace);
                }
                if (trimSpace.length() > SUMMARY_LENGTH) {
                    topic.setSummary(trimSpace.substring(0, SUMMARY_LENGTH) + "..");
                } else {
                    topic.setSummary(trimSpace + "..");
                }
            }

            //不含标签内容
            String source_text = textFilterManage.filterText(content);
            //清除空格&nbsp;
            String source_trimSpace = cms.utils.StringUtil.replaceSpace(source_text).trim();

            if (isImage == true || isFlash == true || isMedia == true || isFile == true || isMap == true || (!"".equals(source_text.trim()) && !"".equals(source_trimSpace))) {
                if (systemSetting.isAllowFilterWord()) {
                    String wordReplace = "";
                    if (systemSetting.getFilterWordReplace() != null) {
                        wordReplace = systemSetting.getFilterWordReplace();
                    }
                    validValue = sensitiveWordFilterManage.filterSensitiveWord(validValue, wordReplace);
                }

                topic.setIp(IpAddress.getClientIpAddress(request));
                topic.setUserName(accessUser.getUserName());
                topic.setIsStaff(false);
                topic.setContent(validValue);
            } else {
                error.put("content", ErrorCode.C_2_0007_0047);
            }

            //非隐藏标签内图片
            getImageInfo(textFilterManage.readImageName(new_content, "topic")).map(JsonUtils::toJSONString).ifPresent(topic::setImage);
        } else {
            error.put("content", ErrorCode.C_2_0007_0047);
        }


        if (error.size() == 0) {
            List<TopicTagAssociation> topicTagAssociations = Optional.ofNullable(twoTagId).map(l -> l.stream().filter(o -> null != o).map(o -> new TopicTagAssociation(o, accessUser.getUserName())).collect(Collectors.toList())).orElse(Lists.newArrayList());
            try {
                //保存话题
                Optional.of(tagId).map(o -> new TopicTagAssociation(o, accessUser.getUserName())).ifPresent(topicTagAssociations::add);
                topicService.saveTopic(topic, topicTagAssociations, user.getUserName());
            } catch (SystemException e) {
                log.error("submit topic error！", e);
                error.put("topic", ErrorCode.C_2_0007_0038);//提交话题错误
            }

            changeIndexByAddTopic(topic, user, topicTagAssociations, systemSetting, tagMap);

            //统计每分钟原来提交次数
            Integer original = settingManage.getSubmitQuantity("topic", accessUser.getUserName());
            if (original != null) {
                settingManage.addSubmitQuantity("topic", accessUser.getUserName(), original + 1);//刷新每分钟原来提交次数
            } else {
                settingManage.addSubmitQuantity("topic", accessUser.getUserName(), 1);//刷新每分钟原来提交次数
            }
        }


        Map<String, String> returnError = new HashMap<String, String>();//错误
        if (error.size() > 0) {
            //将枚举数据转为错误提示字符
            for (Map.Entry<String, String> entry : error.entrySet()) {
                if (ErrorView.get(entry.getValue()) != null) {
                    returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                } else {
                    returnError.put(entry.getKey(), entry.getValue());
                }
            }
        }
        if (isAjax == true) {

            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

            if (error != null && error.size() > 0) {
                returnValue.put("success", "false");
                returnValue.put("error", returnError);
//                if (isCaptcha) {
//                    returnValue.put("captchaKey", UUIDUtil.getUUID32());
//                }
            } else {
                returnValue.put("success", "true");
                returnValue.put("topicId", topic.getId());
            }
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {


            if (error != null && error.size() > 0) {//如果有错误
                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addFlashAttribute("topic", topic);

                String referer = request.getHeader("referer");

                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";

                return "redirect:/" + referer + queryString;
            }

            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());

                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "保存话题成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);

                String dirName = templateService.findTemplateDir_cache();
                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String edit(ModelMap model, Long topicId, Long tagId, Long[] twoTagIdArray, String title, String content,
                       String token, String captchaKey, String captchaValue, String jumpUrl,
                       RedirectAttributes redirectAttrs,
                       HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<Long> twoTagId = Arrays.stream(twoTagIdArray).distinct().collect(Collectors.toList());
        ErrorHashMap<String> error = new ErrorHashMap<String>();
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据
        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getCloseSite().equals(2)) {
            error.put("topic", ErrorView._21.name());//只读模式不允许提交数据
        }

        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token, error);

        //验证码

        Topic topic = null;
        //旧状态
        Integer old_status = -1;

        String old_content = "";

        List<String> imageNameList = null;
        boolean isImage = false;//是否含有图片
        List<String> flashNameList = null;
        boolean isFlash = false;//是否含有Flash
        List<String> mediaNameList = null;
        boolean isMedia = false;//是否含有音视频
        List<String> fileNameList = null;
        boolean isFile = false;//是否含有文件
        boolean isMap = false;//是否含有地图

        if (topicId != null && topicId > 0L) {
            topic = topicService.findById(topicId);
            if (topic != null) {
                if (!topic.getUserName().equals(accessUser.getUserName())) {
                    error.put("topic", ErrorView._113.name());//只允许修改自己发布的话题
                }
                if (topic.getStatus() > 100) {
                    error.put("topic", ErrorView._114.name());//话题已删除
                }

                //是否有当前功能操作权限
                boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1003000, topic.getTagId());
                if (flag_permission == false) {
                    if (isAjax == true) {
                        response.setStatus(403);//设置状态码

                        WebUtil.writeToWeb("", "json", response);
                        return null;
                    } else {
                        String dirName = templateService.findTemplateDir_cache();

                        String accessPath = accessSourceDeviceManage.accessDevices(request);
                        request.setAttribute("message", "权限不足");
                        return "/templates/" + dirName + "/" + accessPath + "/message";
                    }
                }


                //如果全局不允许提交话题
                if (systemSetting.isAllowTopic() == false) {
                    error.put("topic", ErrorView._110.name());//不允许提交话题
                }

                //如果实名用户才允许提交话题
                if (systemSetting.isRealNameUserAllowTopic() == true) {
                    User _user = userManage.query_cache_findUserByUserName(accessUser.getUserName());
                    if (_user.isRealNameAuthentication() == false) {
                        error.put("topic", ErrorView._109.name());//实名用户才允许提交话题
                    }
                }

                old_status = topic.getStatus();
                old_content = topic.getContent();
            } else {
                error.put("topic", ErrorView._112.name());//话题不存在
            }
        } else {
            error.put("topic", ErrorView._103.name());//话题Id不能为空
        }


        if (error.size() == 0) {
            if (topic.getStatus().equals(20)) {//如果已发布，则重新执行发贴审核逻辑
                //前台发表话题审核状态
                if (systemSetting.getTopic_review().equals(10)) {//10.全部审核 20.特权会员未触发敏感词免审核(未实现) 30.特权会员免审核 40.触发敏感词需审核(未实现) 50.无需审核
                    topic.setStatus(10);//10.待审核
                } else if (systemSetting.getTopic_review().equals(30)) {
                    if (topic.getTagId() != null && topic.getTagId() > 0L) {
                        //是否有当前功能操作权限
                        boolean _flag_permission = userRoleManage.isPermission(ResourceEnum._1006000, topic.getTagId());
                        if (_flag_permission) {
                            topic.setStatus(20);//20.已发布
                        } else {
                            topic.setStatus(10);//10.待审核
                        }
                    }
                } else {
                    topic.setStatus(20);//20.已发布
                }
            }

            if (title != null && !"".equals(title.trim())) {
                if (systemSetting.isAllowFilterWord()) {
                    String wordReplace = "";
                    if (systemSetting.getFilterWordReplace() != null) {
                        wordReplace = systemSetting.getFilterWordReplace();
                    }
                    title = sensitiveWordFilterManage.filterSensitiveWord(title, wordReplace);
                }

                topic.setTitle(title);
                if (title.length() > 150) {
                    error.put("title", errorMessageService.getMessage(C_2_0007_0044));
                }
            } else {
                error.put("title", errorMessageService.getMessage(ErrorCode.C_2_0007_0045));
            }

            if (content != null && !"".equals(content.trim())) {
                EditorTag editorTag = settingManage.readTopicEditorTag();
                //过滤标签
                content = textFilterManage.filterTag(request, content, editorTag);
                Object[] object = textFilterManage.filterHtml(request, content, "topic", editorTag);
                String value = (String) object[0];
                imageNameList = (List<String>) object[1];
                isImage = (Boolean) object[2];//是否含有图片
                flashNameList = (List<String>) object[3];
                isFlash = (Boolean) object[4];//是否含有Flash
                mediaNameList = (List<String>) object[5];
                isMedia = (Boolean) object[6];//是否含有音视频
                fileNameList = (List<String>) object[7];
                isFile = (Boolean) object[8];//是否含有文件
                isMap = (Boolean) object[9];//是否含有地图

                List<UserGrade> userGradeList = userGradeService.findAllGrade_cache();
                //校正隐藏标签
                String validValue = textFilterManage.correctionHiddenTag(value, userGradeList);

                //允许使用的隐藏标签
                List<Integer> allowHiddenTagList = new ArrayList<Integer>();
                if (editorTag.isHidePassword()) {
                    //是否有当前功能操作权限
                    boolean _flag_permission = userRoleManage.isPermission(ResourceEnum._1020000, topic.getTagId());
                    if (_flag_permission) {
                        //输入密码可见
                        allowHiddenTagList.add(HideTagType.PASSWORD.getName());
                    }
                }
                if (editorTag.isHideComment()) {
                    //是否有当前功能操作权限
                    boolean _flag_permission = userRoleManage.isPermission(ResourceEnum._1021000, topic.getTagId());
                    if (_flag_permission) {
                        //评论话题可见
                        allowHiddenTagList.add(HideTagType.COMMENT.getName());
                    }
                }
                if (editorTag.isHideGrade()) {
                    //是否有当前功能操作权限
                    boolean _flag_permission = userRoleManage.isPermission(ResourceEnum._1022000, topic.getTagId());
                    if (_flag_permission) {
                        //达到等级可见
                        allowHiddenTagList.add(HideTagType.GRADE.getName());
                    }
                }
                if (editorTag.isHidePoint()) {
                    //是否有当前功能操作权限
                    boolean _flag_permission = userRoleManage.isPermission(ResourceEnum._1023000, topic.getTagId());
                    if (_flag_permission) {
                        //积分购买可见
                        allowHiddenTagList.add(HideTagType.POINT.getName());
                    }
                }
                if (editorTag.isHideAmount()) {
                    //是否有当前功能操作权限
                    boolean _flag_permission = userRoleManage.isPermission(ResourceEnum._1024000, topic.getTagId());
                    if (_flag_permission) {
                        //余额购买可见
                        allowHiddenTagList.add(HideTagType.AMOUNT.getName());
                    }
                }

                //解析隐藏标签
                Map<Integer, Object> analysisHiddenTagMap = textFilterManage.analysisHiddenTag(validValue);
                for (Map.Entry<Integer, Object> entry : analysisHiddenTagMap.entrySet()) {
                    if (!allowHiddenTagList.contains(entry.getKey())) {
                        error.put("content", errorMessageService.getMessage(ErrorCode.C_2_0007_0046, new String[]{HideTagName.getKey(entry.getKey())}));//隐藏标签
                        break;
                    }
                }

                //删除隐藏标签
                String new_content = textFilterManage.deleteHiddenTag(value);

                //不含标签内容
                String text = textFilterManage.filterText(textFilterManage.specifyHtmlTagToText(new_content));
                //清除空格&nbsp;
                String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();
                //摘要
                if (trimSpace != null && !"".equals(trimSpace)) {
                    if (systemSetting.isAllowFilterWord()) {
                        String wordReplace = "";
                        if (systemSetting.getFilterWordReplace() != null) {
                            wordReplace = systemSetting.getFilterWordReplace();
                        }
                        trimSpace = sensitiveWordFilterManage.filterSensitiveWord(trimSpace, wordReplace);
                    }
                    if (trimSpace.length() > SUMMARY_LENGTH) {
                        topic.setSummary(trimSpace.substring(0, SUMMARY_LENGTH) + "..");
                    } else {
                        topic.setSummary(trimSpace + "..");
                    }
                }

                //不含标签内容
                String source_text = textFilterManage.filterText(content);
                //清除空格&nbsp;
                String source_trimSpace = cms.utils.StringUtil.replaceSpace(source_text).trim();

                if (isImage == true || isFlash == true || isMedia == true || isFile == true || isMap == true || (!"".equals(source_text.trim()) && !"".equals(source_trimSpace))) {
                    if (systemSetting.isAllowFilterWord()) {
                        String wordReplace = "";
                        if (systemSetting.getFilterWordReplace() != null) {
                            wordReplace = systemSetting.getFilterWordReplace();
                        }
                        validValue = sensitiveWordFilterManage.filterSensitiveWord(validValue, wordReplace);
                    }
                    topic.setContent(validValue);
                } else {
                    error.put("content", errorMessageService.getMessage(ErrorCode.C_2_0007_0047));
                }

                //非隐藏标签内图片
                topic.setImage(getImageInfo(textFilterManage.readImageName(new_content, "topic")).map(JsonUtils::toJSONString).orElse(null));
            } else {
                error.put("content", errorMessageService.getMessage(ErrorCode.C_2_0007_0047));
            }
        }

        if (error.size() == 0) {
            if (null == topicUpdateLock.putIfAbsent(topic.getId(), Boolean.FALSE)) {
                try {
                    topic.setLastUpdateTime(new Date());//最后修改时间
                    Map<Long, Tag> tagMap = tagService.findAllTag_cache().stream().collect(Collectors.toMap(o -> o.getId(), o -> o));
                    List<Long> oldTagIds = topicTagAssociationService.findByTopicId(topic.getId()).stream().map(TopicTagAssociation::getTagId).map(tagMap::get).filter(Objects::nonNull).filter(o -> o.getGrade() == TOPIC_TAG_GRADE_TWO).map(Tag::getId).collect(Collectors.toList());
                    List<Long> newTagIds = twoTagId;
                    List<TopicTagAssociation> topicTagAssociations = Optional.ofNullable(twoTagId).map(l -> l.stream().filter(o -> null != o).map(o -> new TopicTagAssociation(o, accessUser.getUserName())).collect(Collectors.toList())).orElse(Lists.newArrayList());
                    Optional.of(topic.getTagId()).map(o -> new TopicTagAssociation(o, accessUser.getUserName())).ifPresent(topicTagAssociations::add);
                    int i = topicService.updateTopic2(topic, topicTagAssociations);
                    //更新索引
                    topicIndexService.addTopicIndex(new TopicIndex(String.valueOf(topic.getId()), 2));

                    if (i > 0 && topic.getStatus() < 100 && !old_status.equals(topic.getStatus())) {
                        User user = userManage.query_cache_findUserByUserName(topic.getUserName());
                        if (user != null) {
                            //修改用户动态话题状态
                            userService.updateUserDynamicTopicStatus(user.getId(), topic.getUserName(), topic.getId(), topic.getStatus());
                        }
                    }

                    if (i > 0) {
                        //删除缓存
                        topicManage.deleteTopicCache(topic.getId());//删除话题缓存
                        topicManage.delete_cache_analysisHiddenTag(topic.getId());//删除解析隐藏标签缓存
                        topicManage.delete_cache_analysisFullFileName(topic.getId());//删除 解析上传的文件完整路径名称缓存
                        topicManage.delete_cache_markUpdateTopicStatus(topic.getId());//删除 标记修改话题状态
                        ossFileChangeService.fileChange(old_content, "topic", imageNameList, flashNameList, mediaNameList, fileNameList);
                        Optional.ofNullable(topic.getStatus()).filter(o -> o.equals(20)).ifPresent(o -> {
                            TopicManage.incDecTwoTagTopicNum(oldTagIds, newTagIds);
                            oldTagIds.stream().map(tagMap::get).map(Tag::getName).forEach(topicManage::delete_cache_twoTagNum);
                            newTagIds.stream().map(tagMap::get).map(Tag::getName).forEach(topicManage::delete_cache_twoTagNum);
                        });
                    } else {
                        error.put("topic", ErrorView._115.name());//修改话题失败
                    }
                    //统计每分钟原来提交次数
                    Integer original = settingManage.getSubmitQuantity("topic", accessUser.getUserName());
                    if (original != null) {
                        settingManage.addSubmitQuantity("topic", accessUser.getUserName(), original + 1);//刷新每分钟原来提交次数
                    } else {
                        settingManage.addSubmitQuantity("topic", accessUser.getUserName(), 1);//刷新每分钟原来提交次数
                    }
                } finally {
                    topicUpdateLock.remove(topic.getId());
                }
            } else {
                error.put("content", _119.name());
            }
        }


        Map<String, String> returnError = new HashMap<String, String>();//错误
        if (error.size() > 0) {
            //将枚举数据转为错误提示字符
            for (Map.Entry<String, String> entry : error.entrySet()) {
                if (ErrorView.get(entry.getValue()) != null) {
                    returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                } else {
                    returnError.put(entry.getKey(), entry.getValue());
                }
            }
        }
        if (isAjax == true) {

            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

            if (error != null && error.size() > 0) {
                returnValue.put("success", "false");
                returnValue.put("error", returnError);
            } else {
                returnValue.put("success", "true");
                returnValue.put("topicId", topic.getId());
            }
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {
            if (error != null && error.size() > 0) {//如果有错误

                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addFlashAttribute("topic", topic);

                String referer = request.getHeader("referer");

                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";

                return "redirect:/" + referer + queryString;
            }

            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());

                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "修改话题成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);

                String dirName = templateService.findTemplateDir_cache();


                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Long addByApi(HttpServletRequest request, TopicApi topicApi) {
        AccessUser accessUser = AccessUserThreadLocal.get();
        User user = Optional.ofNullable(userService.findUserByUserName(accessUser.getUserName())).orElseThrow(() -> new CustomException(C_2_2001_0098, "user"));//查询用户数据
        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        String filterWordReplace = Optional.ofNullable(systemSetting.getFilterWordReplace()).orElse(Strings.EMPTY);

        Topic topic = new Topic();
        Date d = new Date();
        topic.setPostTime(d);
        topic.setLastUpdateTime(d);
        topic.setLastReplyTime(d);
        topic.setStatus(TOPIC_STATUS_RELEASE);//20.已发布
        topic.setIp(IpAddress.getClientIpAddress(request));
        topic.setUserName(accessUser.getUserName());
        topic.setIsStaff(false);

        Map<Long, Tag> tagMap = Optional.ofNullable(tagService.findAllTag_cache()).filter(CollUtil::isNotEmpty).map(l -> l.stream().collect(Collectors.toMap(Tag::getId, o -> o))).orElseThrow(() -> new CustomException(C_1_0003_0003, "tag"));
        Tag tag1 = Optional.ofNullable(topicApi.getTagId()).map(tagMap::get).filter(o -> o.getGrade().intValue() == TOPIC_TAG_GRADE_ONE).orElseThrow(() -> new CustomException(C_1_0003_0003, "tag"));
        topic.setTagId(tag1.getId());
        topic.setTagName(tag1.getName());
        topic.setTitle(Optional.ofNullable(topicApi.getTitle()).filter(Strings::isNotEmpty).map(o -> sensitiveWordFilterManage.filterSensitiveWord(o, filterWordReplace)).map(o -> Optional.of(o).filter(v -> v.length() <= 150).orElseThrow(() -> new CustomException(C_2_0007_0044, "title"))).orElseThrow(() -> new CustomException(C_2_0007_0045, "title")));

        EditorTag editorTag = settingManage.readTopicEditorTag();
        // //过滤标签
        String content = Optional.ofNullable(topicApi.getContent()).map(String::trim).map(o -> textFilterManage.filterTag(request, o, editorTag)).orElseThrow(() -> new CustomException(C_2_0007_0047, "content"));
        Object[] object = textFilterManage.filterHtml(request, content, "topic", editorTag);
        String value = (String) object[0];
        Boolean isImage = (Boolean) object[2];//是否含有图片
        Boolean isFlash = (Boolean) object[4];//是否含有Flash
        Boolean isMedia = (Boolean) object[6];//是否含有音视频
        Boolean isFile = (Boolean) object[8];//是否含有文件
        Boolean isMap = (Boolean) object[9];//是否含有地图

        String new_content = Optional.ofNullable(textFilterManage.deleteHiddenTag(value)).orElse(Strings.EMPTY);
        // 摘要
        Optional.of(new_content).map(textFilterManage::specifyHtmlTagToText).map(textFilterManage::filterText).map(cms.utils.StringUtil::replaceSpace).map(String::trim).map(o -> sensitiveWordFilterManage.filterSensitiveWord(o, filterWordReplace)).map(s -> Optional.of(s).filter(o -> o.length() <= SUMMARY_LENGTH).orElseGet(() -> s.substring(0, SUMMARY_LENGTH) + "..")).ifPresent(topic::setSummary);
        // 内容
        topic.setContent(Optional.of(content).map(textFilterManage::filterText).map(cms.utils.StringUtil::replaceSpace).map(String::trim).filter(o -> isImage || isFlash || isMedia || isFile || isMap || Strings.isNotEmpty(o)).map(o -> textFilterManage.correctionHiddenTag(value, userGradeService.findAllGrade_cache())).map(o -> sensitiveWordFilterManage.filterSensitiveWord(o, filterWordReplace)).orElseThrow(() -> new CustomException(C_2_0007_0047, "content")));
        // 非隐藏标签内图片
        getImageInfo(textFilterManage.readImageName(new_content, "topic")).map(JsonUtils::toJSONString).ifPresent(topic::setImage);

        List<TopicTagAssociation> topicTagAssociations = Optional.ofNullable(topicApi.getTwoTagId()).map(l -> l.stream().filter(o -> null != o).map(o -> new TopicTagAssociation(o, accessUser.getUserName())).collect(Collectors.toList())).orElse(Lists.newArrayList());
        Optional.of(topicApi.getTagId()).map(o -> new TopicTagAssociation(o, accessUser.getUserName())).ifPresent(topicTagAssociations::add);
        try {
            topicService.saveTopic(topic, topicTagAssociations, user.getUserName());
        } catch (SystemException e) {
            log.error("submit topic error！", e);
            throw new CustomException(C_2_0007_0038, "topic");
        }

        Optional.ofNullable(topicApi.getOperationId()).map(o -> operationService.getById(o).map(BaseUserDel::getId).orElseThrow(() -> new CustomException(C_2_0002_0001, "operation"))).ifPresent(o -> operationTopicService.add(o, topic.getId()));
        changeIndexByAddTopic(topic, user, topicTagAssociations, systemSetting, Optional.of(tagMap));
        return topic.getId();
    }

    private Optional<List<ImageInfo>> getImageInfo(List<String> other_imageNameList) {
        return Optional.ofNullable(other_imageNameList).filter(CollUtil::isNotEmpty).map(l -> l.stream().map(this::getImageInfo).collect(Collectors.toList()));
    }

    private ImageInfo getImageInfo(String other_imageName) {
        ImageInfo imageInfo = new ImageInfo();
        imageInfo.setName(FileUtil.getName(other_imageName));
        imageInfo.setPath(FileUtil.getFullPath(other_imageName));
        return imageInfo;
    }

    private void changeIndexByAddTopic(Topic topic, User user, List<TopicTagAssociation> topicTagAssociations, SystemSetting systemSetting, Optional<Map<Long, Tag>> tagMap) {
        //更新索引
        topicIndexService.addTopicIndex(new TopicIndex(String.valueOf(topic.getId()), 1));

        PointLog pointLog = new PointLog();
        pointLog.setId(pointManage.createPointLogId(user.getId()));
        pointLog.setModule(100);//100.话题
        pointLog.setParameterId(topic.getId());//参数Id
        pointLog.setOperationUserType(2);//操作用户类型  0:系统  1: 员工  2:会员
        pointLog.setOperationUserName(user.getUserName());//操作用户名称

        pointLog.setPoint(systemSetting.getTopic_rewardPoint());//积分
        pointLog.setUserName(user.getUserName());//用户名称
        pointLog.setRemark("");
        //增加用户积分
        userService.addUserPoint(user.getUserName(), systemSetting.getTopic_rewardPoint(), pointManage.createPointLogObject(pointLog));

        //用户动态
        UserDynamic userDynamic = new UserDynamic();
        userDynamic.setId(userDynamicManage.createUserDynamicId(user.getId()));
        userDynamic.setUserName(user.getUserName());
        userDynamic.setModule(100);//模块 100.话题
        userDynamic.setTopicId(topic.getId());
        userDynamic.setPostTime(topic.getPostTime());
        userDynamic.setStatus(topic.getStatus());
        userDynamic.setFunctionIdGroup("," + topic.getId() + ",");
        Object new_userDynamic = userDynamicManage.createUserDynamicObject(userDynamic);
        userService.saveUserDynamic(new_userDynamic);

        EmailRemindService.addEmailRemind(new EmailRemind(topic.getId(), user.getId(), user.getUserName(), IDOL_TOPIC.getCode(), Optional.empty()));

        //删除缓存
        userManage.delete_cache_findUserById(user.getId());
        userManage.delete_cache_findUserByUserName(user.getUserName());
        followManage.delete_cache_userUpdateFlag(user.getUserName());
        topicManage.delete_cache_markUpdateTopicStatus(topic.getId());//删除 标记修改话题状态
        topicManage.delete_cache_postNumByUserName(user.getUserName());

        Optional.ofNullable(topicTagAssociations).filter(o -> null != topic.getStatus() && topic.getStatus().equals(20)).map(l -> l.stream().map(TopicTagAssociation::getTagId).collect(Collectors.toList())).filter(CollUtil::isNotEmpty).ifPresent(l -> {
            TopicManage.incTwoTagTopicNum(l);
            tagMap.ifPresent(m -> l.stream().map(m::get).map(Tag::getName).forEach(topicManage::delete_cache_twoTagNum));
        });
    }
}
