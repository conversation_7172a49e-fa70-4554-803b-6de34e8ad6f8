package cms.bean.question;

import cms.constraints.validateGroups.Add;
import cms.constraints.validateGroups.Update;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.*;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 问题标签
 */
@Entity
@Table(indexes = {@Index(name = "questionTag_1_idx", columnList = "sort")})
@Data
public class QuestionTag implements Serializable {
    private static final long serialVersionUID = -811884294168718523L;

    /**
     * Id
     **/
    @Id
    private Long id;

    /**
     * 标签名称
     **/
    @Column(length = 190, nullable = false)
    @NotEmpty(groups = {Add.class, Update.class})
    @Length(max = 190, groups = {Add.class, Update.class})
    private String name;
    /**
     * 排序
     **/
    @NotNull(groups = {Add.class, Update.class})
    @Max(value = 10000000, groups = {Add.class, Update.class})
    @Min(value = 0, groups = {Add.class, Update.class})
    @Column(nullable = false)
    private Integer sort;
    /**
     * 所属父类ID
     **/
    @Column(nullable = false)
    private Long parentId;
    /**
     * 父Id组  顺序存放
     **/
    @Column(length = 190)
    private String parentIdGroup;

    @Column(nullable = false)
    private Integer grade;

    /**
     * 子节点数量
     **/
    @Column(nullable = false)
    private Integer childNodeNumber;

    /**
     * 子标签
     **/
    @Transient
    private List<QuestionTag> childTag = new ArrayList<QuestionTag>();

    /**
     * 添加子标签
     **/
    public void addChildTag(QuestionTag questionTag) {
        this.getChildTag().add(questionTag);
    }

}

