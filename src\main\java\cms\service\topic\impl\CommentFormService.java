package cms.service.topic.impl;

import cms.bean.ErrorView;
import cms.bean.api.CommentApi;
import cms.bean.like.CommentLike;
import cms.bean.like.Like;
import cms.bean.like.ReplyLike;
import cms.bean.message.EmailRemind;
import cms.bean.message.Remind;
import cms.bean.operation.OperationTopic;
import cms.bean.setting.SystemSetting;
import cms.bean.topic.Comment;
import cms.bean.topic.Quote;
import cms.bean.topic.Reply;
import cms.bean.topic.Topic;
import cms.bean.user.*;
import cms.constant.enums.LikeType;
import cms.handle.CustomException;
import cms.service.aliyun.OssFileChangeService;
import cms.service.like.LikeService;
import cms.service.like.impl.CommentLikeService;
import cms.service.like.impl.ReplyLikeService;
import cms.service.message.RemindService;
import cms.service.message.impl.EmailRemindService;
import cms.service.operation.OperationTopicService;
import cms.service.setting.SettingService;
import cms.service.template.TemplateService;
import cms.service.topic.CommentService;
import cms.service.topic.TopicService;
import cms.service.user.UserService;
import cms.utils.Base64;
import cms.utils.*;
import cms.utils.threadLocal.AccessUserThreadLocal;
import cms.web.action.AccessSourceDeviceManage;
import cms.web.action.CSRFTokenManage;
import cms.web.action.TextFilterManage;
import cms.web.action.filterWord.SensitiveWordFilterManage;
import cms.web.action.follow.FollowManage;
import cms.web.action.like.LikeManage;
import cms.web.action.message.RemindManage;
import cms.web.action.setting.SettingManage;
import cms.web.action.topic.CommentManage;
import cms.web.action.topic.TopicManage;
import cms.web.action.user.PointManage;
import cms.web.action.user.UserDynamicManage;
import cms.web.action.user.UserManage;
import cms.web.action.user.UserRoleManage;
import cms.web.taglib.Configuration;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

import static cms.constant.Constant.*;
import static cms.constant.ErrorCode.*;
import static cms.constant.enums.RemindTypeEnums.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/4 11:18
 */
@Service
@Slf4j
public class CommentFormService {

    private static Map<Long, Object> lockMap = Maps.newConcurrentMap();

    @Resource
    TemplateService templateService;

    @Resource
    CommentService commentService;
    @Resource
    AccessSourceDeviceManage accessSourceDeviceManage;

    @Resource
    TextFilterManage textFilterManage;
    @Resource
    SettingManage settingManage;
    @Resource
    SettingService settingService;
    @Resource
    UserService userService;
    @Resource
    TopicService topicService;
    @Resource
    PointManage pointManage;

    @Resource
    CSRFTokenManage csrfTokenManage;

    @Resource
    TopicManage topicManage;
    @Resource
    SensitiveWordFilterManage sensitiveWordFilterManage;
    @Resource
    UserManage userManage;

    @Resource
    RemindManage remindManage;
    @Resource
    RemindService remindService;
    @Resource
    UserDynamicManage userDynamicManage;
    @Resource
    FollowManage followManage;
    @Resource
    UserRoleManage userRoleManage;
    @Resource
    CommentManage commentManage;
    @Autowired
    private OperationTopicService operationTopicService;
    @Autowired
    private OssFileChangeService ossFileChangeService;
    @Resource
    LikeService likeService;
    @Resource
    LikeManage likeManage;
    @Autowired
    private CommentLikeService commentLikeService;
    @Autowired
    private ReplyLikeService replyLikeService;

    @Transactional(propagation = Propagation.REQUIRED)
    public String add(ModelMap model, Long topicId, String content,
                      String token, String captchaKey, String captchaValue, String jumpUrl,
                      RedirectAttributes redirectAttrs,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据

        Map<String, String> error = new HashMap<String, String>();

        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getCloseSite().equals(2)) {
            error.put("comment", ErrorView._21.name());//只读模式不允许提交数据
        }
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token, error);

        //验证码

        Comment comment = new Comment();
        Date postTime = new Date();
        comment.setPostTime(postTime);
        comment.setTotalLikeNum(0);
        List<String> imageNameList = null;

        Topic topic = null;
        if (topicId != null && topicId > 0L) {
            topic = topicManage.queryTopicCache(topicId);
            if (topic != null) {
                comment.setTopicId(topicId);
                //是否全局允许评论
                if (systemSetting.isAllowComment() == false) {
                    error.put("comment", ErrorView._106.name());//禁止评论
                }
                if (topic.isAllow() == false) {
                    error.put("comment", ErrorView._106.name());//禁止评论
                }
                if (!topic.getStatus().equals(COMMENT_STATUS_RELEASE)) {//已发布
                    error.put("comment", ErrorView._111.name());//已发布话题才允许评论
                }
            } else {
                error.put("comment", ErrorView._107.name());//话题不存在
            }
        } else {
            error.put("comment", ErrorView._103.name());//话题Id不能为空
        }

        if (topic != null) {
            //前台发表评论审核状态
            if (systemSetting.getComment_review().equals(10)) {//10.全部审核 20.特权会员未触发敏感词免审核(未实现) 30.特权会员免审核 40.触发敏感词需审核(未实现) 50.无需审核
                comment.setStatus(10);//10.待审核
            } else if (systemSetting.getComment_review().equals(30)) {
                if (topic != null) {
                    //是否有当前功能操作权限
                    boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1012000, topic.getTagId());
                    if (flag_permission) {
                        comment.setStatus(COMMENT_STATUS_RELEASE);//20.已发布
                    } else {
                        comment.setStatus(10);//10.待审核
                    }
                }
            } else {
                comment.setStatus(COMMENT_STATUS_RELEASE);//20.已发布
            }

            //是否有当前功能操作权限
            boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1007000, topic.getTagId());
            if (flag_permission == false) {
                if (isAjax == true) {
                    response.setStatus(403);//设置状态码

                    WebUtil.writeToWeb("", "json", response);
                    return null;
                } else {
                    String dirName = templateService.findTemplateDir_cache();

                    String accessPath = accessSourceDeviceManage.accessDevices(request);
                    request.setAttribute("message", "权限不足");
                    return "/templates/" + dirName + "/" + accessPath + "/message";
                }
            }
        }

        //如果实名用户才允许评论
        if (systemSetting.isRealNameUserAllowComment() == true) {
            User _user = userManage.query_cache_findUserByUserName(accessUser.getUserName());
            if (_user.isRealNameAuthentication() == false) {
                error.put("comment", ErrorView._108.name());//实名用户才允许评论
            }
        }

        if (content != null && !"".equals(content.trim())) {
            //过滤标签
            content = textFilterManage.filterTag(request, content, settingManage.readEditorTag());
            Object[] object = textFilterManage.filterHtml(request, content, "comment", settingManage.readEditorTag());
            String value = (String) object[0];
            imageNameList = (List<String>) object[1];
            boolean isImage = (Boolean) object[2];//是否含有图片
            boolean isMedia = (Boolean) object[6];//是否含有音视频
            //不含标签内容
            String text = textFilterManage.filterText(content);
            //清除空格&nbsp;
            String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();

            if (isImage == true || isMedia || (!"".equals(text.trim()) && !"".equals(trimSpace))) {
                if (systemSetting.isAllowFilterWord()) {
                    String wordReplace = "";
                    if (systemSetting.getFilterWordReplace() != null) {
                        wordReplace = systemSetting.getFilterWordReplace();
                    }
                    value = sensitiveWordFilterManage.filterSensitiveWord(value, wordReplace);
                }
                comment.setContent(value);
                comment.setIsStaff(false);
                comment.setUserName(accessUser.getUserName());
            } else {
                error.put("content", ErrorView._101.name());//内容不能为空
            }
        } else {
            error.put("content", ErrorView._101.name());//内容不能为空
        }

        if (error.size() == 0) {
            comment.setIp(IpAddress.getClientIpAddress(request));
            //保存评论
            this.add(topic, comment, accessUser, systemSetting, postTime);

            //统计每分钟原来提交次数
            Integer original = settingManage.getSubmitQuantity("comment", accessUser.getUserName());
            if (original != null) {
                settingManage.addSubmitQuantity("comment", accessUser.getUserName(), original + 1);//刷新每分钟原来提交次数
            } else {
                settingManage.addSubmitQuantity("comment", accessUser.getUserName(), 1);//刷新每分钟原来提交次数
            }
        }

        Map<String, String> returnError = new HashMap<String, String>();//错误
        if (error.size() > 0) {
            //将枚举数据转为错误提示字符
            for (Map.Entry<String, String> entry : error.entrySet()) {
                if (ErrorView.get(entry.getValue()) != null) {
                    returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                } else {
                    returnError.put(entry.getKey(), entry.getValue());
                }
            }
        }
        if (isAjax == true) {

            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

            if (error != null && error.size() > 0) {
                returnValue.put("success", "false");
                returnValue.put("error", returnError);
            } else {
                returnValue.put("success", "true");
            }
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {


            if (error != null && error.size() > 0) {//如果有错误
                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addFlashAttribute("comment", comment);


                String referer = request.getHeader("referer");


                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";

                return "redirect:/" + referer + queryString;

            }


            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());

                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "保存评论成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);

                String dirName = templateService.findTemplateDir_cache();


                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String addQuote(ModelMap model, Long commentId, String content,
                           String token, String captchaKey, String captchaValue, String jumpUrl,
                           RedirectAttributes redirectAttrs,
                           HttpServletRequest request, HttpServletResponse response) throws Exception {
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据

        Map<String, String> error = new HashMap<String, String>();

        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getCloseSite().equals(2)) {
            error.put("comment", ErrorView._21.name());//只读模式不允许提交数据
        }

        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token, error);

        //验证码

        Comment comment = null;

        if (commentId == null || commentId <= 0) {
            error.put("comment", ErrorView._14.name());//引用评论不能为空
        } else {
            comment = commentService.findByCommentId(commentId);
        }

        Topic topic = null;
        if (comment != null) {
            topic = topicService.findById(comment.getTopicId());
            if (topic != null) {
                //是否全局允许评论
                if (systemSetting.isAllowComment() == false) {
                    error.put("comment", ErrorView._106.name());//禁止评论
                }
                if (topic.isAllow() == false) {
                    error.put("comment", ErrorView._106.name());//禁止评论
                }
                if (!topic.getStatus().equals(COMMENT_STATUS_RELEASE)) {//已发布
                    error.put("comment", ErrorView._111.name());//已发布话题才允许评论
                }
            } else {
                error.put("comment", ErrorView._107.name());//话题不存在
            }
        } else {
            error.put("comment", ErrorView._14.name());//引用评论不能为空
        }

        if (topic != null) {
            //是否有当前功能操作权限
            boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1007000, topic.getTagId());
            if (flag_permission == false) {
                if (isAjax == true) {
                    response.setStatus(403);//设置状态码

                    WebUtil.writeToWeb("", "json", response);
                    return null;
                } else {
                    String dirName = templateService.findTemplateDir_cache();

                    String accessPath = accessSourceDeviceManage.accessDevices(request);
                    request.setAttribute("message", "权限不足");
                    return "/templates/" + dirName + "/" + accessPath + "/message";
                }
            }
        }

        //如果实名用户才允许评论
        if (systemSetting.isRealNameUserAllowComment() == true) {
            User _user = userManage.query_cache_findUserByUserName(accessUser.getUserName());
            if (_user.isRealNameAuthentication() == false) {
                error.put("comment", ErrorView._108.name());//实名用户才允许评论
            }
        }

        Comment newComment = new Comment();
        Date postTime = new Date();
        newComment.setPostTime(postTime);
        List<String> imageNameList = null;

        //前台发表评论审核状态
        if (systemSetting.getComment_review().equals(10)) {//10.全部审核 20.特权会员未触发敏感词免审核(未实现) 30.特权会员免审核 40.触发敏感词需审核(未实现) 50.无需审核
            newComment.setStatus(10);//10.待审核
        } else if (systemSetting.getComment_review().equals(30)) {
            if (topic != null) {
                //是否有当前功能操作权限
                boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1012000, topic.getTagId());
                if (flag_permission) {
                    newComment.setStatus(COMMENT_STATUS_RELEASE);//20.已发布
                } else {
                    newComment.setStatus(10);//10.待审核
                }
            }
        } else {
            newComment.setStatus(COMMENT_STATUS_RELEASE);//20.已发布
        }

        if (content != null && !"".equals(content.trim())) {
            if (comment != null) {
                //过滤标签
                content = textFilterManage.filterTag(request, content, settingManage.readEditorTag());

                Object[] object = textFilterManage.filterHtml(request, content, "comment", settingManage.readEditorTag());
                String value = (String) object[0];
                imageNameList = (List<String>) object[1];
                boolean isImage = (Boolean) object[2];//是否含有图片
                //不含标签内容
                String text = textFilterManage.filterText(content);
                //清除空格&nbsp;
                String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();

                if (isImage == true || (!"".equals(text.trim()) && !"".equals(trimSpace))) {
                    //旧引用
                    List<Quote> old_customQuoteList = new ArrayList<Quote>();
                    //旧引用Id组
                    String old_customQuoteId = "," + comment.getId() + comment.getQuoteIdGroup();
                    if (comment.getQuote() != null && !"".equals(comment.getQuote().trim())) {
                        //旧引用
                        old_customQuoteList = JsonUtils.toGenericObject(comment.getQuote(), new TypeReference<List<Quote>>() {
                        });
                    }

                    //自定义引用
                    Quote customQuote = new Quote();
                    customQuote.setCommentId(comment.getId());
                    customQuote.setIsStaff(comment.getIsStaff());
                    customQuote.setUserName(comment.getUserName());
                    customQuote.setContent(textFilterManage.filterText(textFilterManage.specifyHtmlTagToText(comment.getContent())));
                    old_customQuoteList.add(customQuote);

                    //评论
                    newComment.setTopicId(comment.getTopicId());
                    newComment.setIp(IpAddress.getClientIpAddress(request));
                    if (systemSetting.isAllowFilterWord()) {
                        String wordReplace = "";
                        if (systemSetting.getFilterWordReplace() != null) {
                            wordReplace = systemSetting.getFilterWordReplace();
                        }
                        value = sensitiveWordFilterManage.filterSensitiveWord(value, wordReplace);
                    }
                    newComment.setContent(value);
                    newComment.setIsStaff(false);
                    newComment.setQuoteIdGroup(old_customQuoteId);
                    newComment.setUserName(accessUser.getUserName());
                    newComment.setQuote(JsonUtils.toJSONString(old_customQuoteList));
                } else {
                    error.put("content", ErrorView._101.name());//内容不能为空
                }
            } else {
                error.put("comment", ErrorView._104.name());//引用评论不能为空
            }
        } else {
            error.put("content", ErrorView._101.name());//内容不能为空
        }

        //保存
        if (error.size() == 0) {
            //保存评论
            commentService.saveComment(newComment);

            PointLog pointLog = new PointLog();
            pointLog.setId(pointManage.createPointLogId(accessUser.getUserId()));
            pointLog.setModule(200);//200.评论
            pointLog.setParameterId(comment.getId());//参数Id
            pointLog.setOperationUserType(2);//操作用户类型  0:系统  1: 员工  2:会员
            pointLog.setOperationUserName(accessUser.getUserName());//操作用户名称

            pointLog.setPoint(systemSetting.getComment_rewardPoint());//积分
            pointLog.setUserName(accessUser.getUserName());//用户名称
            pointLog.setRemark("");

            //增加用户积分
            userService.addUserPoint(accessUser.getUserName(), systemSetting.getComment_rewardPoint(), pointManage.createPointLogObject(pointLog));

            //用户动态
            UserDynamic userDynamic = new UserDynamic();
            userDynamic.setId(userDynamicManage.createUserDynamicId(accessUser.getUserId()));
            userDynamic.setUserName(accessUser.getUserName());
            userDynamic.setModule(300);//模块 300.引用评论
            userDynamic.setTopicId(topic.getId());
            userDynamic.setCommentId(newComment.getId());
            userDynamic.setQuoteCommentId(comment.getId());
            userDynamic.setPostTime(newComment.getPostTime());
            userDynamic.setStatus(newComment.getStatus());
            userDynamic.setFunctionIdGroup("," + topic.getId() + "," + newComment.getId() + ",");
            Object new_userDynamic = userDynamicManage.createUserDynamicObject(userDynamic);
            userService.saveUserDynamic(new_userDynamic);

            //删除缓存
            userManage.delete_cache_findUserById(accessUser.getUserId());
            userManage.delete_cache_findUserByUserName(accessUser.getUserName());

            followManage.delete_cache_userUpdateFlag(accessUser.getUserName());

            //修改话题最后回复时间
            topicService.updateTopicReplyTime(newComment.getTopicId(), new Date());

            User _user = userManage.query_cache_findUserByUserName(topic.getUserName());
            //别人评论了我的话题
            if (!topic.getIsStaff() && _user != null && !_user.getId().equals(accessUser.getUserId())) {//楼主评论不发提醒给自己
                //如果别人引用话题发布者的评论,则不发本类型提醒给话题发布者
                if (!comment.getUserName().equals(topic.getUserName())) {
                    //提醒楼主
                    Remind remind = new Remind();
                    remind.setId(remindManage.createRemindId(_user.getId()));
                    remind.setReceiverUserId(_user.getId());//接收提醒用户Id
                    remind.setSenderUserId(accessUser.getUserId());//发送用户Id
                    remind.setTypeCode(COMMENT_MY_TOPIC.getCode());//10:别人评论了我的话题
                    remind.setSendTimeFormat(postTime.getTime());//发送时间格式化
                    remind.setTopicId(comment.getTopicId());//话题Id
                    remind.setFriendTopicCommentId(newComment.getId());//对方的话题评论Id

                    Object remind_object = remindManage.createRemindObject(remind);
                    remindService.saveRemind(remind_object);
                    Optional.ofNullable(_user).filter(User::getEmailRemind).ifPresent(o -> EmailRemindService.addEmailRemind(new EmailRemind(newComment.getId(), accessUser.getUserId(), accessUser.getUserName(), COMMENT_MY_TOPIC.getCode(), Optional.of(o))));

                    //删除提醒缓存
                    remindManage.delete_cache_findUnreadRemindByUserId(_user.getId());
                }
            }

            _user = userManage.query_cache_findUserByUserName(comment.getUserName());
            //别人引用了我的评论
            if (!comment.getIsStaff() && _user != null && !_user.getId().equals(accessUser.getUserId())) {//引用自已的评论不发提醒给自己
                //提醒楼主
                Remind remind = new Remind();
                remind.setId(remindManage.createRemindId(_user.getId()));
                remind.setReceiverUserId(_user.getId());//接收提醒用户Id
                remind.setSenderUserId(accessUser.getUserId());//发送用户Id
                remind.setTypeCode(QUOTE_MY_COMMENT.getCode());//30:别人引用了我的评论
                remind.setSendTimeFormat(postTime.getTime());//发送时间格式化
                remind.setTopicId(comment.getTopicId());//话题Id
                remind.setTopicCommentId(comment.getId());//我的话题评论Id
                remind.setFriendTopicCommentId(newComment.getId());//对方的话题评论Id

                Object remind_object = remindManage.createRemindObject(remind);
                remindService.saveRemind(remind_object);
                Optional.ofNullable(_user).filter(User::getEmailRemind).ifPresent(o -> EmailRemindService.addEmailRemind(new EmailRemind(newComment.getId(), accessUser.getUserId(), accessUser.getUserName(), QUOTE_MY_COMMENT.getCode(), Optional.of(o))));

                //删除提醒缓存
                remindManage.delete_cache_findUnreadRemindByUserId(_user.getId());
            }

            //统计每分钟原来提交次数
            Integer original = settingManage.getSubmitQuantity("comment", accessUser.getUserName());
            if (original != null) {
                settingManage.addSubmitQuantity("comment", accessUser.getUserName(), original + 1);//刷新每分钟原来提交次数
            } else {
                settingManage.addSubmitQuantity("comment", accessUser.getUserName(), 1);//刷新每分钟原来提交次数
            }
        }

        Map<String, String> returnError = new HashMap<String, String>();//错误
        if (error.size() > 0) {
            //将枚举数据转为错误提示字符
            for (Map.Entry<String, String> entry : error.entrySet()) {
                if (ErrorView.get(entry.getValue()) != null) {
                    returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                } else {
                    returnError.put(entry.getKey(), entry.getValue());
                }
            }
        }
        if (isAjax == true) {
            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

            if (error != null && error.size() > 0) {
                returnValue.put("success", "false");
                returnValue.put("error", returnError);
            } else {
                returnValue.put("success", "true");
            }
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {
            if (error != null && error.size() > 0) {//如果有错误
                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addFlashAttribute("comment", newComment);

                String referer = request.getHeader("referer");

                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";
                return "redirect:/" + referer + queryString;
            }

            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());

                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "保存评论成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);
                String dirName = templateService.findTemplateDir_cache();

                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String edit(ModelMap model, Long commentId, String content,
                       String token, String captchaKey, String captchaValue, String jumpUrl,
                       RedirectAttributes redirectAttrs,
                       HttpServletRequest request, HttpServletResponse response) throws Exception {
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据
        Map<String, String> error = new HashMap<String, String>();

        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getCloseSite().equals(2)) {
            error.put("comment", ErrorView._21.name());//只读模式不允许提交数据
        }

        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token, error);

        //验证码

        List<String> imageNameList = null;
        Comment comment = null;
        Topic topic = null;
        //旧状态
        Integer old_status = -1;

        String old_content = "";

        if (commentId != null && commentId > 0L) {
            comment = commentService.findByCommentId(commentId);
            if (comment != null) {
                topic = topicService.findById(comment.getTopicId());
                if (topic != null) {
                    if (!comment.getUserName().equals(accessUser.getUserName())) {
                        error.put("comment", ErrorView._117.name());//只允许修改自己发布的评论
                    }

                    //是否有当前功能操作权限
                    boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1008000, topic.getTagId());
                    if (flag_permission == false) {
                        if (isAjax == true) {
                            response.setStatus(403);//设置状态码

                            WebUtil.writeToWeb("", "json", response);
                            return null;
                        } else {
                            String dirName = templateService.findTemplateDir_cache();

                            String accessPath = accessSourceDeviceManage.accessDevices(request);
                            request.setAttribute("message", "权限不足");
                            return "/templates/" + dirName + "/" + accessPath + "/message";
                        }
                    }

                    //如果全局不允许提交评论
                    if (systemSetting.isAllowComment() == false) {
                        error.put("comment", ErrorView._106.name());//禁止评论
                    }

                    //如果实名用户才允许提交评论
                    if (systemSetting.isRealNameUserAllowComment() == true) {
                        User _user = userManage.query_cache_findUserByUserName(accessUser.getUserName());
                        if (_user.isRealNameAuthentication() == false) {
                            error.put("comment", ErrorView._108.name());//实名用户才允许提交评论
                        }
                    }
                    old_status = comment.getStatus();
                    old_content = comment.getContent();
                } else {
                    error.put("comment", ErrorView._112.name());//话题不存在
                }

            } else {
                error.put("comment", ErrorView._116.name());//评论不存在
            }
        } else {
            error.put("comment", ErrorView._120.name());//评论Id不能为空
        }

        if (error.size() == 0) {
            if (comment.getStatus().equals(COMMENT_STATUS_RELEASE)) {//如果已发布，则重新执行发贴审核逻辑
                //前台发表评论审核状态
                if (systemSetting.getComment_review().equals(10)) {//10.全部审核 20.特权会员未触发敏感词免审核(未实现) 30.特权会员免审核 40.触发敏感词需审核(未实现) 50.无需审核
                    comment.setStatus(10);//10.待审核
                } else if (systemSetting.getComment_review().equals(30)) {
                    if (topic.getTagId() != null && topic.getTagId() > 0L) {
                        //是否有当前功能操作权限
                        boolean _flag_permission = userRoleManage.isPermission(ResourceEnum._1008000, topic.getTagId());
                        if (_flag_permission) {
                            comment.setStatus(COMMENT_STATUS_RELEASE);//20.已发布
                        } else {
                            comment.setStatus(10);//10.待审核
                        }
                    }
                } else {
                    comment.setStatus(COMMENT_STATUS_RELEASE);//20.已发布
                }
            }

            if (content != null && !"".equals(content.trim())) {
                //过滤标签
                content = textFilterManage.filterTag(request, content, settingManage.readEditorTag());
                Object[] object = textFilterManage.filterHtml(request, content, "comment", settingManage.readEditorTag());
                String value = (String) object[0];
                imageNameList = (List<String>) object[1];
                boolean isImage = (Boolean) object[2];//是否含有图片
                boolean isMedia = (Boolean) object[6];//是否含有音视频
                //不含标签内容
                String text = textFilterManage.filterText(content);
                //清除空格&nbsp;
                String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();

                if (isImage == true || isMedia || (!"".equals(text.trim()) && !"".equals(trimSpace))) {
                    if (systemSetting.isAllowFilterWord()) {
                        String wordReplace = "";
                        if (systemSetting.getFilterWordReplace() != null) {
                            wordReplace = systemSetting.getFilterWordReplace();
                        }
                        value = sensitiveWordFilterManage.filterSensitiveWord(value, wordReplace);
                    }
                    comment.setContent(value);
                } else {
                    error.put("content", ErrorView._101.name());//内容不能为空
                }
            } else {
                error.put("content", ErrorView._101.name());//内容不能为空
            }
        }

        if (error.size() == 0) {
            int i = commentService.updateComment(comment.getId(), comment.getContent(), comment.getStatus(), new Date(), comment.getUserName());

            if (i > 0 && comment.getStatus() < 100 && !old_status.equals(comment.getStatus())) {
                User user = userManage.query_cache_findUserByUserName(comment.getUserName());
                if (user != null) {
                    //修改评论状态
                    userService.updateUserDynamicCommentStatus(user.getId(), comment.getUserName(), comment.getTopicId(), comment.getId(), comment.getStatus());
                }
            }

            if (i > 0) {
                //删除缓存
                commentManage.delete_cache_findByCommentId(comment.getId());
                //旧图片删除
                ossFileChangeService.ossImgFileChange(old_content, "comment", imageNameList);
            } else {
                error.put("comment", ErrorView._119.name());//修改评论失败
            }
            //统计每分钟原来提交次数
            Integer original = settingManage.getSubmitQuantity("comment", accessUser.getUserName());
            if (original != null) {
                settingManage.addSubmitQuantity("comment", accessUser.getUserName(), original + 1);//刷新每分钟原来提交次数
            } else {
                settingManage.addSubmitQuantity("comment", accessUser.getUserName(), 1);//刷新每分钟原来提交次数
            }
        }

        Map<String, String> returnError = new HashMap<String, String>();//错误
        if (error.size() > 0) {
            //将枚举数据转为错误提示字符
            for (Map.Entry<String, String> entry : error.entrySet()) {
                if (ErrorView.get(entry.getValue()) != null) {
                    returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                } else {
                    returnError.put(entry.getKey(), entry.getValue());
                }
            }
        }
        if (isAjax == true) {

            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

            if (error != null && error.size() > 0) {
                returnValue.put("success", "false");
                returnValue.put("error", returnError);
            } else {
                returnValue.put("success", "true");
            }
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {
            if (error != null && error.size() > 0) {//如果有错误

                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addFlashAttribute("comment", comment);


                String referer = request.getHeader("referer");


                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";

                return "redirect:/" + referer + queryString;
            }

            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());

                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "修改评论成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);

                String dirName = templateService.findTemplateDir_cache();

                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String delete(ModelMap model, Long commentId,
                         String token, String jumpUrl,
                         RedirectAttributes redirectAttrs,
                         HttpServletRequest request, HttpServletResponse response) throws Exception {
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据
        Map<String, String> error = new HashMap<String, String>();

        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getCloseSite().equals(2)) {
            error.put("comment", ErrorView._21.name());//只读模式不允许提交数据
        }

        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token, error);

        Comment comment = null;
        Topic topic = null;

        if (commentId != null && commentId > 0L) {
            comment = commentService.findByCommentId(commentId);
            if (comment != null) {
                topic = topicService.findById(comment.getTopicId());
                if (topic != null) {
                    if (!comment.getUserName().equals(accessUser.getUserName())) {
                        error.put("comment", ErrorView._143.name());//只允许删除自己发布的评论
                    }

                    if (comment.getStatus() > 100) {
                        error.put("comment", ErrorView._118.name());//评论已删除
                    }

                    //是否有当前功能操作权限
                    boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1009000, topic.getTagId());
                    if (flag_permission == false) {
                        if (isAjax == true) {
                            response.setStatus(403);//设置状态码

                            WebUtil.writeToWeb("", "json", response);
                            return null;
                        } else {
                            String dirName = templateService.findTemplateDir_cache();

                            String accessPath = accessSourceDeviceManage.accessDevices(request);
                            request.setAttribute("message", "权限不足");
                            return "/templates/" + dirName + "/" + accessPath + "/message";
                        }
                    }
                } else {
                    error.put("comment", ErrorView._112.name());//话题不存在
                }
            } else {
                error.put("comment", ErrorView._116.name());//评论不存在
            }
        } else {
            error.put("comment", ErrorView._120.name());//评论Id不能为空
        }

        if (error.size() == 0) {
            Integer constant = 100;
            int i = commentService.markDeleteComment(comment.getTopicId(), comment.getId(), constant);

            if (i > 0 && comment.getStatus() < 100) {
                User user = userManage.query_cache_findUserByUserName(comment.getUserName());
                if (user != null) {
                    //修改评论状态
                    userService.updateUserDynamicCommentStatus(user.getId(), comment.getUserName(), comment.getTopicId(), comment.getId(), comment.getStatus() + constant);
                }
            }

            if (i > 0) {
                //删除缓存
                commentManage.delete_cache_findByCommentId(comment.getId());
                topicManage.deleteTopicCache(topic.getId());//删除缓存
            } else {
                error.put("comment", ErrorView._141.name());//删除评论失败
            }
        }

        Map<String, String> returnError = new HashMap<String, String>();//错误
        if (error.size() > 0) {
            //将枚举数据转为错误提示字符
            for (Map.Entry<String, String> entry : error.entrySet()) {
                if (ErrorView.get(entry.getValue()) != null) {
                    returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                } else {
                    returnError.put(entry.getKey(), entry.getValue());
                }
            }
        }
        if (isAjax == true) {
            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

            if (error != null && error.size() > 0) {
                returnValue.put("success", "false");
                returnValue.put("error", returnError);

            } else {
                returnValue.put("success", "true");
            }
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {
            if (error != null && error.size() > 0) {//如果有错误
                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addFlashAttribute("comment", comment);

                String referer = request.getHeader("referer");

                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";

                return "redirect:/" + referer + queryString;
            }

            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());

                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "删除评论成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);

                String dirName = templateService.findTemplateDir_cache();

                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String addReply(ModelMap model, Long commentId, String content,
                           String token, String captchaKey, String captchaValue, String jumpUrl,
                           RedirectAttributes redirectAttrs,
                           HttpServletRequest request, HttpServletResponse response) throws Exception {
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据

        Map<String, String> error = new HashMap<String, String>();
        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getCloseSite().equals(2)) {
            error.put("reply", ErrorView._21.name());//只读模式不允许提交数据
        }

        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token, error);

        //验证码

        Comment comment = null;
        Topic topic = null;
        if (commentId == null || commentId <= 0) {
            error.put("reply", ErrorView._105.name());//评论不存在
        } else {
            comment = commentService.findByCommentId(commentId);
            if (comment != null) {
                topic = topicService.findById(comment.getTopicId());
                if (topic != null) {
                    //是否全局允许评论
                    if (systemSetting.isAllowComment() == false) {
                        error.put("reply", ErrorView._106.name());//禁止评论
                    }
                    if (topic.isAllow() == false) {
                        error.put("reply", ErrorView._106.name());//禁止评论
                    }
                    if (!topic.getStatus().equals(COMMENT_STATUS_RELEASE)) {//已发布
                        error.put("reply", ErrorView._111.name());//已发布话题才允许回复
                    }
                } else {
                    error.put("reply", ErrorView._107.name());//话题不存在
                }
            } else {
                error.put("reply", ErrorView._105.name());//评论不存在
            }
        }

        if (topic != null) {
            //是否有当前功能操作权限
            boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1013000, topic.getTagId());
            if (flag_permission == false) {
                if (isAjax == true) {
                    response.setStatus(403);//设置状态码

                    WebUtil.writeToWeb("", "json", response);
                    return null;
                } else {
                    String dirName = templateService.findTemplateDir_cache();

                    String accessPath = accessSourceDeviceManage.accessDevices(request);
                    request.setAttribute("message", "权限不足");
                    return "/templates/" + dirName + "/" + accessPath + "/message";
                }
            }
        }

        //如果实名用户才允许评论
        if (systemSetting.isRealNameUserAllowComment() == true) {
            User _user = userManage.query_cache_findUserByUserName(accessUser.getUserName());
            if (_user.isRealNameAuthentication() == false) {
                error.put("reply", ErrorView._108.name());//实名用户才允许评论
            }
        }

        //回复
        Reply reply = new Reply();
        Date postTime = new Date();
        reply.setPostTime(postTime);
        reply.setTotalLikeNum(0);

        //前台发表评论审核状态
        if (systemSetting.getReply_review().equals(10)) {//10.全部审核 20.特权会员未触发敏感词免审核(未实现) 30.特权会员免审核 40.触发敏感词需审核(未实现) 50.无需审核
            reply.setStatus(10);//10.待审核
        } else if (systemSetting.getReply_review().equals(30)) {
            if (topic != null) {
                //是否有当前功能操作权限
                boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1016000, topic.getTagId());
                if (flag_permission) {
                    reply.setStatus(COMMENT_STATUS_RELEASE);//20.已发布
                } else {
                    reply.setStatus(10);//10.待审核
                }
            }
        } else {
            reply.setStatus(COMMENT_STATUS_RELEASE);//20.已发布
        }

        if (error != null && error.size() == 0) {
            if (content != null && !"".equals(content.trim())) {
                if (comment != null) {
                    //过滤标签
                    content = textFilterManage.filterTag(request, content, settingManage.readEditorTag());
                    Object[] object = textFilterManage.filterHtml(request, content, "comment", settingManage.readEditorTag());
                    String value = (String) object[0];
                    boolean isImage = (Boolean) object[2];//是否含有图片
                    boolean isMedia = (Boolean) object[6];//是否含有音视频
                    //不含标签内容
                    String text = textFilterManage.filterText(content);
                    //清除空格&nbsp;
                    String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();

                    if (isImage || isMedia || (!"".equals(text.trim()) && !"".equals(trimSpace))) {
                        if (systemSetting.isAllowFilterWord()) {
                            String wordReplace = "";
                            if (systemSetting.getFilterWordReplace() != null) {
                                wordReplace = systemSetting.getFilterWordReplace();
                            }
                            value = sensitiveWordFilterManage.filterSensitiveWord(value, wordReplace);
                        }

                        reply.setCommentId(comment.getId());
                        reply.setIsStaff(false);
                        reply.setUserName(accessUser.getUserName());
                        reply.setIp(IpAddress.getClientIpAddress(request));
                        reply.setContent(value);
                        reply.setTopicId(comment.getTopicId());
                    } else {
                        error.put("content", ErrorView._101.name());//内容不能为空
                    }
                } else {
                    error.put("reply", ErrorView._105.name());//评论不存在
                }
            } else {
                error.put("content", ErrorView._101.name());//内容不能为空
            }
        }
        //保存
        if (error.size() == 0) {
            //保存回复
            commentService.saveReply(reply);

            PointLog pointLog = new PointLog();
            pointLog.setId(pointManage.createPointLogId(accessUser.getUserId()));
            pointLog.setModule(300);//300.回复
            pointLog.setParameterId(comment.getId());//参数Id
            pointLog.setOperationUserType(2);//操作用户类型  0:系统  1: 员工  2:会员
            pointLog.setOperationUserName(accessUser.getUserName());//操作用户名称

            pointLog.setPoint(systemSetting.getReply_rewardPoint());//积分
            pointLog.setUserName(accessUser.getUserName());//用户名称
            pointLog.setRemark("");

            //增加用户积分
            userService.addUserPoint(accessUser.getUserName(), systemSetting.getReply_rewardPoint(), pointManage.createPointLogObject(pointLog));

            //用户动态
            UserDynamic userDynamic = new UserDynamic();
            userDynamic.setId(userDynamicManage.createUserDynamicId(accessUser.getUserId()));
            userDynamic.setUserName(accessUser.getUserName());
            userDynamic.setModule(400);//模块 400.回复
            userDynamic.setTopicId(reply.getTopicId());
            userDynamic.setCommentId(reply.getCommentId());
            userDynamic.setReplyId(reply.getId());
            userDynamic.setPostTime(reply.getPostTime());
            userDynamic.setStatus(reply.getStatus());
            userDynamic.setFunctionIdGroup("," + reply.getTopicId() + "," + reply.getCommentId() + "," + reply.getId() + ",");
            Object new_userDynamic = userDynamicManage.createUserDynamicObject(userDynamic);
            userService.saveUserDynamic(new_userDynamic);

            //删除缓存
            userManage.delete_cache_findUserById(accessUser.getUserId());
            userManage.delete_cache_findUserByUserName(accessUser.getUserName());
            followManage.delete_cache_userUpdateFlag(accessUser.getUserName());

            //修改话题最后回复时间
            topicService.updateTopicReplyTime(comment.getTopicId(), new Date());

            if (topic != null && !topic.getIsStaff()) {
                User _user = userManage.query_cache_findUserByUserName(topic.getUserName());
                //别人回复了我的话题
                if (_user != null && !_user.getId().equals(accessUser.getUserId())) {//楼主回复不发提醒给自己
                    //如果别人回复了话题发布者的评论，则不发本类型提醒给话题发布者
                    if (!comment.getUserName().equals(topic.getUserName())) {
                        Remind remind = new Remind();
                        remind.setId(remindManage.createRemindId(_user.getId()));
                        remind.setReceiverUserId(_user.getId());//接收提醒用户Id
                        remind.setSenderUserId(accessUser.getUserId());//发送用户Id
                        remind.setTypeCode(REPLY_MY_TOPIC.getCode());//20:别人回复了我的话题
                        remind.setSendTimeFormat(postTime.getTime());//发送时间格式化
                        remind.setTopicId(comment.getTopicId());//话题Id
                        remind.setFriendTopicCommentId(comment.getId());//对方的话题评论Id
                        remind.setFriendTopicReplyId(reply.getId());//对方的话题回复Id

                        Object remind_object = remindManage.createRemindObject(remind);
                        remindService.saveRemind(remind_object);
                        Optional.ofNullable(_user).filter(User::getEmailRemind).ifPresent(o -> EmailRemindService.addEmailRemind(new EmailRemind(reply.getId(), accessUser.getUserId(), accessUser.getUserName(), REPLY_MY_TOPIC.getCode(), Optional.of(o))));

                        //删除提醒缓存
                        remindManage.delete_cache_findUnreadRemindByUserId(_user.getId());
                    }
                }
            }

            User _user = userManage.query_cache_findUserByUserName(comment.getUserName());
            //别人回复了我的评论
            if (!comment.getIsStaff() && _user != null && !_user.getId().equals(accessUser.getUserId())) {//不发提醒给自己
                Remind remind = new Remind();
                remind.setId(remindManage.createRemindId(_user.getId()));
                remind.setReceiverUserId(_user.getId());//接收提醒用户Id
                remind.setSenderUserId(accessUser.getUserId());//发送用户Id
                remind.setTypeCode(REPLY_MY_COMMENT.getCode());//40:别人回复了我的评论
                remind.setSendTimeFormat(postTime.getTime());//发送时间格式化
                remind.setTopicId(comment.getTopicId());//话题Id
                remind.setTopicCommentId(comment.getId());//我的话题评论Id
                remind.setFriendTopicReplyId(reply.getId());//对方的话题回复Id

                Object remind_object = remindManage.createRemindObject(remind);
                remindService.saveRemind(remind_object);
                Optional.ofNullable(_user).filter(User::getEmailRemind).ifPresent(o -> EmailRemindService.addEmailRemind(new EmailRemind(reply.getId(), accessUser.getUserId(), accessUser.getUserName(), REPLY_MY_COMMENT.getCode(), Optional.of(o))));

                //删除提醒缓存
                remindManage.delete_cache_findUnreadRemindByUserId(_user.getId());
            }

            Set<String> userNameList = new HashSet<String>();
            List<Reply> replyList = commentService.findReplyByCommentId(comment.getId());
            if (replyList != null && replyList.size() > 0) {
                for (Reply _reply : replyList) {
                    //如果是话题发布者的回复，则不发本类型提醒给话题的发布者
                    if (topic != null && !topic.getIsStaff() && _reply.getUserName().equals(topic.getUserName())) {
                        continue;
                    }
                    //如果是评论发布者的回复，则不发本类型提醒给评论的发布者
                    if (!comment.getIsStaff() && _reply.getUserName().equals(comment.getUserName())) {
                        continue;
                    }
                    //员工的回复不发提醒
                    if (_reply.getIsStaff()) {
                        continue;
                    }

                    //如果是自己的回复，则不发本类型提醒给自己
                    if (_reply.getUserName().equals(accessUser.getUserName())) {
                        continue;
                    }

                    //如果同一个用户有多条回复,只发一条提醒
                    if (userNameList.contains(_reply.getUserName())) {
                        continue;
                    }

                    userNameList.add(_reply.getUserName());

                    _user = userManage.query_cache_findUserByUserName(_reply.getUserName());

                    //提醒
                    Remind remind = new Remind();
                    remind.setId(remindManage.createRemindId(_user.getId()));
                    remind.setReceiverUserId(_user.getId());//接收提醒用户Id
                    remind.setSenderUserId(accessUser.getUserId());//发送用户Id
                    remind.setTypeCode(REPLY_MY_REPLY_COMMENT.getCode());//50:别人回复了我回复过的评论
                    remind.setSendTimeFormat(postTime.getTime());//发送时间格式化
                    remind.setTopicId(_reply.getTopicId());//话题Id
                    remind.setTopicReplyId(_reply.getId());//我的话题回复Id

                    remind.setFriendTopicCommentId(comment.getId());//对方的话题评论Id
                    remind.setFriendTopicReplyId(reply.getId());//对方的话题回复Id


                    Object remind_object = remindManage.createRemindObject(remind);
                    remindService.saveRemind(remind_object);
                    Optional.ofNullable(_user).filter(User::getEmailRemind).ifPresent(o -> EmailRemindService.addEmailRemind(new EmailRemind(reply.getId(), accessUser.getUserId(), accessUser.getUserName(), REPLY_MY_REPLY_COMMENT.getCode(), Optional.of(o))));

                    //删除提醒缓存
                    remindManage.delete_cache_findUnreadRemindByUserId(_user.getId());
                }
            }
            EmailRemindService.addEmailRemind(new EmailRemind(reply.getId(), accessUser.getUserId(), accessUser.getUserName(), IDOL_REPLY.getCode(), Optional.empty()));

            //统计每分钟原来提交次数
            Integer original = settingManage.getSubmitQuantity("comment", accessUser.getUserName());
            if (original != null) {
                settingManage.addSubmitQuantity("comment", accessUser.getUserName(), original + 1);//刷新每分钟原来提交次数
            } else {
                settingManage.addSubmitQuantity("comment", accessUser.getUserName(), 1);//刷新每分钟原来提交次数
            }
        }

        Map<String, String> returnError = new HashMap<String, String>();//错误
        if (error.size() > 0) {
            //将枚举数据转为错误提示字符
            for (Map.Entry<String, String> entry : error.entrySet()) {
                if (ErrorView.get(entry.getValue()) != null) {
                    returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                } else {
                    returnError.put(entry.getKey(), entry.getValue());
                }
            }
        }
        if (isAjax == true) {
            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

            if (error != null && error.size() > 0) {
                returnValue.put("success", "false");
                returnValue.put("error", returnError);
            } else {
                returnValue.put("success", "true");
            }
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {
            if (error != null && error.size() > 0) {//如果有错误
                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addFlashAttribute("reply", reply);


                String referer = request.getHeader("referer");


                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";
                return "redirect:/" + referer + queryString;

            }


            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());

                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "保存回复成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);
                String dirName = templateService.findTemplateDir_cache();

                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String editReply(ModelMap model, Long replyId, String content,
                            String token, String captchaKey, String captchaValue, String jumpUrl,
                            RedirectAttributes redirectAttrs,
                            HttpServletRequest request, HttpServletResponse response) throws Exception {
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据
        Map<String, String> error = new HashMap<String, String>();

        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getCloseSite().equals(2)) {
            error.put("reply", ErrorView._21.name());//只读模式不允许提交数据
        }

        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token, error);

        //验证码

        Topic topic = null;
        Reply reply = null;
        //旧状态
        Integer old_status = -1;

        if (replyId != null && replyId > 0L) {
            reply = commentService.findReplyByReplyId(replyId);
            if (reply != null) {
                topic = topicService.findById(reply.getTopicId());
                if (topic != null) {
                    if (!reply.getUserName().equals(accessUser.getUserName())) {
                        error.put("reply", ErrorView._122.name());//只允许修改自己发布的回复
                    }

                    //是否有当前功能操作权限
                    boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1014000, topic.getTagId());
                    if (flag_permission == false) {
                        if (isAjax == true) {
                            response.setStatus(403);//设置状态码

                            WebUtil.writeToWeb("", "json", response);
                            return null;
                        } else {
                            String dirName = templateService.findTemplateDir_cache();

                            String accessPath = accessSourceDeviceManage.accessDevices(request);
                            request.setAttribute("message", "权限不足");
                            return "/templates/" + dirName + "/" + accessPath + "/message";
                        }
                    }

                    //如果全局不允许提交评论
                    if (systemSetting.isAllowComment() == false) {
                        error.put("reply", ErrorView._123.name());//禁止回复
                    }

                    //如果实名用户才允许提交评论
                    if (systemSetting.isRealNameUserAllowComment() == true) {
                        User _user = userManage.query_cache_findUserByUserName(accessUser.getUserName());
                        if (_user.isRealNameAuthentication() == false) {
                            error.put("reply", ErrorView._124.name());//实名用户才允许提交回复
                        }
                    }
                    old_status = reply.getStatus();
                } else {
                    error.put("reply", ErrorView._107.name());//话题不存在
                }
            } else {
                error.put("reply", ErrorView._125.name());//回复不存在
            }
        } else {
            error.put("reply", ErrorView._126.name());//回复Id不能为空
        }

        if (error.size() == 0) {
            if (reply.getStatus().equals(COMMENT_STATUS_RELEASE)) {//如果已发布，则重新执行发贴审核逻辑
                //前台发表评论审核状态
                if (systemSetting.getReply_review().equals(10)) {//10.全部审核 20.特权会员未触发敏感词免审核(未实现) 30.特权会员免审核 40.触发敏感词需审核(未实现) 50.无需审核
                    reply.setStatus(10);//10.待审核
                } else if (systemSetting.getReply_review().equals(30)) {
                    if (topic.getTagId() != null && topic.getTagId() > 0L) {
                        //是否有当前功能操作权限
                        boolean _flag_permission = userRoleManage.isPermission(ResourceEnum._1016000, topic.getTagId());
                        if (_flag_permission) {
                            reply.setStatus(COMMENT_STATUS_RELEASE);//20.已发布
                        } else {
                            reply.setStatus(10);//10.待审核
                        }
                    }
                } else {
                    reply.setStatus(COMMENT_STATUS_RELEASE);//20.已发布
                }
            }

            if (content != null && !"".equals(content.trim())) {
                //过滤标签
                content = textFilterManage.filterTag(request, content, settingManage.readEditorTag());
                Object[] object = textFilterManage.filterHtml(request, content, "comment", settingManage.readEditorTag());
                String value = (String) object[0];
                boolean isImage = (Boolean) object[2];//是否含有图片
                boolean isMedia = (Boolean) object[6];//是否含有音视频
                //不含标签内容
                String text = textFilterManage.filterText(content);
                //清除空格&nbsp;
                String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();

                if (isImage || isMedia || (!"".equals(text.trim()) && !"".equals(trimSpace))) {
                    if (systemSetting.isAllowFilterWord()) {
                        String wordReplace = "";
                        if (systemSetting.getFilterWordReplace() != null) {
                            wordReplace = systemSetting.getFilterWordReplace();
                        }
                        value = sensitiveWordFilterManage.filterSensitiveWord(value, wordReplace);
                    }
                    reply.setContent(value);
                } else {
                    error.put("content", ErrorView._101.name());//内容不能为空
                }
            } else {
                error.put("content", ErrorView._101.name());//内容不能为空
            }
        }

        if (error.size() == 0) {
            //修改回复
            int i = commentService.updateReply(reply.getId(), reply.getContent(), reply.getUserName(), reply.getStatus(), new Date());

            if (i > 0 && !old_status.equals(reply.getStatus())) {
                User user = userManage.query_cache_findUserByUserName(reply.getUserName());
                if (user != null) {
                    //修改回复状态
                    userService.updateUserDynamicReplyStatus(user.getId(), reply.getUserName(), reply.getTopicId(), reply.getCommentId(), reply.getId(), reply.getStatus());
                }
            }

            if (i > 0) {
                //删除缓存
                commentManage.delete_cache_findReplyByReplyId(reply.getId());
            } else {
                error.put("reply", ErrorView._121.name());//修改回复失败
            }
            //统计每分钟原来提交次数
            Integer original = settingManage.getSubmitQuantity("comment", accessUser.getUserName());
            if (original != null) {
                settingManage.addSubmitQuantity("comment", accessUser.getUserName(), original + 1);//刷新每分钟原来提交次数
            } else {
                settingManage.addSubmitQuantity("comment", accessUser.getUserName(), 1);//刷新每分钟原来提交次数
            }
        }

        Map<String, String> returnError = new HashMap<String, String>();//错误
        if (error.size() > 0) {
            //将枚举数据转为错误提示字符
            for (Map.Entry<String, String> entry : error.entrySet()) {
                if (ErrorView.get(entry.getValue()) != null) {
                    returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                } else {
                    returnError.put(entry.getKey(), entry.getValue());
                }
            }
        }
        if (isAjax == true) {
            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

            if (error != null && error.size() > 0) {
                returnValue.put("success", "false");
                returnValue.put("error", returnError);
            } else {
                returnValue.put("success", "true");
            }
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {
            if (error != null && error.size() > 0) {//如果有错误
                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addFlashAttribute("reply", reply);

                String referer = request.getHeader("referer");

                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";

                return "redirect:/" + referer + queryString;
            }

            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());

                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "修改回复成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);

                String dirName = templateService.findTemplateDir_cache();


                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String deleteReply(ModelMap model, Long replyId,
                              String token, String jumpUrl,
                              RedirectAttributes redirectAttrs,
                              HttpServletRequest request, HttpServletResponse response) throws Exception {
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据
        Map<String, String> error = new HashMap<String, String>();

        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getCloseSite().equals(2)) {
            error.put("reply", ErrorView._21.name());//只读模式不允许提交数据
        }

        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token, error);

        Topic topic = null;
        Reply reply = null;

        if (replyId != null && replyId > 0L) {
            reply = commentService.findReplyByReplyId(replyId);
            if (reply != null) {
                topic = topicService.findById(reply.getTopicId());
                if (topic != null) {
                    if (!reply.getUserName().equals(accessUser.getUserName())) {
                        error.put("reply", ErrorView._144.name());//只允许删除自己发布的回复
                    }

                    if (reply.getStatus() > 100) {
                        error.put("reply", ErrorView._116.name());//回复已删除
                    }

                    //是否有当前功能操作权限
                    boolean flag_permission = userRoleManage.isPermission(ResourceEnum._1015000, topic.getTagId());
                    if (flag_permission == false) {
                        if (isAjax == true) {
                            response.setStatus(403);//设置状态码

                            WebUtil.writeToWeb("", "json", response);
                            return null;
                        } else {
                            String dirName = templateService.findTemplateDir_cache();

                            String accessPath = accessSourceDeviceManage.accessDevices(request);
                            request.setAttribute("message", "权限不足");
                            return "/templates/" + dirName + "/" + accessPath + "/message";
                        }
                    }
                } else {
                    error.put("reply", ErrorView._107.name());//话题不存在
                }
            } else {
                error.put("reply", ErrorView._125.name());//回复不存在
            }
        } else {
            error.put("reply", ErrorView._126.name());//回复Id不能为空
        }

        if (error.size() == 0) {
            Integer constant = 100;
            int i = commentService.markDeleteReply(reply.getId(), constant);

            if (i > 0) {
                User user = userManage.query_cache_findUserByUserName(reply.getUserName());
                if (user != null) {
                    //修改回复状态
                    userService.updateUserDynamicReplyStatus(user.getId(), reply.getUserName(), reply.getTopicId(), reply.getCommentId(), reply.getId(), reply.getStatus() + constant);
                }
            }

            if (i > 0) {
                //删除缓存
                commentManage.delete_cache_findReplyByReplyId(reply.getId());
            } else {
                error.put("reply", ErrorView._142.name());//删除回复失败
            }
        }

        Map<String, String> returnError = new HashMap<String, String>();//错误
        if (error.size() > 0) {
            //将枚举数据转为错误提示字符
            for (Map.Entry<String, String> entry : error.entrySet()) {
                if (ErrorView.get(entry.getValue()) != null) {
                    returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                } else {
                    returnError.put(entry.getKey(), entry.getValue());
                }
            }
        }
        if (isAjax == true) {

            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

            if (error != null && error.size() > 0) {
                returnValue.put("success", "false");
                returnValue.put("error", returnError);

            } else {
                returnValue.put("success", "true");
            }
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {
            if (error != null && error.size() > 0) {//如果有错误

                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addFlashAttribute("reply", reply);

                String referer = request.getHeader("referer");

                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";

                return "redirect:/" + referer + queryString;
            }

            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());

                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "删除回复成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);

                String dirName = templateService.findTemplateDir_cache();


                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Long addByApi(CommentApi commentApi, HttpServletRequest request) {
        Topic topic = operationTopicService.getSponsorByOperationId(commentApi.getOperationId()).map(OperationTopic::getTopicId).map(topicService::findById).orElseThrow(() -> new CustomException(C_2_0007_0048, "operationId"));

        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();
        Date nowDate = new Date();
        Comment comment = new Comment();
        comment.setPostTime(nowDate);
        comment.setTopicId(topic.getId());
        comment.setStatus(COMMENT_STATUS_RELEASE);//20.已发布
        comment.setIsStaff(Boolean.FALSE);
        comment.setIp(IpAddress.getClientIpAddress(request));
        comment.setUserName(accessUser.getUserName());
        comment.setTotalLikeNum(0);

        String content = Optional.ofNullable(commentApi.getContent()).map(String::trim).filter(Strings::isNotEmpty).map(o -> textFilterManage.filterTag(request, o, settingManage.readEditorTag())).orElseThrow(() -> new CustomException(C_2_0008_0001, "content"));
        Object[] object = textFilterManage.filterHtml(request, content, "comment", settingManage.readEditorTag());
        String value = (String) object[0];
        boolean isImage = (Boolean) object[2];//是否含有图片
        boolean isMedia = (Boolean) object[6];//是否含有音视频

        Optional.of(content).map(textFilterManage::filterText).map(cms.utils.StringUtil::replaceSpace).map(String::trim).filter(o -> isImage || isMedia || Strings.isNotEmpty(o)).map(o -> {
            comment.setContent(sensitiveWordFilterManage.filterSensitiveWord(value, Optional.ofNullable(systemSetting.getFilterWordReplace()).orElse(Strings.EMPTY)));
            return "";
        }).orElseThrow(() -> new CustomException(C_2_0008_0001, "trimSpace"));
        this.add(topic, comment, accessUser, systemSetting, nowDate);
        return comment.getId();
    }

    private void add(Topic topic, Comment comment, AccessUser accessUser, SystemSetting systemSetting, Date nowDate) {
        commentService.saveComment(comment);

        PointLog pointLog = new PointLog();
        pointLog.setId(pointManage.createPointLogId(accessUser.getUserId()));
        pointLog.setModule(POINT_MODULE_COMMENT);//200.评论
        pointLog.setParameterId(comment.getId());//参数Id
        pointLog.setOperationUserType(POINT_OPERATION_USER_TYPE_uSER);//操作用户类型  0:系统  1: 员工  2:会员
        pointLog.setOperationUserName(accessUser.getUserName());//操作用户名称
        pointLog.setPoint(systemSetting.getComment_rewardPoint());//积分
        pointLog.setUserName(accessUser.getUserName());//用户名称
        pointLog.setRemark(Strings.EMPTY);
        //增加用户积分
        userService.addUserPoint(accessUser.getUserName(), systemSetting.getComment_rewardPoint(), pointManage.createPointLogObject(pointLog));

        //用户动态
        UserDynamic userDynamic = new UserDynamic();
        userDynamic.setId(userDynamicManage.createUserDynamicId(accessUser.getUserId()));
        userDynamic.setUserName(accessUser.getUserName());
        userDynamic.setModule(200);//模块 200.评论
        userDynamic.setTopicId(topic.getId());
        userDynamic.setCommentId(comment.getId());
        userDynamic.setPostTime(nowDate);
        userDynamic.setStatus(comment.getStatus());
        userDynamic.setFunctionIdGroup("," + topic.getId() + "," + comment.getId() + ",");
        Object new_userDynamic = userDynamicManage.createUserDynamicObject(userDynamic);
        userService.saveUserDynamic(new_userDynamic);


        //删除缓存
        userManage.delete_cache_findUserById(accessUser.getUserId());
        userManage.delete_cache_findUserByUserName(accessUser.getUserName());
        topicManage.delete_cache_findWhetherCommentTopic(comment.getTopicId(), accessUser.getUserName());
        topicManage.deleteTopicCache(topic.getId());

        followManage.delete_cache_userUpdateFlag(accessUser.getUserName());

        //修改话题最后回复时间
        topicService.updateTopicReplyTime(topic.getId(), new Date());

        if (!topic.getIsStaff()) {
            User _user = userManage.query_cache_findUserByUserName(topic.getUserName());
            if (_user != null && !_user.getId().equals(accessUser.getUserId())) {//楼主评论不发提醒给自己

                //提醒楼主
                Remind remind = new Remind();
                remind.setId(remindManage.createRemindId(_user.getId()));
                remind.setReceiverUserId(_user.getId());//接收提醒用户Id
                remind.setSenderUserId(accessUser.getUserId());//发送用户Id
                remind.setTypeCode(COMMENT_MY_TOPIC.getCode());//10:别人评论了我的话题
                remind.setSendTimeFormat(nowDate.getTime());//发送时间格式化
                remind.setTopicId(topic.getId());//话题Id
                remind.setFriendTopicCommentId(comment.getId());//对方的话题评论Id

                Object remind_object = remindManage.createRemindObject(remind);
                remindService.saveRemind(remind_object);
                Optional.ofNullable(_user).filter(User::getEmailRemind).ifPresent(o -> EmailRemindService.addEmailRemind(new EmailRemind(comment.getId(), accessUser.getUserId(), accessUser.getUserName(), COMMENT_MY_TOPIC.getCode(), Optional.of(o))));

                //删除提醒缓存
                remindManage.delete_cache_findUnreadRemindByUserId(_user.getId());
            }
        }
        EmailRemindService.addEmailRemind(new EmailRemind(comment.getId(), accessUser.getUserId(), accessUser.getUserName(), IDOL_COMMENT.getCode(), Optional.empty()));
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void likeLock(Long commentId, String token, HttpServletRequest request) {
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();
        Optional.ofNullable(accessUser).filter(o -> null == lockMap.putIfAbsent(o.getUserId(), new Object())).ifPresent(o -> {
            try {
                this.like(commentId, token, o, request);
            } finally {
                lockMap.remove(o.getUserId());
            }
        });
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteLikeLock(Long commentId, String token, HttpServletRequest request) {
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();
        Optional.ofNullable(accessUser).filter(o -> null == lockMap.putIfAbsent(o.getUserId(), new Object())).ifPresent(o -> {
            try {
                this.deleteLike(commentId, token, o, request);
            } finally {
                lockMap.remove(o.getUserId());
            }
        });
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void likeReplyLock(Long replyId, String token, HttpServletRequest request) {
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();
        Optional.ofNullable(accessUser).filter(o -> null == lockMap.putIfAbsent(o.getUserId(), new Object())).ifPresent(o -> {
            try {
                this.likeReplyLock(replyId, token, o, request);
            } finally {
                lockMap.remove(o.getUserId());
            }
        });
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteReplyLikeLock(Long replyId, String token, HttpServletRequest request) {
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();
        Optional.ofNullable(accessUser).filter(o -> null == lockMap.putIfAbsent(o.getUserId(), new Object())).ifPresent(o -> {
            try {
                this.deleteReplyLike(replyId, token, o, request);
            } finally {
                lockMap.remove(o.getUserId());
            }
        });
    }

    private void like(Long commentId, String token, AccessUser accessUser, HttpServletRequest request) {
        likeCommonCheck(token, request);
        Comment comment = Optional.ofNullable(commentId).map(commentManage::query_cache_findByCommentId).orElseThrow(() -> new CustomException(C_2_0007_0005, "commentId"));
        String commentLikeId = likeManage.createCommonLikeId(comment.getId(), accessUser.getUserId());
        Optional.ofNullable(null == likeManage.query_cache_findCommentLikeById(commentLikeId)).filter(o -> o).orElseThrow(() -> new CustomException(C_2_2001_0102, "comment"));

        Date time = new Date();
        Like like = new Like();
        like.setId(likeManage.createLikeId(accessUser.getUserId()));
        like.setAddtime(time);
        like.setTopicId(commentId);
        like.setLikeType(LikeType.COMMENT.getValue());
        like.setUserName(accessUser.getUserName());
        like.setPostUserName(comment.getUserName());

        CommentLike commentLike = new CommentLike();
        commentLike.setId(commentLikeId);
        commentLike.setAddtime(time);
        commentLike.setCommentId(commentId);
        commentLike.setUserName(accessUser.getUserName());
        commentLike.setPostUserName(comment.getUserName());

        //保存点赞
        likeService.saveLike(likeManage.createLikeObject(like), commentLikeService.createCommentLikeObject(commentLike));
        commentService.addTotalLikeNum(commentId);
        likeManage.delete_cache_findCommentLikeById(commentLikeId);
    }

    private void deleteLike(Long commentId, String token, AccessUser accessUser, HttpServletRequest request) {
        likeCommonCheck(token, request);

        Like like = likeService.getLikeByOtherId(commentId, LikeType.COMMENT).orElseThrow(() -> new CustomException(C_2_2001_0107, "comment"));
        String commentLikeId = likeManage.createCommonLikeId(commentId, accessUser.getUserId());

        Optional.ofNullable(likeService.deleteLikeByComment(like.getId(), commentLikeId)).filter(i -> i > 0).orElseThrow(() -> new CustomException(C_2_2001_0106, "deleteLike"));
        commentService.subTotalLikeNum(commentId);
        likeManage.delete_cache_findCommentLikeById(commentLikeId);
    }

    public void likeReplyLock(Long replyId, String token, AccessUser accessUser, HttpServletRequest request) {
        likeCommonCheck(token, request);
        Reply reply = Optional.ofNullable(replyId).map(commentService::findReplyByReplyId).orElseThrow(() -> new CustomException(C_2_0007_0023, "reply"));
        String replyLikeId = likeManage.createCommonLikeId(reply.getId(), accessUser.getUserId());
        Optional.ofNullable(null == likeManage.query_cache_findReplyLikeById(replyLikeId)).filter(o -> o).orElseThrow(() -> new CustomException(C_2_2001_0102, "like"));

        Date time = new Date();
        Like like = new Like();
        like.setId(likeManage.createLikeId(accessUser.getUserId()));
        like.setAddtime(time);
        like.setTopicId(replyId);
        like.setLikeType(LikeType.REPLY.getValue());
        like.setUserName(accessUser.getUserName());
        like.setPostUserName(reply.getUserName());

        ReplyLike replyLike = new ReplyLike();
        replyLike.setId(replyLikeId);
        replyLike.setAddtime(time);
        replyLike.setReplyId(replyId);
        replyLike.setUserName(accessUser.getUserName());
        replyLike.setPostUserName(reply.getUserName());

        //保存点赞
        likeService.saveLike(likeManage.createLikeObject(like), replyLikeService.createReplyLikeObject(replyLike));
        commentService.addReplyTotalLikeNum(replyId);
        likeManage.delete_cache_findReplyLikeById(replyLikeId);
    }

    private void deleteReplyLike(Long replyId, String token, AccessUser accessUser, HttpServletRequest request) {
        likeCommonCheck(token, request);

        Like like = likeService.getLikeByOtherId(replyId, LikeType.REPLY).orElseThrow(() -> new CustomException(C_2_2001_0107, "like"));
        String commentLikeId = likeManage.createCommonLikeId(replyId, accessUser.getUserId());

        Optional.ofNullable(likeService.deleteLikeByReply(like.getId(), commentLikeId)).filter(i -> i > 0).orElseThrow(() -> new CustomException(C_2_2001_0106, "deleteLike"));
        commentService.subReplyTotalLikeNum(replyId);
        likeManage.delete_cache_findReplyLikeById(commentLikeId);
    }

    public void likeCommonCheck(String token, HttpServletRequest request) {
        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getCloseSite().equals(CLOSE_SITE_ONLY_READ)) {
            throw new CustomException(C_2_0001_0001, "like");
        }
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
    }
}
