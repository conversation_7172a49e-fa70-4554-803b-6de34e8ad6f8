package cms.service.topic.impl;

import cms.bean.topic.Tag;
import cms.constant.ErrorCode;
import cms.handle.CustomException;
import cms.service.besa.DaoSupport;
import cms.service.topic.TagService;
import cms.web.action.topic.TopicManage;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Maps;
import org.apache.commons.compress.utils.Lists;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * 标签
 */
@Service
@Transactional
public class TagServiceBean extends DaoSupport<Tag> implements TagService {

    private static ConcurrentMap<Long, Boolean> upLock = Maps.newConcurrentMap();

    /**
     * 根据Id查询标签
     *
     * @param tagId 标签Id
     * @return
     */
    @Override
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    public Tag findById(Long tagId) {
        Query query = em.createQuery("select o from Tag o where o.id=?1")
                .setParameter(1, tagId);
        List<Tag> list = query.getResultList();
        for (Tag p : list) {
            return p;
        }
        return null;
    }

    /**
     * 查询所有标签
     *
     * @return
     */
    @Override
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    public List<Tag> findAllTag() {
        Query query = em.createQuery("select o from Tag o order by o.sort desc");
        return query.getResultList();
    }

    @Override
    public List<Tag> findTagByParentId(Long tagId) {
        Query query = em.createQuery("select o from Tag o where o.parentId = ?1 order by o.sort desc").setParameter(1, tagId);
        return query.getResultList();
    }

    @Override
    public List<Tag> findTagByParentIdAndName(Long tagId, String name) {
        Query query = em.createQuery("select o from Tag o where o.parentId = ?1 and o.name = ?2")
                .setParameter(1, tagId)
                .setParameter(2, name);
        return query.getResultList();
    }

    @Override
    public int findTwoTagCountTopic(String twoTagName) {
        Query query = em.createQuery("select count(o.id) from Topic o where o.status = 20 and exists(select t1.id from TopicTagAssociation t1 left join Tag t2 on t1.tagId = t2.id where t1.topicId = o.id and t2.name = ?1)").setParameter(1, twoTagName);
        return Optional.ofNullable(query.getSingleResult()).map(Object::toString).map(Integer::valueOf).orElse(0);
    }

    @Override
    public Map<Long, Integer> findTwoTagCountTopic() {
        Query query = em.createQuery("select count(o.id),b.tagId from Topic o " +
                "left join TopicTagAssociation b on o.id = b.topicId where o.status = 20 group by b.tagId");
        return (Map<Long, Integer>) Optional.ofNullable(query.getResultList()).filter(CollUtil::isNotEmpty).map(l -> l.stream().collect(Collectors.toMap(o -> (Long) ((Object[]) o)[1], o -> Integer.valueOf(((Object[]) o)[0].toString()), (a, b) -> a))).orElseGet(() -> Maps.newHashMap());
    }

    @Override
    public List<Map<String, Long>> findTagCountTopic(List<Long> tagIds) {
        Query query = em.createQuery("select count(o.id) as num,t1.tagId as tagId from Topic o left join TopicTagAssociation t1 on t1.topicId=o.id where o.status = 20 and t1.tagId in (:tagIds) group by t1.tagId").setParameter("tagIds", tagIds);
        return (List<Map<String, Long>>) Optional.ofNullable(query.getResultList()).map(l -> l.stream().map(o -> {
            Map<String, Long> map = Maps.newHashMap();
            map.put("tagId", (Long) ((Object[]) o)[1]);
            map.put("countNum", (Long) ((Object[]) o)[0]);
            return map;
        }).collect(Collectors.toList())).orElseGet(() -> Lists.newArrayList());

    }

    /**
     * 查询所有标签 - 缓存
     *
     * @return
     */
    @Override
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    @Cacheable(value = "findAllTag_cache", key = "'findAllTag_default'")
    public List<Tag> findAllTag_cache() {
        return this.findAllTag();
    }


    /**
     * 保存标签
     *
     * @param tag
     */
    @Override
    @Caching(evict = {@CacheEvict(value = "findAllTag_cache", allEntries = true),
            @CacheEvict(value = "api_topic_sys_config_cache", allEntries = true)})
    public void saveTag(Tag tag) {
        this.save(tag);
    }


    /**
     * 修改标签
     *
     * @param tag
     * @return
     */
    @Override
    @Caching(evict = {@CacheEvict(value = "findAllTag_cache", allEntries = true),
            @CacheEvict(value = "api_topic_sys_config_cache", allEntries = true)})
    @Transactional(propagation = Propagation.REQUIRED)
    public Integer updateTag(Tag tag) {
        if (null == upLock.putIfAbsent(tag.getId(), Boolean.TRUE)) {
            try {
                Tag oldTag = this.findById(tag.getId());
                Query query = em.createQuery("update Tag o set o.name=?1, o.sort=?2 where o.id=?3")
                        .setParameter(1, tag.getName())
                        .setParameter(2, tag.getSort())
                        .setParameter(3, tag.getId());
                int i = query.executeUpdate();
                Optional.of(i).filter(o -> o > 0).ifPresent(o -> TopicManage.updateNewTwoTagName(tag.getId(), oldTag.getName(), tag.getName()));
                return i;
            } finally {
                upLock.remove(tag.getId());
            }
        }
        return null;
    }

    /**
     * 删除标签
     *
     * @param tagId 标签Id
     */
    @Override
    @Caching(evict = {@CacheEvict(value = "findAllTag_cache", allEntries = true),
            @CacheEvict(value = "api_topic_sys_config_cache", allEntries = true)})
    @Transactional(propagation = Propagation.REQUIRED)
    public Integer deleteTag(Long tagId) {
        if (null == upLock.putIfAbsent(tagId, Boolean.TRUE)) {
            try {
                Tag oldTag = this.findById(tagId);
                int i = 0;
                if (CollUtil.isNotEmpty(this.findTagByParentId(tagId))) {
                    throw new CustomException(ErrorCode.C_1_0002_0004, "tagId");
                }
                Query delete = em.createQuery("delete from Tag o where o.id=?1")
                        .setParameter(1, tagId);
                i = delete.executeUpdate();
                Optional.of(i).filter(o -> o > 0).ifPresent(o -> TopicManage.delTwoTagName(tagId, oldTag.getName()));
                return i;
            } finally {
                upLock.remove(tagId);
            }
        }
        return null;
    }

}
