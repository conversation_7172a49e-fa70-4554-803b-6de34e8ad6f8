package cms.service.topic.impl;

import cms.bean.topic.CommentSummary;
import cms.service.besa.DaoSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.persistence.Query;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/19 11:21
 */
@Service
@Slf4j
public class CommentSummaryService extends DaoSupport<CommentSummary> {

    public int addTotalLikeNum(long commentId) {
        int i = 0;
        Query query = em.createQuery("update CommentSummary o set o.totalLikeNum=o.totalLikeNum+1 where o.id=?1")
                .setParameter(1, commentId);
        i = query.executeUpdate();
        return i;
    }

    public int subTotalLikeNum(long commentId) {
        int i = 0;
        Query query = em.createQuery("update CommentSummary o set o.totalLikeNum=o.totalLikeNum-1 where o.id=?1")
                .setParameter(1, commentId);
        i = query.executeUpdate();
        return i;
    }
}
