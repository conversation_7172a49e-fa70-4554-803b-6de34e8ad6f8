package cms.service.topic.impl;

import cms.bean.PageForm;
import cms.bean.RequestResult;
import cms.bean.ResultCode;
import cms.bean.staff.SysUsers;
import cms.bean.topic.Comment;
import cms.bean.topic.Quote;
import cms.bean.topic.Reply;
import cms.bean.user.User;
import cms.service.aliyun.OssFileChangeService;
import cms.service.topic.CommentService;
import cms.service.topic.TopicService;
import cms.service.user.UserService;
import cms.utils.IpAddress;
import cms.utils.JsonUtils;
import cms.web.action.TextFilterManage;
import cms.web.action.fileSystem.FileManage;
import cms.web.action.setting.SettingManage;
import cms.web.action.topic.CommentManage;
import cms.web.action.topic.TopicManage;
import cms.web.action.user.UserManage;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/5 16:46
 */
@Service
@Slf4j
public class CommentManageService {

    @Resource
    CommentService commentService;//通过接口引用代理返回的对象
    @Resource
    SettingManage settingManage;
    @Resource
    CommentManage commentManage;

    @Resource
    TextFilterManage textFilterManage;

    @Resource
    TopicManage topicManage;
    @Resource
    TopicService topicService;
    @Resource
    FileManage fileManage;
    @Resource
    UserService userService;
    @Resource
    UserManage userManage;
    @Autowired
    private OssFileChangeService ossFileChangeService;

    @Transactional(propagation = Propagation.REQUIRED)
    public String add(ModelMap model, Long topicId, String content,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {

        Map<String, String> error = new HashMap<String, String>();
        if (topicId == null || topicId <= 0L) {
            error.put("topicId", "话题Id不能为空");
        }
        if (error.size() == 0 && content != null && !"".equals(content.trim())) {
            //过滤标签
            content = textFilterManage.filterTag(request, content, settingManage.readEditorTag());
            Object[] object = textFilterManage.filterHtml(request, content, "comment", settingManage.readEditorTag());
            String value = (String) object[0];
            List<String> imageNameList = (List<String>) object[1];
            boolean isImage = (Boolean) object[2];//是否含有图片
            boolean isMedia = (Boolean) object[6];//是否含有音视频
            //不含标签内容
            String text = textFilterManage.filterText(content);
            //清除空格&nbsp;
            String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();

            if (isImage == true || isMedia || (!"".equals(text.trim()) && !"".equals(trimSpace))) {
                String username = "";//用户名称
                String userId = "";//用户Id
                Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
                if (obj instanceof SysUsers) {
                    userId = ((SysUsers) obj).getUserId();
                    username = ((SysUsers) obj).getUserAccount();
                }

                Comment comment = new Comment();
                comment.setTopicId(topicId);
                comment.setContent(value);
                comment.setIsStaff(true);
                comment.setUserName(username);
                comment.setIp(IpAddress.getClientIpAddress(request));
                comment.setStatus(20);
                //保存评论
                commentService.saveComment(comment);

                //修改话题最后回复时间
                topicService.updateTopicReplyTime(topicId, new Date());
            }
        } else {
            error.put("content", "评论内容不能为空");
        }

        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String edit(PageForm pageForm, ModelMap model, Long commentId, String content, Integer status,
                       HttpServletRequest request, HttpServletResponse response) throws Exception {

        Comment comment = null;
        Integer old_status = -1;
        Map<String, String> error = new HashMap<String, String>();
        if (commentId == null || commentId <= 0) {
            error.put("commentId", "评论Id不能为空");
        } else {
            comment = commentService.findByCommentId(commentId);
        }

        if (status == null) {
            error.put("status", "状态参数不能为空");
        }

        if (error.size() == 0 && content != null && !"".equals(content.trim())) {
            if (comment != null) {
                old_status = comment.getStatus();
                comment.setStatus(status);

                //过滤标签
                content = textFilterManage.filterTag(request, content, settingManage.readEditorTag());
                Object[] object = textFilterManage.filterHtml(request, content, "comment", settingManage.readEditorTag());
                String value = (String) object[0];
                List<String> imageNameList = (List<String>) object[1];
                boolean isImage = (Boolean) object[2];//是否含有图片
                boolean isMedia = (Boolean) object[6];//是否含有音视频
                //不含标签内容
                String text = textFilterManage.filterText(content);
                //清除空格&nbsp;
                String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();

                if (isImage == true || isMedia || (!"".equals(text.trim()) && !"".equals(trimSpace))) {

                    //下级引用Id组
                    String lowerQuoteIdGroup = "," + comment.getId() + comment.getQuoteIdGroup();

                    String username = comment.getUserName();//用户名称

                    //修改评论
                    int i = commentService.updateComment(comment.getId(), value, status, new Date(), lowerQuoteIdGroup, username);


                    if (i > 0 && !old_status.equals(status)) {
                        User user = userManage.query_cache_findUserByUserName(comment.getUserName());
                        if (user != null) {
                            //修改评论状态
                            userService.updateUserDynamicCommentStatus(user.getId(), comment.getUserName(), comment.getTopicId(), comment.getId(), comment.getStatus());
                        }

                    }

                    //删除缓存
                    commentManage.delete_cache_findByCommentId(comment.getId());

                    //旧图片名称
                    ossFileChangeService.ossImgFileChange(comment.getContent(), "comment", imageNameList);
                } else {
                    error.put("content", "评论内容不能为空");
                }
            } else {
                error.put("commentId", "评论不能为空");
            }
        } else {
            error.put("content", "评论内容不能为空");
        }

        if (error.size() == 0) {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String delete(ModelMap model, Long[] commentId,
                         HttpServletRequest request, HttpServletResponse response) throws Exception {

        Map<String, String> error = new HashMap<String, String>();

        if (commentId != null && commentId.length > 0) {
            List<Long> commentIdList = new ArrayList<Long>();
            for (Long l : commentId) {
                if (l != null && l > 0L) {
                    commentIdList.add(l);
                }
            }
            if (commentIdList != null && commentIdList.size() > 0) {
                List<Comment> commentList = commentService.findByCommentIdList(commentIdList);
                if (commentList != null && commentList.size() > 0) {
                    for (Comment comment : commentList) {
                        if (comment.getStatus() < 100) {//标记删除
                            Integer constant = 100000;
                            int i = commentService.markDeleteComment(comment.getTopicId(), comment.getId(), constant);

                            if (i > 0) {
                                User user = userManage.query_cache_findUserByUserName(comment.getUserName());
                                if (user != null) {
                                    //修改评论状态
                                    userService.updateUserDynamicCommentStatus(user.getId(), comment.getUserName(), comment.getTopicId(), comment.getId(), comment.getStatus() + constant);
                                }
                                //删除缓存
                                commentManage.delete_cache_findByCommentId(comment.getId());
                                topicManage.deleteTopicCache(comment.getTopicId());//删除缓存
                            }

                        } else {//物理删除
                            int i = commentService.deleteComment(comment.getTopicId(), comment.getId());
                            if (i > 0) {
                                //根据评论Id删除用户动态(评论下的回复也同时删除)
                                userService.deleteUserDynamicByCommentId(comment.getTopicId(), comment.getId());

                                //删除缓存
                                commentManage.delete_cache_findByCommentId(comment.getId());

                                String fileNumber = topicManage.generateFileNumber(comment.getUserName(), comment.getIsStaff());

                                //删除图片
                                ossFileChangeService.ossImgFileChange(comment.getContent(), "comment", Lists.newArrayList());
                            }
                        }
                    }
                }
            }
        } else {
            error.put("commentId", "评论Id不能为空");
        }

        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String recoveryComment(ModelMap model, Long commentId,
                                  HttpServletRequest request, HttpServletResponse response) throws Exception {

        Map<String, String> error = new HashMap<String, String>();
        if (commentId != null && commentId > 0L) {
            Comment comment = commentService.findByCommentId(commentId);
            if (comment != null && comment.getStatus() > 100) {
                int originalState = this.parseInitialValue(comment.getStatus());
                int i = commentService.updateCommentStatus(commentId, originalState);
                if (i > 0) {
                    User user = userManage.query_cache_findUserByUserName(comment.getUserName());
                    if (user != null) {
                        //修改评论状态
                        userService.updateUserDynamicCommentStatus(user.getId(), comment.getUserName(), comment.getTopicId(), comment.getId(), originalState);
                    }
                }

                commentManage.delete_cache_findByCommentId(commentId);
                return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
            } else {
                error.put("commentId", "评论不存在或未标记删除");
            }
        } else {
            error.put("commentId", "评论Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String addQuote(ModelMap model, Long commentId, String content,
                           HttpServletRequest request, HttpServletResponse response) throws Exception {


        Comment comment = null;
        Map<String, String> error = new HashMap<String, String>();
        if (commentId == null || commentId <= 0) {
            error.put("commentId", "引用评论不能为空");
        } else {
            comment = commentService.findByCommentId(commentId);
        }


        if (content != null && !"".equals(content.trim())) {
            if (comment != null) {
                comment.setStatus(20);

                //过滤标签
                content = textFilterManage.filterTag(request, content, settingManage.readEditorTag());

                Object[] object = textFilterManage.filterHtml(request, content, "comment", settingManage.readEditorTag());
                String value = (String) object[0];
                List<String> imageNameList = (List<String>) object[1];
                boolean isImage = (Boolean) object[2];//是否含有图片
                //不含标签内容
                String text = textFilterManage.filterText(content);
                //清除空格&nbsp;
                String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();

                if (isImage == true || (!"".equals(text.trim()) && !"".equals(trimSpace))) {
                    String username = "";//用户名称
                    String userId = "";//用户Id
                    Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
                    if (obj instanceof SysUsers) {
                        userId = ((SysUsers) obj).getUserId();
                        username = ((SysUsers) obj).getUserAccount();
                    }
                    //旧引用
                    List<Quote> old_quoteList = new ArrayList<Quote>();
                    //旧引用Id组
                    String old_quoteId = "," + comment.getId() + comment.getQuoteIdGroup();
                    if (comment.getQuote() != null && !"".equals(comment.getQuote().trim())) {
                        //旧引用
                        old_quoteList = JsonUtils.toGenericObject(comment.getQuote(), new TypeReference<List<Quote>>() {
                        });
                    }

                    //自定义引用
                    Quote quote = new Quote();
                    quote.setCommentId(comment.getId());
                    quote.setIsStaff(comment.getIsStaff());
                    quote.setUserName(comment.getUserName());
                    quote.setContent(textFilterManage.filterText(textFilterManage.specifyHtmlTagToText(comment.getContent())));
                    old_quoteList.add(quote);

                    //自定义评论
                    Comment newComment = new Comment();
                    newComment.setTopicId(comment.getTopicId());
                    newComment.setContent(value);
                    newComment.setIsStaff(true);
                    newComment.setQuoteIdGroup(old_quoteId);
                    newComment.setUserName(username);
                    newComment.setIp(IpAddress.getClientIpAddress(request));
                    newComment.setQuote(JsonUtils.toJSONString(old_quoteList));
                    newComment.setStatus(20);
                    //保存评论
                    commentService.saveComment(newComment);

                    //修改话题最后回复时间
                    topicService.updateTopicReplyTime(comment.getTopicId(), new Date());
                } else {
                    error.put("content", "评论内容不能为空");
                }
            } else {
                error.put("commentId", "引用评论不能为空");
            }
        } else {
            error.put("content", "评论内容不能为空");
        }

        if (error.size() == 0) {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));

    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String auditComment(ModelMap model, Long commentId,
                               HttpServletResponse response) throws Exception {
        Map<String, String> error = new HashMap<String, String>();
        if (commentId != null && commentId > 0L) {
            int i = commentService.updateCommentStatus(commentId, 20);

            Comment comment = commentManage.query_cache_findByCommentId(commentId);
            if (i > 0 && comment != null) {
                User user = userManage.query_cache_findUserByUserName(comment.getUserName());
                if (user != null) {
                    //修改话题状态
                    userService.updateUserDynamicCommentStatus(user.getId(), comment.getUserName(), comment.getTopicId(), comment.getId(), 20);
                }
            }

            //删除缓存
            commentManage.delete_cache_findByCommentId(commentId);
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        } else {
            error.put("commentId", "评论Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String addReply(ModelMap model, Long commentId, String content,
                           HttpServletRequest request, HttpServletResponse response) throws Exception {
        Comment comment = null;
        Map<String, String> error = new HashMap<String, String>();
        if (commentId == null || commentId <= 0) {
            error.put("commentId", "评论Id不能为空");
        } else {
            comment = commentService.findByCommentId(commentId);
        }


        if (content != null && !"".equals(content.trim())) {
            if (comment != null) {
                //过滤标签
                content = textFilterManage.filterTag(request, content, settingManage.readEditorTag());
                Object[] object = textFilterManage.filterHtml(request, content, "comment", settingManage.readEditorTag());
                String value = (String) object[0];
                boolean isImage = (Boolean) object[2];//是否含有图片
                boolean isMedia = (Boolean) object[6];//是否含有音视频
                //不含标签内容
                String text = textFilterManage.filterText(content);
                //清除空格&nbsp;
                String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();

                if (isImage || isMedia || (!"".equals(text.trim()) && !"".equals(trimSpace))) {
                    String username = "";//用户名称

                    Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
                    if (obj instanceof UserDetails) {
                        username = ((UserDetails) obj).getUsername();
                    }


                    //回复
                    Reply reply = new Reply();
                    reply.setCommentId(comment.getId());
                    reply.setIsStaff(true);
                    reply.setUserName(username);
                    reply.setContent(value);
                    reply.setTopicId(comment.getTopicId());
                    reply.setStatus(20);
                    reply.setIp(IpAddress.getClientIpAddress(request));
                    //保存评论
                    commentService.saveReply(reply);


                    //修改话题最后回复时间
                    topicService.updateTopicReplyTime(comment.getTopicId(), new Date());
                } else {
                    error.put("content", "回复内容不能为空");

                }
            } else {
                error.put("commentId", "评论不存在");
            }
        } else {
            error.put("content", "回复内容不能为空");
        }

        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String editReply(ModelMap model, Long replyId, String content, Integer status,
                            HttpServletRequest request, HttpServletResponse response) throws Exception {


        Reply reply = null;
        Integer old_status = -1;
        Map<String, String> error = new HashMap<String, String>();
        if (replyId != null && replyId > 0) {
            reply = commentService.findReplyByReplyId(replyId);
        } else {
            error.put("replyId", "回复Id不能为空");

        }

        if (status == null) {
            error.put("status", "回复状态不能为空");
        }

        if (content != null && !"".equals(content.trim())) {
            if (reply != null) {
                old_status = reply.getStatus();
                reply.setStatus(status);

                //过滤标签
                content = textFilterManage.filterTag(request, content, settingManage.readEditorTag());
                Object[] object = textFilterManage.filterHtml(request, content, "comment", settingManage.readEditorTag());
                String value = (String) object[0];
                boolean isImage = (Boolean) object[2];//是否含有图片
                boolean isMedia = (Boolean) object[6];//是否含有音视频
                //不含标签内容
                String text = textFilterManage.filterText(content);
                //清除空格&nbsp;
                String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();

                if (isImage || isMedia || (!"".equals(text.trim()) && !"".equals(trimSpace))) {
                    String username = reply.getUserName();//用户名称
                    //修改回复
                    int i = commentService.updateReply(replyId, value, username, status, new Date());

                    if (i > 0 && !old_status.equals(status)) {
                        User user = userManage.query_cache_findUserByUserName(reply.getUserName());
                        if (user != null) {
                            //修改回复状态
                            userService.updateUserDynamicReplyStatus(user.getId(), reply.getUserName(), reply.getTopicId(), reply.getCommentId(), reply.getId(), reply.getStatus());
                        }

                    }


                    //删除缓存
                    commentManage.delete_cache_findReplyByReplyId(replyId);
                } else {
                    error.put("content", "回复内容不能为空");

                }
            } else {
                error.put("commentId", "回复不存在");
            }
        } else {
            error.put("content", "回复内容不能为空");
        }

        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String deleteReply(ModelMap model, Long[] replyId,
                              HttpServletRequest request, HttpServletResponse response) throws Exception {

        Map<String, String> error = new HashMap<String, String>();

        if (replyId != null && replyId.length > 0) {
            List<Long> replyIdList = new ArrayList<Long>();
            for (Long l : replyId) {
                if (l != null && l > 0L) {
                    replyIdList.add(l);
                }
            }
            if (replyIdList != null && replyIdList.size() > 0) {
                List<Reply> replyList = commentService.findByReplyIdList(replyIdList);
                if (replyList != null && replyList.size() > 0) {
                    for (Reply reply : replyList) {
                        if (reply.getStatus() < 100) {//标记删除
                            Integer constant = 100000;
                            int i = commentService.markDeleteReply(reply.getId(), constant);


                            if (i > 0) {
                                User user = userManage.query_cache_findUserByUserName(reply.getUserName());
                                if (user != null) {
                                    //修改回复状态
                                    userService.updateUserDynamicReplyStatus(user.getId(), reply.getUserName(), reply.getTopicId(), reply.getCommentId(), reply.getId(), reply.getStatus() + constant);
                                }
                                //删除缓存
                                commentManage.delete_cache_findReplyByReplyId(reply.getId());
                            }
                        } else {//物理删除
                            int i = commentService.deleteReply(reply.getId());
                            if (i > 0 && reply != null) {
                                User user = userManage.query_cache_findUserByUserName(reply.getUserName());
                                if (user != null) {
                                    userService.deleteUserDynamicByReplyId(user.getId(), reply.getTopicId(), reply.getCommentId(), reply.getId());
                                }
                            }

                            //删除缓存
                            commentManage.delete_cache_findReplyByReplyId(reply.getId());
                        }

                    }

                }

            }
        }


        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String recoveryReply(ModelMap model, Long replyId,
                                HttpServletRequest request, HttpServletResponse response) throws Exception {

        Map<String, String> error = new HashMap<String, String>();
        if (replyId != null && replyId > 0) {
            Reply reply = commentService.findReplyByReplyId(replyId);
            if (reply != null && reply.getStatus() > 100) {
                int originalState = this.parseInitialValue(reply.getStatus());
                int i = commentService.updateReplyStatus(replyId, originalState);

                User user = userManage.query_cache_findUserByUserName(reply.getUserName());
                if (i > 0 && user != null) {
                    //修改回复状态
                    userService.updateUserDynamicReplyStatus(user.getId(), reply.getUserName(), reply.getTopicId(), reply.getCommentId(), reply.getId(), originalState);
                }
                //删除缓存
                commentManage.delete_cache_findReplyByReplyId(replyId);

                return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
            } else {
                error.put("replyId", "回复不存在或未标记删除");
            }

        } else {
            error.put("replyId", "回复Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    private int parseInitialValue(Integer status) {
        int tens = status % 100 / 10;//十位%100/10
        int units = status % 10;//个位直接%10

        return Integer.parseInt(tens + "" + units);
    }
}
