package cms.service.keywordDict;

import cms.bean.keywordDict.KeywordDict;
import cms.bean.setting.SystemSetting;
import cms.constant.ErrorCode;
import cms.handle.CustomException;
import cms.service.question.AnswerService;
import cms.service.question.QuestionService;
import cms.service.setting.SettingService;
import cms.service.topic.CommentService;
import cms.service.topic.TopicService;
import cms.web.action.keywordDict.KeywordDictManageAction;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;

import static cms.constant.Constant.DATE_YMD_HMS;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/27 17:08
 */
@Service
@Slf4j
public class TextFilterKeywordService {

    private static final int MAX_RESULT = 200;
    private static final int WAIT_SECONDS = 60;

    @Autowired
    private KeywordDictService keywordDictService;
    @Autowired
    private TopicFilterKeyWordService topicFilterKeyWordService;
    @Autowired
    private QuestionFilterKeywordService questionFilterKeywordService;
    @Autowired
    private TopicService topicService;
    @Autowired
    private CommentService commentService;
    @Autowired
    private QuestionService questionService;
    @Autowired
    private AnswerService answerService;
    @Resource
    SettingService settingService;

    public void runFilterKeyWord() throws InterruptedException, CustomException {
        try {
            if (KeywordDictManageAction.semaphore.tryAcquire(WAIT_SECONDS, TimeUnit.SECONDS)) {
                this.filterKeyWord();
            } else {
                throw new CustomException(ErrorCode.C_1_0001_0012, "word");
            }
        } finally {
            KeywordDictManageAction.semaphore.release();
        }
    }

    private void filterKeyWord() {
        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (!systemSetting.isAllowFilterWord()) {
            return;
        }
        final Date date = keywordDictService.findAllKeyword_cache().stream().map(KeywordDict::getCreateTime).max(Comparator.comparing(o -> o)).map(o -> DateUtil.offsetMinute(o, 5)).orElse(null);
        if (null == date) {
            return;
        }
        String wordReplace = Optional.ofNullable(systemSetting.getFilterWordReplace()).orElse("***");

        String dateFormat = DateUtil.format(date, DATE_YMD_HMS);
        log.info(">>>>>>>> start topic filterKeyWord {} <<<<<<<<", dateFormat);
        filterKeyWord(o -> topicService.findIdByPageAndLtLastUpdateTime(date, o, MAX_RESULT), o -> topicFilterKeyWordService.filterKeyWord(o, wordReplace, date));
        log.info(">>>>>>>> end topic filterKeyWord {} <<<<<<<<", dateFormat);
        log.info(">>>>>>>> start comment filterKeyWord {} <<<<<<<<", dateFormat);
        filterKeyWord(o -> commentService.findIdByPageAndLtLastUpdateTime(date, o, MAX_RESULT), o -> topicFilterKeyWordService.filterKeyWordComment(o, wordReplace, date));
        log.info(">>>>>>>> end comment filterKeyWord {} <<<<<<<<", dateFormat);
        log.info(">>>>>>>> start reply filterKeyWord {} <<<<<<<<", dateFormat);
        filterKeyWord(o -> commentService.findReplyIdByPageAndLtLastUpdateTime(date, o, MAX_RESULT), o -> topicFilterKeyWordService.filterKeyWordReply(o, wordReplace, date));
        log.info(">>>>>>>> end reply filterKeyWord {} <<<<<<<<", dateFormat);
        log.info(">>>>>>>> start question filterKeyWord {} <<<<<<<<", dateFormat);
        filterKeyWord(o -> questionService.findQuestionIdByPageAndLtLastUpdateTime(date, o, MAX_RESULT), o -> questionFilterKeywordService.filterKeyWord(o, wordReplace, date));
        log.info(">>>>>>>> end question filterKeyWord {} <<<<<<<<", dateFormat);
        log.info(">>>>>>>> start answer filterKeyWord {} <<<<<<<<", dateFormat);
        filterKeyWord(o -> answerService.findIdByPageAndLtLastUpdateTime(date, o, MAX_RESULT), o -> questionFilterKeywordService.filterKeyWordAnswer(o, wordReplace, date));
        log.info(">>>>>>>> end answer filterKeyWord {} <<<<<<<<", dateFormat);
        log.info(">>>>>>>> start answerReply filterKeyWord {} <<<<<<<<", dateFormat);
        filterKeyWord(o -> answerService.findReplyIdByByPageAndLtLastUpdateTime(date, o, MAX_RESULT), o -> questionFilterKeywordService.filterKeyWordReply(o, wordReplace, date));
        log.info(">>>>>>>> end answerReply filterKeyWord {} <<<<<<<<", dateFormat);
    }

    private void filterKeyWord(Function<Integer, Optional<List<Long>>> getByIds, Consumer<Long> commonFilter) {
        int page = 1;
        while (true) {
            int firstIndex = (page - 1) * MAX_RESULT;
            boolean b = getByIds.apply(firstIndex).map(l -> l.stream().map(o -> {
                try {
                    commonFilter.accept(o);
                } catch (Exception e) {
                    log.error(String.format("%s %s errpr!", commonFilter.toString(), o), e);
                }
                return true;
            }).count() == l.size()).orElse(false);
            if (!b) {
                break;
            }
            page++;
        }
    }
}
