package cms.web.action.upgrade.impl;

import cms.bean.upgrade.UpgradeLog;
import cms.bean.upgrade.UpgradeSystem;
import cms.service.upgrade.UpgradeService;
import cms.utils.JsonUtils;
import cms.utils.SpringConfigTool;
import cms.web.action.upgrade.UpgradeManage;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Date;

/**
 * 5.6升级到5.7版本执行程序
 */
public class Upgrade5_6to5_7 {
    private static final Logger logger = LogManager.getLogger(Upgrade5_6to5_7.class);


    /**
     * 运行
     *
     * @param upgradeId 升级Id
     */
    public static void run(String upgradeId) {
        UpgradeService upgradeService = (UpgradeService) SpringConfigTool.getContext().getBean("upgradeServiceBean");
        UpgradeManage upgradeManage = (UpgradeManage) SpringConfigTool.getContext().getBean("upgradeManage");

        for (int i = 0; i < 100; i++) {
            upgradeManage.taskRunMark_delete();
            upgradeManage.taskRunMark_add(1L);
            UpgradeSystem upgradeSystem = upgradeService.findUpgradeSystemById(upgradeId);
            if (upgradeSystem == null || upgradeSystem.getRunningStatus().equals(9999)) {
                break;
            }
            if (upgradeSystem.getRunningStatus() >= 40 && upgradeSystem.getRunningStatus() < 200) {


                //更改运行状态
                upgradeService.updateRunningStatus(upgradeId, 200, JsonUtils.toJSONString(new UpgradeLog(new Date(), "升级流程结束", 1)) + ",");

            }


            if (upgradeSystem.getRunningStatus() >= 200 && upgradeSystem.getRunningStatus() < 9999) {
                //更改运行状态
                upgradeService.updateRunningStatus(upgradeId, 9999, JsonUtils.toJSONString(new UpgradeLog(new Date(), "升级完成", 1)) + ",");

            }


        }
        upgradeManage.taskRunMark_delete();
    }

}
