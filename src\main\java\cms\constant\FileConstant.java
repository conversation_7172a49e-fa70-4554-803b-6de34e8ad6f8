package cms.constant;

import cms.bean.setting.EditorTag;
import cms.bean.user.ResourceEnum;
import cms.utils.CommentedProperties;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/6 14:21
 */
public interface FileConstant {
    Integer SIZE_SPLIT = 1024;
    String FILE_TYPE_IMG = "image";
    String FILE_TYPE_FLASH = "flash";
    String FILE_TYPE_MEDIA = "media";
    String FILE_TYPE_FILE = "file";
    String MEDIA_SUFFIX_MOV = "mov";

    static List<String> getUploadTypeMap(String type) {
        switch (type) {
            case FILE_TYPE_IMG:
                return CommentedProperties.readRichTextAllowImageUploadFormat();
            case FILE_TYPE_FLASH:
                List<String> flashFormatList = new ArrayList<String>();
                flashFormatList.add("swf");
                return flashFormatList;
            case FILE_TYPE_MEDIA:
                return Optional.ofNullable(CommentedProperties.readRichTextAllowVideoUploadFormat()).map(o -> {
                    o.add(MEDIA_SUFFIX_MOV);
                    return o;
                }).orElse(null);
            case FILE_TYPE_FILE:
                return CommentedProperties.readRichTextAllowFileUploadFormat();
        }
        return null;
    }

    static List<String> getUploadTypeMap(EditorTag editorTag, String type) {
        switch (type) {
            case FILE_TYPE_IMG:
                return editorTag.getImageFormat();
            case FILE_TYPE_MEDIA:
                return Optional.ofNullable(editorTag.getVideoFormat()).map(o -> {
                    o.add(MEDIA_SUFFIX_MOV);
                    return o;
                }).orElse(null);
            case FILE_TYPE_FILE:
                return editorTag.getFileFormat();
        }
        return null;
    }

    static boolean isUpload(EditorTag editorTag, String type) {
        switch (type) {
            case FILE_TYPE_IMG:
                return editorTag.isImage();
            case FILE_TYPE_MEDIA:
                return editorTag.isUploadVideo();
            case FILE_TYPE_FILE:
                return editorTag.isFile();
        }
        return false;
    }

    static ResourceEnum getResourceEnum(String type) {
        switch (type) {
            case FILE_TYPE_IMG:
                return ResourceEnum._2002000;
            case FILE_TYPE_MEDIA:
                return ResourceEnum._2004000;
            case FILE_TYPE_FILE:
                return ResourceEnum._2003000;
        }
        return null;
    }

    static Long getUploadSize(EditorTag editorTag, String type) {
        switch (type) {
            case FILE_TYPE_IMG:
                return editorTag.getImageSize();
            case FILE_TYPE_MEDIA:
                return editorTag.getVideoSize();
            case FILE_TYPE_FILE:
                return editorTag.getFileSize();
        }
        return null;
    }
}
