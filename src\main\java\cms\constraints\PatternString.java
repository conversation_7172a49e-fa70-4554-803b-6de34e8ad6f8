package cms.constraints;

import cms.constraints.validator.PatternStringConstraintValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/4/21 12:01
 */
@Documented
@Constraint(validatedBy = {
        PatternStringConstraintValidator.class
})
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.TYPE_USE})
@Retention(RetentionPolicy.RUNTIME)
public @interface PatternString {
    String regex() default "";

    String message() default "格式不正确";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
