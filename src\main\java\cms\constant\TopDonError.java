package cms.constant;

import com.google.common.collect.Maps;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/18 9:33
 */
public enum TopDonError {

    E10001("10001", "password"),
    E60002("60002", "password"),
    E60106("60106", "captchaValue"),
    ;

    private String topDonErrorCode;
    private String filedName;

    TopDonError(String topDonErrorCode, String filedName) {
        this.topDonErrorCode = topDonErrorCode;
        this.filedName = filedName;
    }

    private static final Map<String, TopDonError> map = Maps.newHashMap();

    static {
        Arrays.stream(TopDonError.values()).forEach(o -> map.put(o.getTopDonErrorCode(), o));
    }

    public String getTopDonErrorCode() {
        return topDonErrorCode;
    }

    public String getFiledName() {
        return filedName;
    }

    public static Optional<String> getFiledName(String topDonErrorCode) {
        return Optional.ofNullable(map.get(topDonErrorCode)).map(TopDonError::getFiledName);
    }
}
