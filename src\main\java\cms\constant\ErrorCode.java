package cms.constant;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/4/20 12:02
 * 第1位数 1 后台 2 前端 3 TopDon
 * 第2-5位数 功能
 * 0001: 系统错误
 * 0002: 话题标签
 * 0003: 问题标签
 * 0004: 会员管理
 * 0005: 员工管理
 * 0006: 查询关键字字典
 * 0007: 话题
 * 0008: 问答
 * 0009: 在线留言
 * 0010: 关注粉丝
 * 2001: 对应原来的错误码-不想归类
 * <p>
 * 1001: topDon自定义错误
 * 第 6-9位数 错误码
 */
public interface ErrorCode {

    String DEFAULT_CODE = "500";
    int DEFAULT_CODE_INT = 500;

    int C_1_0001_0001 = 100010001; // 令牌错误
    int C_1_0001_0002 = 100010002; // 令牌过期
    int C_1_0001_0003 = 100010003; // 令牌为空
    int C_1_0001_0004 = 100010004; // 令牌检测未开启
    int C_1_0001_0005 = 100010005; // 路径来路错误
    int C_1_0001_0006 = 100010006; // 密钥错误
    int C_1_0001_0007 = 100010007; // 字符超长
    int C_1_0001_0008 = 100010008; // 系统繁忙
    int C_1_0001_0009 = 100010009; // 模块错误
    int C_1_0001_0010 = 100010010; // 刷新令牌不能为空
    int C_1_0001_0011 = 100010011; // 60秒内不能重复获取邮件
    int C_1_0001_0012 = 100010012; // 导入，删除，初始化敏感词，历史话题，历史问答 敏感词剔除。不能同时操作
    int C_1_0001_0013 = 100010013; // 当前请求太多，请稍后再试。
    int C_1_0001_0014 = 100010014; // clientId stamp sign 不能为空
    int C_1_0001_0015 = 100010015; // clientId 错误
    int C_1_0001_0016 = 100010016; // stamp 格式错误
    int C_1_0001_0017 = 100010017; // invalid stamp header: invalid time
    int C_1_0001_0018 = 100010018; // invalid sign header: sign mismatch
    int C_1_0001_0019 = 100010019; // unauthorized API URI(API接口未授权)
    int C_1_0001_0020 = 100010020; // topdon token 不能为空

    int C_1_0002_0001 = 100020001; // 话题标签已达到最大层数
    int C_1_0002_0002 = 100020002; // 必须选择一级标签
    int C_1_0002_0003 = 100020003; // 标签名称不能重复
    int C_1_0002_0004 = 100020004; // 存在下级标签不能删除

    int C_1_0003_0001 = 100030001; // 问题标签已达到最大层数
    int C_1_0003_0002 = 100030002; // 父标签不存在
    int C_1_0003_0003 = 100030003; // 标签不存在
    int C_1_0003_0004 = 100030004; // 问题标签名称不能重复

    int C_1_0004_0001 = 100040001; // 该账号已注册
    int C_1_0004_0002 = 100040002; // 手机号码已注册
    int C_1_0004_0006 = 100040006; // 用户类型不正确
    int C_1_0004_0007 = 100040007; // 需要输入验证码
    int C_1_0004_0008 = 100040008; // 请输入验证码
    int C_1_0004_0009 = 100040009; // 验证码错误
    int C_1_0004_0010 = 100040010; // 验证码过期
    int C_1_0004_0011 = 100040011; // 用户不存在
    int C_1_0004_0012 = 100040012; // 禁止账号

    int C_1_0005_0001 = 100050001; // 该账号已存在
    int C_1_0005_0002 = 100050002; // 两次输入密码不相同
    int C_1_0005_0003 = 100050003; // 员工Id不能为空
    int C_1_0005_0004 = 100050004; // 员工不存在

    int C_1_0006_0001 = 100060001; // 上传只能上传txt文件
    int C_1_0006_0002 = 100060002; // 请上传非空文件
    int C_1_0006_0003 = 100060003; // 删除Id不能为空

    int C_2_0001_0001 = 200010001; // 只读模式不允许提交数据
    int C_2_0001_0002 = 200010002; // 权限不足
    int C_2_0001_0003 = 200010003; // 输入密码可见
    int C_2_0001_0004 = 200010004; // 评论话题可见
    int C_2_0001_0005 = 200010005; // 达到等级可见
    int C_2_0001_0006 = 200010006; // 积分购买可见
    int C_2_0001_0007 = 200010007; // 余额购买可见
    int C_2_0001_0008 = 200010008; // token刷新失败
    int C_2_0001_0009 = 200010009; // 上传附件，参数不能为空
    int C_2_0001_0010 = 200010010; // 当前格式的文件，不允许上传
    int C_2_0001_0011 = 200010011; // 上传文件错误，请稍后再试
    int C_2_0001_0012 = 200010012; // 不允许上传图片
    int C_2_0001_0013 = 200010013; // 图片大小超出 {0} K
    int C_2_0001_0014 = 200010014; // 不允许发表评论
    int C_2_0001_0015 = 200010015; // 不允许发表问题
    int C_2_0001_0016 = 200010016; // dir参数错误
    int C_2_0001_0017 = 200010017; // 不允许发表话题
    int C_2_0001_0018 = 200010018; // 不允许上传此类型的附件
    int C_2_0001_0019 = 200010019; // 没有配置文件类型对应的权限信息
    int C_2_0001_0020 = 200010020; // 因为敏感词有更新，当前文本正在进行敏感词检测，请稍后操作
    int C_2_0001_0021 = 200010021; // 获取执行锁失败
    int C_2_0001_0022 = 200010022; // 获取执行锁钥失败
    int C_2_0001_0023 = 200010023; // 用户注册日期不能为空
    int C_2_0001_0024 = 200010024; // 媒体路径不正确

    int C_2_0002_0001 = 200020001; // 活动不存在
    int C_2_0002_0002 = 200020002; // topicInfo 必填

    int C_2_0001_0500 = 200010500; // 系统错误

    int C_2_0004_0001 = 200040001; // 不允许注册
    int C_2_0004_0002 = 200040002; // 用户名称不能为空
    int C_2_0004_0003 = 200040003; // 验证码参数错误
    int C_2_0004_0006 = 200040006; // 账号错误
    int C_2_0004_0007 = 200040007; // 用户不存在
    int C_2_0004_0008 = 200040008; // 用户不是本地密码账户
    int C_2_0004_0009 = 200040009; // 密码提示答案错误
    int C_2_0004_0010 = 200040010; // 手机号不是手机账户
    int C_2_0004_0011 = 200040011; // 手机验证码不能为空
    int C_2_0004_0012 = 200040012; // 手机验证码超长
    int C_2_0004_0013 = 200040013; // 手机验证码不存在或已过期
    int C_2_0004_0014 = 200040014; // 手机验证码错误
    int C_2_0004_0015 = 200040015; // 密码提示答案不能为空
    int C_2_0004_0016 = 200040016; // 密码提示答案长度错误
    int C_2_0004_0018 = 200040018; // 用户类型不能为空
    int C_2_0004_0019 = 200040019; // 该用户名不允许注册
    int C_2_0004_0020 = 200040020; // 注册会员出错
    int C_2_0004_0021 = 200040021; // 找回密码成功
    int C_2_0004_0022 = 200040022; // 存在敏感词

    int C_2_0007_0001 = 200070001; // 话题不存在
    int C_2_0007_0002 = 200070002; // 内容不能为空
    int C_2_0007_0003 = 200070003; // 话题Id不能为空
    int C_2_0007_0004 = 200070004; // 引用评论不能为空
    int C_2_0007_0005 = 200070005; // 评论不存在
    int C_2_0007_0006 = 200070006; // 禁止评论
    int C_2_0007_0007 = 200070007; // 实名用户才允许评论
    int C_2_0007_0008 = 200070008; // 实名用户才允许提交话题
    int C_2_0007_0009 = 200070009; // 不允许提交话题
    int C_2_0007_0010 = 200070010; // 已发布话题才允许评论
    int C_2_0007_0011 = 200070011; // 只允许修改自己发布的话题
    int C_2_0007_0012 = 200070012; // 话题已删除
    int C_2_0007_0013 = 200070013; // 修改话题失败
    int C_2_0007_0015 = 200070015; // 只允许修改自己发布的评论
    int C_2_0007_0016 = 200070016; // 评论已删除
    int C_2_0007_0017 = 200070017; // 修改评论失败
    int C_2_0007_0018 = 200070018; // 评论Id不能为空
    int C_2_0007_0019 = 200070019; // 修改回复失败
    int C_2_0007_0020 = 200070020; // 只允许修改自己发布的回复
    int C_2_0007_0021 = 200070021; // 禁止回复
    int C_2_0007_0022 = 200070022; // 实名用户才允许提交回复
    int C_2_0007_0023 = 200070023; // 回复不存在
    int C_2_0007_0024 = 200070024; // 回复Id不能为空
    int C_2_0007_0025 = 200070025; // 请填写总金额
    int C_2_0007_0026 = 200070026; // 不能超过12位数字
    int C_2_0007_0027 = 200070027; // 不能超过3位数字
    int C_2_0007_0028 = 200070028; // 请填写正整数
    int C_2_0007_0029 = 200070029; // 不允许发红包
    int C_2_0007_0030 = 200070030; // 不能小于发红包总金额下限
    int C_2_0007_0031 = 200070031; // 不能大于发红包总金额上限
    int C_2_0007_0032 = 200070032; // 总金额不能为空
    int C_2_0007_0033 = 200070033; // 拆分后最低单个红包金额不足0.01元
    int C_2_0007_0034 = 200070034; // 金额不能为空
    int C_2_0007_0035 = 200070035; // 不能小于0.01元
    int C_2_0007_0036 = 200070036; // 请填写货币格式
    int C_2_0007_0037 = 200070037; // 不能为空
    int C_2_0007_0038 = 200070038; // 提交话题错误
    int C_2_0007_0039 = 200070039; // 删除评论失败
    int C_2_0007_0040 = 200070040; // 删除回复失败
    int C_2_0007_0041 = 200070041; // 只允许删除自己发布的评论
    int C_2_0007_0042 = 200070042; // 只允许删除自己发布的回复
    int C_2_0007_0043 = 200070043; // 标签不能为空
    int C_2_0007_0044 = 200070044; // 不能大于150个字符
    int C_2_0007_0045 = 200070045; // 标题不能为空
    int C_2_0007_0046 = 200070046; // 发表话题不允许使用 ‘{0}’ 隐藏标签
    int C_2_0007_0047 = 200070047; // 话题内容不能为空
    int C_2_0007_0048 = 200070048; // 活动没有绑定对应话题

    int C_2_0008_0001 = 200080001;// 内容不能为空
    int C_2_0008_0002 = 200080002;// 问题Id不能为空
    int C_2_0008_0003 = 200080003;// 答案不存在
    int C_2_0008_0004 = 200080004;// 禁止回答
    int C_2_0008_0005 = 200080005;// 问题不存在
    int C_2_0008_0006 = 200080006;// 实名用户才允许回答
    int C_2_0008_0007 = 200080007;// 实名用户才允许提交问题
    int C_2_0008_0008 = 200080008;// 不允许提交问题
    int C_2_0008_0009 = 200080009;// 已提交问题才允许评论
    int C_2_0008_0010 = 200080010;// 问题不存在
    int C_2_0008_0011 = 200080011;// 问题已删除
    int C_2_0008_0012 = 200080012;// 答案Id不能为空
    int C_2_0008_0013 = 200080013;// 该问题已经采纳答案
    int C_2_0008_0014 = 200080014;// 不是提交该问题的用户不允许采纳答案
    int C_2_0008_0015 = 200080015;// 追加内容不能为空
    int C_2_0008_0016 = 200080016;// 不是提交该问题的用户不允许追加提问
    int C_2_0008_0017 = 200080017;// 不能超过12位数字
    int C_2_0008_0018 = 200080018;// 请填写金额
    int C_2_0008_0019 = 200080019;// 不能超过8位数字
    int C_2_0008_0020 = 200080020;// 请填写正整数或0
    int C_2_0008_0022 = 200080022;// 不能大于账户预存款
    int C_2_0008_0023 = 200080023;// 不能小于0
    int C_2_0008_0024 = 200080024;// 不能大于账户积分
    int C_2_0008_0025 = 200080025;// 提交问题错误
    int C_2_0008_0026 = 200080026;// 该问题不允许采纳答案
    int C_2_0008_0027 = 200080027;// 不允许悬赏金额
    int C_2_0008_0028 = 200080028;// 不能小于悬赏金额下限
    int C_2_0008_0029 = 200080029;// 不能大于悬赏金额上限
    int C_2_0008_0030 = 200080030;// 不允许悬赏积分
    int C_2_0008_0031 = 200080031;// 不能小于悬赏积分下限
    int C_2_0008_0032 = 200080032;// 不能大于悬赏积分上限
    int C_2_0008_0034 = 200080034;// 只允许修改自己发布的答案
    int C_2_0008_0035 = 200080035;// 答案已删除
    int C_2_0008_0036 = 200080036;// 修改答案失败
    int C_2_0008_0037 = 200080037;// 答案Id不能为空
    int C_2_0008_0038 = 200080038;// 不允许修改已采纳的答案
    int C_2_0008_0039 = 200080039;// 删除答案失败
    int C_2_0008_0040 = 200080040;// 不能重复提交，请稍后再试
    int C_2_0008_0041 = 200080041;// 最多只能追加三次问题
    int C_2_0008_0042 = 200080042;// 不是提交该问题的用户不允许取消采纳答案
    int C_2_0008_0043 = 200080043;// 不允许删除已采纳的答案
    int C_2_0008_0044 = 200080044;// 话题标签不能超过{0}个
    int C_2_0008_0045 = 200080045;// 不允许删除已采纳的答案

    int C_2_0009_0001 = 200090001;// 不能超过100个字符
    int C_2_0009_0002 = 200090002;// 名称不能为空
    int C_2_0009_0003 = 200090003;// 联系方式不能为空
    int C_2_0009_0004 = 200090004;// 内容不能为空
    int C_2_0009_0005 = 200090005;// 在线留言已关闭
    int C_2_0009_0006 = 200090006;// 字符超长

    int C_2_0010_0001 = 200100001;// 粉丝不存在
    int C_2_0010_0002 = 200100002;// 粉丝不属于该用户
    int C_2_0010_0003 = 200100003;// 删除粉丝失败

    int C_2_1001_0001 = 210010001; // TopDon登录错误
    int C_2_1001_0002 = 210010002; // TopDon注册错误
    int C_2_1001_0003 = 210010003; // TopDon获取验证码错误
    int C_2_1001_0004 = 210010004; // 用户未登录
    int C_2_1001_0005 = 210010005; // 获取Topdon用户信息错误

    int C_2_2001_0001 = 220010001;// 密码长度错误
    int C_2_2001_0002 = 220010002;// 旧密码错误
    int C_2_2001_0003 = 220010003;// 旧密码不能为空
    int C_2_2001_0004 = 220010004;// 只允许输入数字
    int C_2_2001_0005 = 220010005;// 只允许输入字母
    int C_2_2001_0006 = 220010006;// 只允许输入数字和字母
    int C_2_2001_0007 = 220010007;// 只允许输入汉字
    int C_2_2001_0008 = 220010008;// 输入错误
    int C_2_2001_0009 = 220010009;// 必填项
    int C_2_2001_0010 = 220010010;// 修改用户失败
    int C_2_2001_0011 = 220010011;// 账号不能小于3个字符
    int C_2_2001_0012 = 220010012;// 账号不能大于25个字符
    int C_2_2001_0013 = 220010013;// 账号只能输入由数字、26个英文字母或者下划线组成
    int C_2_2001_0014 = 220010014;// 该账号已注册
    int C_2_2001_0015 = 220010015;// 账号不能为空
    int C_2_2001_0016 = 220010016;// 密码不能为空
    int C_2_2001_0017 = 220010017;// 密码提示问题不能超过50个字符
    int C_2_2001_0018 = 220010018;// 密码提示问题不能为空
    int C_2_2001_0019 = 220010019;// 密码提示答案长度错误
    int C_2_2001_0020 = 220010020;// 密码提示答案不能为空
    int C_2_2001_0021 = 220010021;// 邮箱地址不正确
    int C_2_2001_0022 = 220010022;// 邮箱地址不能超过60个字符
    int C_2_2001_0023 = 220010023;// 注册会员出错
    int C_2_2001_0024 = 220010024;// 禁止账号
    int C_2_2001_0025 = 220010025;// 账号错误
    int C_2_2001_0026 = 220010026;// 密码错误
    int C_2_2001_0027 = 220010027;// 密码提示答案错误
    int C_2_2001_0028 = 220010028;// 找回密码错误
    int C_2_2001_0029 = 220010029;// 呢称不能超过15个字符
    int C_2_2001_0030 = 220010030;// 该呢称已存在
    int C_2_2001_0031 = 220010031;// 不允许修改呢称
    int C_2_2001_0032 = 220010032;// 该呢称不允许使用
    int C_2_2001_0033 = 220010033;// 呢称不能和其他用户名相同
    int C_2_2001_0034 = 220010034;// 用户类型不能为空
    int C_2_2001_0035 = 220010035;// 用户类型错误
    int C_2_2001_0036 = 220010036;// 手机验证码错误
    int C_2_2001_0037 = 220010037;// 手机号不能为空
    int C_2_2001_0038 = 220010038;// 手机验证码不能为空
    int C_2_2001_0039 = 220010039;// 手机号码不正确
    int C_2_2001_0040 = 220010040;// 手机号码超长
    int C_2_2001_0041 = 220010041;// 手机验证码超长
    int C_2_2001_0042 = 220010042;// 手机验证码不存在或已过期
    int C_2_2001_0043 = 220010043;// 手机号码不能重复绑定
    int C_2_2001_0044 = 220010044;// 你还没有绑定手机
    int C_2_2001_0045 = 220010045;// 用户不存在
    int C_2_2001_0046 = 220010046;// 新手机号码不能和旧用机号码相同
    int C_2_2001_0047 = 220010047;// 旧手机号码校验失败
    int C_2_2001_0048 = 220010048;// 不允许注册
    int C_2_2001_0049 = 220010049;// 该用户名不允许注册
    int C_2_2001_0050 = 220010050;// 手机号码已注册
    int C_2_2001_0051 = 220010051;// 用户类型不能为空
    int C_2_2001_0052 = 220010052;// 手机号不能为空
    int C_2_2001_0053 = 220010053;// 手机号错误
    int C_2_2001_0054 = 220010054;// 不允许发短信
    int C_2_2001_0055 = 220010055;// 手机用户不存在
    int C_2_2001_0056 = 220010056;// 手机号不是手机账户
    int C_2_2001_0057 = 220010057;// 用户不存在
    int C_2_2001_0058 = 220010058;// 用户不是本地密码账户
    int C_2_2001_0059 = 220010059;// 不允许给当前用户发私信
    int C_2_2001_0060 = 220010060;// 不允许给自己发私信
    int C_2_2001_0061 = 220010061;// 对方用户名称不能为空
    int C_2_2001_0062 = 220010062;// 私信内容不能超过1000个字符
    int C_2_2001_0063 = 220010063;// 私信内容不能为空
    int C_2_2001_0064 = 220010064;// 删除私信失败
    int C_2_2001_0065 = 220010065;// 订阅系统通知Id不能为空
    int C_2_2001_0066 = 220010066;// 删除系统通知失败
    int C_2_2001_0067 = 220010067;// 不能超过8位数字
    int C_2_2001_0068 = 220010068;// 宽度必须大于0
    int C_2_2001_0069 = 220010069;// 高度必须大于0
    int C_2_2001_0070 = 220010070;// X轴必须大于或等于0
    int C_2_2001_0071 = 220010071;// Y轴必须大于或等于0
    int C_2_2001_0072 = 220010072;// 超出最大宽度
    int C_2_2001_0073 = 220010073;// 超出最大高度
    int C_2_2001_0074 = 220010074;// 当前文件类型不允许上传
    int C_2_2001_0075 = 220010075;// 文件超出允许上传大小
    int C_2_2001_0076 = 220010076;// 文件不能为空
    int C_2_2001_0077 = 220010077;// 提醒不存在
    int C_2_2001_0078 = 220010078;// 重复收藏
    int C_2_2001_0079 = 220010079;// 话题收藏Id不能为空
    int C_2_2001_0080 = 220010080;// 当前话题已经收藏
    int C_2_2001_0081 = 220010081;// 当前问题已经收藏
    int C_2_2001_0082 = 220010082;// 收藏Id不存在
    int C_2_2001_0083 = 220010083;// 删除收藏失败
    int C_2_2001_0084 = 220010084;// 收藏不存在
    int C_2_2001_0085 = 220010085;// 本收藏不属于当前用户
    int C_2_2001_0086 = 220010086;// 问题收藏Id不能为空
    int C_2_2001_0087 = 220010087;// 待收藏数据不存在
    int C_2_2001_0088 = 220010088;// 不允许同时收藏多项数据
    int C_2_2001_0091 = 220010091;// 话题重复取消隐藏
    int C_2_2001_0092 = 220010092;// 当前话题已经取消隐藏
    int C_2_2001_0093 = 220010093;// 隐藏标签不存在
    int C_2_2001_0094 = 220010094;// 密码错误
    int C_2_2001_0095 = 220010095;// 提交过于频繁，请稍后再提交
    int C_2_2001_0096 = 220010096;// 密码不能为空
    int C_2_2001_0097 = 220010097;// 话题内容不含当前标签
    int C_2_2001_0098 = 220010098;// 用户不存在
    int C_2_2001_0099 = 220010099;// 用户积分不足
    int C_2_2001_0100 = 220010100;// 用户余额不足
    int C_2_2001_0101 = 220010101;// 不允许解锁自已发表的话题
    int C_2_2001_0102 = 220010102;// 重复点赞
    int C_2_2001_0103 = 220010103;// 话题点赞Id不能为空
    int C_2_2001_0104 = 220010104;// 当前话题已经点赞
    int C_2_2001_0105 = 220010105;// 点赞Id不存在
    int C_2_2001_0106 = 220010106;// 删除点赞失败
    int C_2_2001_0107 = 220010107;// 点赞不存在
    int C_2_2001_0108 = 220010108;// 本点赞不属于当前用户
    int C_2_2001_0109 = 220010109;// 重复关注
    int C_2_2001_0110 = 220010110;// 关注Id不能为空
    int C_2_2001_0111 = 220010111;// 当前用户已关注对方
    int C_2_2001_0112 = 220010112;// 关注Id不存在
    int C_2_2001_0113 = 220010113;// 删除关注失败
    int C_2_2001_0114 = 220010114;// 关注不存在
    int C_2_2001_0115 = 220010115;// 本关注不属于当前用户
    int C_2_2001_0116 = 220010116;// 不能关注自身
    int C_2_2001_0117 = 220010117;// 规格Id不能为空
    int C_2_2001_0118 = 220010118;// 规格不存在
    int C_2_2001_0119 = 220010119;// 会员卡不存在
    int C_2_2001_0120 = 220010120;// 会员卡已下架
    int C_2_2001_0121 = 220010121;// 创建会员卡订单错误
    int C_2_2001_0122 = 220010122;// 角色不存在
    int C_2_2001_0123 = 220010123;// 库存不足
    int C_2_2001_0124 = 220010124;// 积分不足
    int C_2_2001_0125 = 220010125;// 预存款不足
    int C_2_2001_0126 = 220010126;// 此规格已下架
    int C_2_2001_0127 = 220010127;// 此会员卡已下架
    int C_2_2001_0130 = 220010130;// 没有发红包
    int C_2_2001_0131 = 220010131;// 发红包Id不能为空
    int C_2_2001_0132 = 220010132;// 收红包错误
    int C_2_2001_0133 = 220010133;// 每个红包只能领取一次
    int C_2_2001_0134 = 220010134;// 红包已被抢光
    int C_2_2001_0135 = 220010135;// 话题未发布不允许领取红包
    int C_2_2001_0136 = 220010136;// 话题不存在不允许领取红包
    int C_2_2001_0137 = 220010137;// 红包已原路返还用户
    int C_2_2001_0138 = 220010138;// 没有领取红包权限
}
