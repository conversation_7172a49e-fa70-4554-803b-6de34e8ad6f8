package cms.utils;

import com.sun.mail.util.MailSSLSocketFactory;
import lombok.extern.slf4j.Slf4j;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.io.UnsupportedEncodingException;
import java.security.GeneralSecurityException;
import java.util.Properties;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/24 16:09
 */
@Slf4j
public class EmailUtil {

    private final static String NICK_NAME = "TOPDON Community";
    private final static String from = "<EMAIL>";
    private final static String host = "smtp.office365.com";
    private final static String auth = "nm736tgaopl.2098h";

    public static void send(String to, String title, String content) throws MessagingException, UnsupportedEncodingException, GeneralSecurityException {
        log.info(">>>>>>>> start send email to {} title {} content {} <<<<<<<<", to, title, content);
        Properties properties = System.getProperties();
        properties.setProperty("mail.smtp.host", host);
        properties.setProperty("mail.smtp.auth", "true");
        properties.setProperty("mail.smtp.port", "587");
        MailSSLSocketFactory sf = new MailSSLSocketFactory();
        sf.setTrustAllHosts(true);
//        properties.setProperty("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        properties.setProperty("mail.smtp.starttls.enable", "true");
//        properties.setProperty("mail.smtp.socketFactory.fallback", "false");
//        properties.setProperty("mail.smtp.socketFactory.port", "587");

        Session session = Session.getDefaultInstance(properties, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(from, auth);
            }
        });

        MimeMessage message = new MimeMessage(session);
        message.setFrom(new InternetAddress(from, NICK_NAME));
        message.addRecipient(Message.RecipientType.TO, new InternetAddress(to));
        message.setSubject(title);
        message.setText(content, "utf-8", "html");

        Transport.send(message);
    }
}
