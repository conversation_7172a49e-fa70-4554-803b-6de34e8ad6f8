package cms.config;

import cms.web.filter.ExceptionFilter;
import cms.web.filter.RequestTrimFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.DispatcherType;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/4/21 16:00
 */
@Configuration
public class FilterConfig {

    @Bean
    public FilterRegistrationBean trimFilter() {
        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
        registrationBean.setDispatcherTypes(DispatcherType.REQUEST);
        registrationBean.setFilter(new RequestTrimFilter());
        registrationBean.addUrlPatterns("/*");
        registrationBean.setName("RequestTrimFilter");
        registrationBean.setOrder(Integer.MAX_VALUE - 1);
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean exceptionFilter() {
        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
        registrationBean.setDispatcherTypes(DispatcherType.REQUEST);
        registrationBean.setFilter(new ExceptionFilter());
        registrationBean.addUrlPatterns("/*");
        registrationBean.setName("ExceptionFilter");
        registrationBean.setOrder(-1);
        return registrationBean;
    }
}
