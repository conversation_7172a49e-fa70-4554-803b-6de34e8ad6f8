package cms.web.action.question;

import cms.bean.PageForm;
import cms.bean.RequestResult;
import cms.bean.ResultCode;
import cms.bean.question.Answer;
import cms.bean.question.AnswerReply;
import cms.bean.setting.EditorTag;
import cms.constant.ErrorCode;
import cms.handle.MsgException;
import cms.service.aliyun.OssFileChangeService;
import cms.service.question.AnswerService;
import cms.service.question.impl.AnswerManageService;
import cms.utils.IpAddress;
import cms.utils.JsonUtils;
import cms.web.action.fileSystem.FileManage;
import cms.web.action.setting.SettingManage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static cms.constant.FileConstant.SIZE_SPLIT;

/**
 * 答案
 */
@Controller
@RequestMapping("/control/answer/manage")
public class AnswerManageAction {

    @Resource
    AnswerService answerService;//通过接口引用代理返回的对象
    @Resource
    SettingManage settingManage;
    @Resource
    AnswerManage answerManage;

    @Resource
    FileManage fileManage;
    @Autowired
    private OssFileChangeService ossFileChangeService;
    @Autowired
    private AnswerManageService answerManageService;

    /**
     * 答案  添加
     */
    @ResponseBody
    @RequestMapping(params = "method=add", method = RequestMethod.POST)
    public String add(ModelMap model, Long questionId, String content,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {
        return answerManageService.add(model, questionId, content, request, response);
    }


    /**
     * 答案  修改页面显示
     */
    @ResponseBody
    @RequestMapping(params = "method=edit", method = RequestMethod.GET)
    public String editUI(ModelMap model, Long answerId,
                         HttpServletRequest request, HttpServletResponse response) throws Exception {

        Map<String, String> error = new HashMap<String, String>();
        Map<String, Object> returnValue = new HashMap<String, Object>();

        returnValue.put("availableTag", answerManage.availableTag());

        if (answerId != null && answerId > 0L) {
            Answer answer = answerService.findByAnswerId(answerId);
            if (answer != null) {
                if (answer.getContent() != null && !"".equals(answer.getContent().trim())) {
                    //处理富文本路径
                    answer.setContent(fileManage.processRichTextFilePath(answer.getContent(), "answer"));
                }
                returnValue.put("answer", answer);
                String username = "";
                Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
                if (obj instanceof UserDetails) {
                    username = ((UserDetails) obj).getUsername();
                }
                returnValue.put("userName", username);
            } else {
                error.put("answerId", "答案Id不能为空");
            }

        } else {
            error.put("answerId", "答案不存在");
        }

        if (error.size() == 0) {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, returnValue));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    /**
     * 答案  修改
     */
    @ResponseBody
    @RequestMapping(params = "method=edit", method = RequestMethod.POST)
    public String edit(PageForm pageForm, ModelMap model, Long answerId, String content, Integer status,
                       HttpServletRequest request, HttpServletResponse response) throws Exception {
        return answerManageService.edit(pageForm, model, answerId, content, status, request, response);
    }

    /**
     * 答案  删除
     *
     * @param model
     * @param answerId 答案Id
     */
    @ResponseBody
    @RequestMapping(params = "method=delete", method = RequestMethod.POST)
    public String delete(ModelMap model, Long[] answerId,
                         HttpServletRequest request, HttpServletResponse response) throws Exception {
        return answerManageService.delete(model, answerId, request, response);
    }

    /**
     * 答案  图片上传
     * <p>
     * 员工发话题 上传文件名为UUID + a + 员工Id
     * 用户发话题 上传文件名为UUID + b + 用户Id
     *
     * @param questionId 问题Id
     * @param userName   用户名称
     * @param isStaff    是否是员工   true:员工   false:会员
     * @param fileName   文件名称 预签名时有值
     */
    @ResponseBody
    @RequestMapping(params = "method=uploadImage", method = RequestMethod.POST)
    public String uploadImage(ModelMap model, Long questionId, String userName, Boolean isStaff, String fileName,
                              MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Optional<EditorTag> editorTagOptional = Optional.ofNullable(settingManage.readAnswerEditorTag());
        return ossFileChangeService.upload(errorMessageService -> {
            long size = file.getSize();
            editorTagOptional.map(EditorTag::isImage).filter(o -> o).orElseThrow(() -> new MsgException(errorMessageService.getMessage(ErrorCode.C_2_0001_0012)));
            editorTagOptional.map(EditorTag::getImageSize).filter(o -> size / SIZE_SPLIT <= o).orElseThrow(() -> new MsgException(errorMessageService.getMessage(ErrorCode.C_2_0001_0013, String.valueOf(size / SIZE_SPLIT))));
        }, file, editorTagOptional.map(EditorTag::getImageFormat), userName, isStaff, "answer", Optional.ofNullable(questionId).map(String::valueOf));
    }


    /**
     * 审核答案
     *
     * @param model
     * @param answerId 答案Id
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(params = "method=auditAnswer", method = RequestMethod.POST)
    public String auditAnswer(ModelMap model, Long answerId,
                              HttpServletResponse response) throws Exception {
        return answerManageService.auditAnswer(model, answerId, response);
    }


    /**
     * 答案回复  添加页面显示
     *
     * @param pageForm
     * @param model
     * @param answerId 答案Id
     */
    @ResponseBody
    @RequestMapping(params = "method=addAnswerReply", method = RequestMethod.GET)
    public String addAnswerReplyUI(ModelMap model, Long answerId,
                                   HttpServletRequest request, HttpServletResponse response) throws Exception {

        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
    }

    /**
     * 答案回复  添加
     *
     * @param model
     * @param answerId 答案Id
     */
    @ResponseBody
    @RequestMapping(params = "method=addAnswerReply", method = RequestMethod.POST)
    public String addAnswerReply(ModelMap model, Long answerId, String content,
                                 HttpServletRequest request, HttpServletResponse response) throws Exception {
        return answerManageService.addAnswerReply(model, answerId, content, request, response);
    }

    /**
     * 答案回复  修改页面显示
     *
     * @param pageForm
     * @param model
     * @param answerReplyId 答案回复Id
     */
    @ResponseBody
    @RequestMapping(params = "method=editAnswerReply", method = RequestMethod.GET)
    public String editAnswerReplyUI(ModelMap model, Long answerReplyId,
                                    HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, String> error = new HashMap<String, String>();
        Map<String, Object> returnValue = new HashMap<String, Object>();

        if (answerReplyId != null && answerReplyId > 0) {
            AnswerReply answerReply = answerService.findReplyByReplyId(answerReplyId);
            if (answerReply != null) {
                if (answerReply.getIp() != null && !"".equals(answerReply.getIp().trim())) {

                    answerReply.setIpAddress(IpAddress.queryAddress(answerReply.getIp()));
                }
                returnValue.put("answerReply", answerReply);
                return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, returnValue));
            }
        } else {
            error.put("answerReplyId", "回复Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    /**
     * 答案回复  修改页面
     *
     * @param pageForm
     * @param model
     * @param answerReplyId 答案回复Id
     */
    @ResponseBody
    @RequestMapping(params = "method=editAnswerReply", method = RequestMethod.POST)
    public String editAnswerReply(ModelMap model, Long answerReplyId, String content, Integer status,
                                  HttpServletRequest request, HttpServletResponse response) throws Exception {
        return answerManageService.editAnswerReply(model, answerReplyId, content, status, request, response);
    }

    /**
     * 回复  删除
     *
     * @param pageForm
     * @param model
     * @param answerReplyId 回复Id
     */
    @ResponseBody
    @RequestMapping(params = "method=deleteAnswerReply", method = RequestMethod.POST)
    public String deleteAnswerReply(ModelMap model, Long[] answerReplyId,
                                    HttpServletRequest request, HttpServletResponse response) throws Exception {
        return answerManageService.deleteAnswerReply(model, answerReplyId, request, response);
    }

    /**
     * 审核回复
     *
     * @param model
     * @param answerReplyId 回复Id
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(params = "method=auditAnswerReply", method = RequestMethod.POST)
    public String auditAnswerReply(ModelMap model, Long answerReplyId,
                                   HttpServletResponse response) throws Exception {
        return answerManageService.auditAnswerReply(model, answerReplyId, response);
    }

    /**
     * 回复  恢复
     *
     * @param model
     * @param replyId 回复Id
     */
    @ResponseBody
    @RequestMapping(params = "method=recoveryReply", method = RequestMethod.POST)
    public String recoveryReply(ModelMap model, Long replyId,
                                HttpServletRequest request, HttpServletResponse response) throws Exception {
        return answerManageService.recoveryReply(model, replyId, request, response);
    }

    /**
     * 采纳答案
     *
     * @param model
     * @param answerId 答案Id
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(params = "method=adoptionAnswer", method = RequestMethod.POST)
    public String adoptionAnswer(ModelMap model, Long answerId,
                                 HttpServletResponse response) throws Exception {
        Map<String, String> error = answerManageService.adoptionAnswer(answerId);
        if (error.size() == 0) {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));

    }

    /**
     * 取消采纳答案
     *
     * @param model
     * @param answerId 答案Id
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(params = "method=cancelAdoptionAnswer", method = RequestMethod.POST)
    public String cancelAdoptionAnswer(ModelMap model, Long answerId,
                                       HttpServletResponse response) throws Exception {
        Map<String, String> error = answerManageService.cancelAdoptionAnswer(answerId);
        if (error.size() == 0) {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }


}
