package cms.bean.question;

import cms.bean.MediaInfo;
import cms.bean.topic.ImageInfo;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 问题
 */
@Entity
@Table(indexes = {@Index(name = "question_1_idx", columnList = "userName,postTime"), @Index(name = "question_4_idx", columnList = "status,sort,lastAnswerTime"), @Index(name = "question_5_idx", columnList = "adoptionAnswerId,status,sort,lastAnswerTime"), @Index(name = "question_6_idx", columnList = "point,status,sort,lastAnswerTime"), @Index(name = "question_7_idx", columnList = "amount,status,sort,lastAnswerTime")})
@Data
public class Question implements Serializable {
    private static final long serialVersionUID = 8441186002971301170L;
    /**
     * Id
     **/
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 标题
     **/
    @Column(length = 190)
    private String title;

    /**
     * 标签
     **/
    @Transient
    private List<QuestionTagAssociation> questionTagAssociationList = new ArrayList<QuestionTagAssociation>();

    /**
     * 问题内容
     **/
    @Lob
    private String content;
    /**
     * 内容摘要
     **/
    @Lob
    private String summary;
    /**
     * 追加内容
     **/
    @Lob
    private String appendContent = "[";

    @Temporal(TemporalType.TIMESTAMP)
    private Date appendTime;

    /**
     * 追加内容集合
     **/
    @Transient
    private List<AppendQuestionItem> appendQuestionItemList = new ArrayList<AppendQuestionItem>();


    /**
     * 发表时间
     **/
    @Temporal(TemporalType.TIMESTAMP)
    private Date postTime = new Date();
    /**
     * 最后回答时间
     **/
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastAnswerTime;
    /**
     * 最后修改时间
     **/
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastUpdateTime;

    /**
     * IP
     **/
    @Column(length = 45)
    private String ip;
    /**
     * IP归属地
     **/
    @Transient
    private String ipAddress;

    /**
     * 采纳的答案Id
     **/
    private Long adoptionAnswerId = 0L;

    /**
     * 答案总数
     **/
    private Long answerTotal = 0L;
    /**
     * 允许回答
     **/
    private boolean allow = true;

    /**
     * 查看总数
     **/
    private Long viewTotal = 0L;

    /**
     * 用户名称
     **/
    @Column(length = 30)
    private String userName;
    /**
     * 账号
     **/
    @Transient
    private String account;
    /**
     * 呢称
     **/
    @Transient
    private String nickname;
    /**
     * 头像路径
     **/
    @Transient
    private String avatarPath;
    /**
     * 头像名称
     **/
    @Transient
    private String avatarName;

    /**
     * 用户等级,按积分从低祷告，最高五级
     */
    @Transient
    private Integer userGrade;

    /**
     * 用户信息状态 -30.账号已注销(不显示数据) -20.账号已逻辑删除(不显示数据) -10.账号已禁用(不显示数据)  0.正常 10.账号已禁用(显示数据) 20.账号已逻辑删除(显示数据)
     **/
    @Transient
    private Integer userInfoStatus = 0;

    /**
     * 用户角色名称集合
     **/
    @Transient
    private List<String> userRoleNameList = new ArrayList<String>();

    /**
     * 是否为员工 true:员工  false:会员
     **/
    private Boolean isStaff = false;
    /**
     * 排序
     **/
    private Integer sort = 0;
    /**
     * 状态 10.待审核 20.已发布 110.待审核删除 120.已发布删除
     **/
    private Integer status = 10;

    /**
     * 悬赏金额
     **/
    @Column(nullable = false, precision = 12, scale = 2)
    private BigDecimal amount = new BigDecimal("0.00");
    /**
     * 悬赏积分
     **/
    private Long point = 0L;

    /**
     * 收藏总数
     */
    @Transient
    private Long favoriteCount;

    @Lob
    private String image;
    @Transient
    private List<ImageInfo> imageInfoList = new ArrayList<ImageInfo>();

    /**
     * 媒体文件信息集合
     **/
    @Transient
    private List<MediaInfo> mediaInfoList = new ArrayList<MediaInfo>();

    /**
     * 添加标签
     *
     * @param questionTagAssociation
     */
    public void addQuestionTagAssociation(QuestionTagAssociation questionTagAssociation) {
        this.getQuestionTagAssociationList().add(questionTagAssociation);
    }

    @Transient
    private String intro;
}
