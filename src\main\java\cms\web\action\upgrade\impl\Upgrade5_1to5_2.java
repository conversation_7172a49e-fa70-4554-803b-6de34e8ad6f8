package cms.web.action.upgrade.impl;

import cms.bean.upgrade.UpgradeLog;
import cms.bean.upgrade.UpgradeSystem;
import cms.service.upgrade.UpgradeService;
import cms.utils.JsonUtils;
import cms.utils.SpringConfigTool;
import cms.web.action.fileSystem.localImpl.LocalFileManage;
import cms.web.action.upgrade.UpgradeManage;

import java.util.Date;

/**
 * 5.1升级到5.2版本执行程序
 */
public class Upgrade5_1to5_2 {


    /**
     * 运行
     *
     * @param upgradeId 升级Id
     */
    public static void run(String upgradeId) {
        UpgradeService upgradeService = (UpgradeService) SpringConfigTool.getContext().getBean("upgradeServiceBean");
        UpgradeManage upgradeManage = (UpgradeManage) SpringConfigTool.getContext().getBean("upgradeManage");
        LocalFileManage localFileManage = (LocalFileManage) SpringConfigTool.getContext().getBean("localFileManage");
        for (int i = 0; i < 100; i++) {
            upgradeManage.taskRunMark_delete();
            upgradeManage.taskRunMark_add(1L);
            UpgradeSystem upgradeSystem = upgradeService.findUpgradeSystemById(upgradeId);
            if (upgradeSystem == null || upgradeSystem.getRunningStatus().equals(9999)) {
                break;
            }
            if (upgradeSystem.getRunningStatus() >= 40 && upgradeSystem.getRunningStatus() < 200) {

                updateSQL_layout_accessRequireLogin(upgradeService);
                upgradeService.addLog(upgradeId, JsonUtils.toJSONString(new UpgradeLog(new Date(), "修改layout表字段(访问需要登录)成功", 1)) + ",");

                updateSQL_layout_referenceCode(upgradeService);
                upgradeService.addLog(upgradeId, JsonUtils.toJSONString(new UpgradeLog(new Date(), "修改layout表字段(修改会员卡订单列表URL)成功", 1)) + ",");

                //更改运行状态
                upgradeService.updateRunningStatus(upgradeId, 200, JsonUtils.toJSONString(new UpgradeLog(new Date(), "升级流程结束", 1)) + ",");

            }


            if (upgradeSystem.getRunningStatus() >= 200 && upgradeSystem.getRunningStatus() < 9999) {
                //更改运行状态
                upgradeService.updateRunningStatus(upgradeId, 9999, JsonUtils.toJSONString(new UpgradeLog(new Date(), "升级完成", 1)) + ",");

            }


        }
        upgradeManage.taskRunMark_delete();
    }


    /**
     * 修改layout表字段(访问需要登录)
     *
     * @param upgradeService
     */
    public static void updateSQL_layout_accessRequireLogin(UpgradeService upgradeService) {
        String updateSQL = "UPDATE layout SET accessRequireLogin = 0;";
        upgradeService.insertNativeSQL(updateSQL);
    }

    /**
     * 修改layout表字段(会员卡订单列表URL)
     *
     * @param upgradeService
     */
    public static void updateSQL_layout_referenceCode(UpgradeService upgradeService) {
        String updateSQL = "UPDATE layout SET referenceCode = 'user/control/membershipCardOrderList' WHERE referenceCode='membershipCardOrderList';";
        upgradeService.insertNativeSQL(updateSQL);
    }

}
