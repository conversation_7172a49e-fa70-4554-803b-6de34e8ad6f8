package cms.service.operation;

import cms.bean.operation.OperationTag;
import cms.service.besa.DaoSupport;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import javax.persistence.Query;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/25 12:11
 */
@Service
@Slf4j
public class OperationTagService extends DaoSupport<OperationTag> {

    @Cacheable(value = "get_operation_tag_cache", key = "'getOperationTag_'+#operationId")
    public Optional<List<OperationTag>> getByOperationId(Long operationId) {
        Query query = em.createQuery("select o from OperationTag o where o.deleted=false and o.operationId=?1")
                .setParameter(1, operationId);
        return Optional.ofNullable((List<OperationTag>) query.getResultList()).filter(CollectionUtil::isNotEmpty);
    }

    @Caching(evict = {@CacheEvict(value = "get_operation_tag_cache", key = "'getOperationTag_'+#operationId"),
            @CacheEvict(value = "api_topic_sys_config_cache", key = "'getTopicAndSys_'+#operationId")
    })
    public void add(Long operationId, List<Long> tagIds) {
        List<OperationTag> operationTags = tagIds.stream().map(o -> this.buildOperationTag(operationId, o)).filter(Objects::nonNull).collect(Collectors.toList());
        Optional.ofNullable(operationTags).filter(CollectionUtil::isNotEmpty).ifPresent(this::save);
    }

    @Caching(evict = {@CacheEvict(value = "get_operation_tag_cache", key = "'getOperationTag_'+#operationId"),
            @CacheEvict(value = "api_topic_sys_config_cache", key = "'getTopicAndSys_'+#operationId")
    })
    public Integer delByOperationId(Long operationId) {
        Query query = em.createQuery("update OperationTag o set o.deleted=true where o.operationId=:operationId")
                .setParameter("operationId", operationId);
        int i = query.executeUpdate();
        return i;
    }

    @Caching(evict = {@CacheEvict(value = "get_operation_tag_cache", key = "'getOperationTag_'+#operationId"),
            @CacheEvict(value = "api_topic_sys_config_cache", key = "'getTopicAndSys_'+#operationId"),
            @CacheEvict(value = "api_operation_id_cache", key = "'getOperation_'+#operationId"),
            @CacheEvict(value = "api_sponsor_operation_topic_cache", key = "'getSponsorOperationTopic_'+#operationId")
    })
    public void reByOperationId(Long operationId) {
    }

    @CacheEvict(value = "api_innerapi_cache", allEntries = true)
    public void reInnerApi() {

    }

    public OperationTag buildOperationTag(Long operationId, Long tagId) {
        return Optional.ofNullable(operationId).filter(o -> null != tagId).map(o -> {
            OperationTag operationTag = new OperationTag();
            operationTag.setOperationId(operationId);
            operationTag.setTagId(tagId);
            return operationTag;
        }).orElse(null);
    }
}
