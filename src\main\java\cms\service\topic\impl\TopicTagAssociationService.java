package cms.service.topic.impl;

import cms.bean.topic.TopicTagAssociation;
import cms.service.besa.DaoSupport;
import cn.hutool.core.collection.CollectionUtil;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.persistence.Query;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/12 11:25
 */
@Service
public class TopicTagAssociationService extends DaoSupport<TopicTagAssociation> {

    public Optional<List<TopicTagAssociation>> findByTopicId(List<Long> topicIds) {
        Query query = em.createQuery("select o from TopicTagAssociation o where o.topicId in(:topicIds)")
                .setParameter("topicIds", topicIds);
        return Optional.ofNullable(query.getResultList());
    }

    public List<TopicTagAssociation> findByTopicId(Long topicId) {
        Query query = em.createQuery("select o from TopicTagAssociation o where o.topicId  = :topicId")
                .setParameter("topicId", topicId);
        return Optional.ofNullable(query.getResultList()).orElseGet(() -> Lists.newArrayList());
    }

    public void delByTopicId(long topicId) {
        Query comment_delete = em.createQuery("delete from TopicTagAssociation o where o.topicId=?1");
        comment_delete.setParameter(1, topicId);
        comment_delete.executeUpdate();
    }

    public void add(Optional<List<TopicTagAssociation>> topicTagAssociationList) {
        topicTagAssociationList.filter(CollectionUtil::isNotEmpty).ifPresent(this::save);
    }
}
