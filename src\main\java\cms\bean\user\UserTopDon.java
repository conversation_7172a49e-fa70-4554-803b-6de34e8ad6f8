package cms.bean.user;

import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16 10:54
 */
@Entity
@Table(name = "userTopDon")
@Data
public class UserTopDon {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private Long userId;

    @Column(nullable = false)
    private Integer topDonUserId;
}
