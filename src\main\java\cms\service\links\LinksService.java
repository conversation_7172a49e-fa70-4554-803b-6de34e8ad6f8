package cms.service.links;

import cms.bean.links.Links;
import cms.service.besa.DAO;

import java.util.List;

/**
 * 友情链接
 */
public interface LinksService extends DAO<Links> {
    /**
     * 根据Id查询友情链接
     *
     * @param linkId 友情链接Id
     * @return
     */
	Links findById(Integer linkId);

    /**
     * 查询所有友情链接
     *
     * @return
     */
	List<Links> findAllLinks();

    /**
     * 查询所有友情链接 - 缓存
     *
     * @return
     */
	List<Links> findAllLinks_cache();


    /**
     * 保存友情链接
     *
     * @param links
     */
	void saveLinks(Links links);


    /**
     * 修改友情链接
     *
     * @param links
     * @return
     */
	Integer updateLinks(Links links);

    /**
     * 删除友情链接
     *
     * @param linkId 友情链接Id
     */
	Integer deleteLinks(Integer linkId);
}
