package cms.service.user.impl;

import cms.bean.topdon.PlatUser;
import cms.bean.topdon.TopdonResult;
import cms.bean.user.AccessUser;
import cms.bean.user.User;
import cms.bean.user.UserLoginLog;
import cms.handle.CustomException;
import cms.service.user.UserService;
import cms.utils.IpAddress;
import cms.utils.UUIDUtil;
import cms.utils.threadLocal.AccessUserThreadLocal;
import cms.web.action.common.OAuthManage;
import cms.web.action.fileSystem.FileManage;
import cms.web.action.user.UserLoginLogManage;
import cms.web.action.user.UserManage;
import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Maps;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static cms.constant.Constant.USER_TYPE_LOCAL;
import static cms.constant.ErrorCode.C_1_0001_0020;
import static cms.constant.ErrorCode.C_2_2001_0098;
import static cms.web.filter.TopdonTokenFilter.TOPDON_AUTHORIZATION;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/31 17:57
 */
@Service
public class UserLoginService {

    @Autowired
    private OAuthManage oAuthManage;
    @Autowired
    private UserService userService;
    @Autowired
    private FileManage fileManage;
    @Autowired
    private UserManage userManage;
    @Autowired
    private UserLoginLogManage userLoginLogManage;
    @Autowired
    private TopDonUserService topDonUserService;

    public Map<String, Object> loginByAccessUser(HttpServletRequest request) {
        String authorization = Optional.ofNullable(request.getHeader(TOPDON_AUTHORIZATION)).filter(Strings::isNotEmpty).orElseThrow(() -> new CustomException(C_1_0001_0020, "authorization"));
        Optional.ofNullable(topDonUserService.getUserInfoByApp(authorization)).map(TopdonResult::getData).map(PlatUser::getEmail).map(e ->
                Optional.ofNullable(userService.getByEmail(e).orElse(null)).orElseGet(() -> userService.add(e, "", USER_TYPE_LOCAL))
        ).ifPresent(user -> {
            AccessUserThreadLocal.set(new AccessUser(user.getId(), user.getUserName(), user.getAccount(), user.getNickname(), user.getAvatarPath(fileManage.fileOssAddress()), user.getAvatarName(), user.getSecurityDigest(), Boolean.FALSE, ""));
        });

        AccessUser accessUser = Optional.ofNullable(AccessUserThreadLocal.get()).orElseThrow(() -> new CustomException(C_2_2001_0098, "access"));
        User user = Optional.ofNullable(userService.findUserByUserName(accessUser.getUserName())).orElseThrow(() -> new CustomException(C_2_2001_0098, "user"));//查询用户数据

        //访问令牌
        String accessToken = UUIDUtil.getUUID32();
        //刷新令牌
        String refreshToken = UUIDUtil.getUUID32();
        oAuthManage.initToken(user, Optional.empty(), Optional.empty(), accessToken, refreshToken, "", user.getAvatarPath(fileManage.fileOssAddress()), Boolean.FALSE);
        //删除缓存用户状态
        userManage.delete_userState(user.getUserName());
        //写入登录日志
        UserLoginLog userLoginLog = new UserLoginLog();
        userLoginLog.setId(userLoginLogManage.createUserLoginLogId(user.getId()));
        userLoginLog.setIp(IpAddress.getClientIpAddress(request));
        userLoginLog.setUserId(user.getId());
        userLoginLog.setTypeNumber(10);//登录
        userLoginLog.setLogonTime(new Date());
        Object new_userLoginLog = userLoginLogManage.createUserLoginLogObject(userLoginLog);
        userService.saveUserLoginLog(new_userLoginLog);
        return MapUtil.builder(new HashMap<String, Object>(8))
                .put("systemUser", new AccessUser(user.getId(), user.getUserName(), user.getAccount(), user.getNickname(), user.getAvatarPath(fileManage.fileOssAddress()), user.getAvatarName(), null, false, ""))
                .put("accessToken", accessToken)
                .put("refreshToken", refreshToken)
                .build();
    }

}
