package cms.bean.follow;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 粉丝 实体类的抽象基类,定义基本属性
 */
@MappedSuperclass
public class FollowerEntity implements Serializable {
    private static final long serialVersionUID = -5018324406006241501L;

    /**
     * ID (结构：对方用户Id-粉丝的用户Id)
     **/
    @Id
    @Column(length = 40)
    protected String id;

    /**
     * 粉丝的用户名称
     **/
    @Column(length = 30)
    protected String userName;


    /**
     * 对方的用户名称
     **/
    @Column(length = 30)
    protected String friendUserName;
    /**
     * 对方账号
     **/
    @Transient
    protected String friendAccount;
    /**
     * 对方呢称
     **/
    @Transient
    protected String friendNickname;
    /**
     * 对方头像路径
     **/
    @Transient
    protected String friendAvatarPath;
    /**
     * 对方头像名称
     **/
    @Transient
    protected String friendAvatarName;

    /**
     * 加入时间
     **/
    @Temporal(TemporalType.TIMESTAMP)
    protected Date addtime = new Date();

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getFriendUserName() {
        return friendUserName;
    }

    public void setFriendUserName(String friendUserName) {
        this.friendUserName = friendUserName;
    }

    public String getFriendNickname() {
        return friendNickname;
    }

    public void setFriendNickname(String friendNickname) {
        this.friendNickname = friendNickname;
    }

    public String getFriendAvatarPath() {
        return friendAvatarPath;
    }

    public void setFriendAvatarPath(String friendAvatarPath) {
        this.friendAvatarPath = friendAvatarPath;
    }

    public String getFriendAvatarName() {
        return friendAvatarName;
    }

    public void setFriendAvatarName(String friendAvatarName) {
        this.friendAvatarName = friendAvatarName;
    }

    public Date getAddtime() {
        return addtime;
    }

    public void setAddtime(Date addtime) {
        this.addtime = addtime;
    }

    public String getFriendAccount() {
        return friendAccount;
    }

    public void setFriendAccount(String friendAccount) {
        this.friendAccount = friendAccount;
    }


}
