package cms.constant.enums;

import cms.constant.BaseEnums;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/6/2 18:14
 */
public enum TopicTypeEnums implements BaseEnums {
    Beginners(1, "Beginners"),
    DIYers(10, "DIYers"),
    Workshops(20, "Workshops"),
    ;


    private Integer code;
    private String msg;

    TopicTypeEnums(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private final static Map<Integer, String> map;

    static {
        map = Arrays.stream(TopicTypeEnums.values()).collect(Collectors.toMap(TopicTypeEnums::getCode, TopicTypeEnums::getMsg));
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    public static Optional<String> getMsg(Integer code) {
        return Optional.ofNullable(map.get(code));
    }

    public static Map<Integer, String> getMap() {
        return map;
    }
}
