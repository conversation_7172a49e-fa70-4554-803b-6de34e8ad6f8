package cms.web.action.api;

import cms.bean.RequestResult;
import cms.bean.setting.EditorTag;
import cms.bean.user.AccessUser;
import cms.constant.ErrorCode;
import cms.handle.MsgException;
import cms.service.aliyun.OssFileChangeService;
import cms.utils.JsonUtils;
import cms.utils.threadLocal.AccessUserThreadLocal;
import cms.web.action.setting.SettingManage;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Optional;

import static cms.constant.FileConstant.SIZE_SPLIT;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/12 11:15
 */
@Controller
@RequestMapping("api/common")
@Validated
@Slf4j
public class CommonApiController {

    @Resource
    SettingManage settingManage;
    @Autowired
    private OssFileChangeService ossFileChangeService;

    @ResponseBody
    @RequestMapping(value = "/topic/uploadImage", method = RequestMethod.POST)
    public RequestResult uploadImage(MultipartFile file) {
        Optional<EditorTag> editorTagOptional = Optional.ofNullable(settingManage.readEditorTag());
        String userName = Optional.ofNullable(AccessUserThreadLocal.get()).map(AccessUser::getUserName).orElse("");
        String json = ossFileChangeService.upload(errorMessageService -> {
            Long size = file.getSize();
            editorTagOptional.map(EditorTag::getImageSize).filter(o -> size / SIZE_SPLIT <= o).orElseThrow(() -> new MsgException(errorMessageService.getMessage(ErrorCode.C_2_0001_0013, String.valueOf(size / SIZE_SPLIT))));
        }, file, editorTagOptional.map(EditorTag::getImageFormat), userName, Boolean.FALSE, "topic", Optional.ofNullable(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));

        RequestResult requestResult = RequestResult.ok("");
        Optional.ofNullable(json).map(o -> (Map<String, Object>) JsonUtils.toObject(o, Map.class)).ifPresent(m -> {
            Optional.ofNullable(m).map(o -> (String) o.get("message")).filter(Strings::isNotEmpty).map(requestResult::error).orElseGet(() -> {
                Map<String, Object> map = Maps.newHashMap();
                map.put("url", m.get("url"));
                requestResult.setData(map);
                return requestResult;
            });
        });
        return RequestResult.ok(requestResult);
    }
}
