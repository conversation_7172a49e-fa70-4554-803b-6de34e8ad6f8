package cms.web.action.user;

import cms.bean.*;
import cms.bean.user.User;
import cms.bean.user.UserCustom;
import cms.bean.user.UserGrade;
import cms.bean.user.UserInputValue;
import cms.service.setting.SettingService;
import cms.service.user.UserCustomService;
import cms.service.user.UserGradeService;
import cms.service.user.UserService;
import cms.service.user.impl.UserOperationService;
import cms.utils.JsonUtils;
import cms.utils.Verification;
import cms.web.action.fileSystem.FileManage;
import com.alibaba.excel.EasyExcel;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 用户管理 分页显示 查询结果显示
 */
@Controller
public class UserAction {
    //注入业务bean
    @Resource
    UserGradeService userGradeService;
    @Resource
    SettingService settingService;
    @Resource
    UserManage userManage;
    @Resource
    FileManage fileManage;
    //注入业务bean
    @Resource(name = "userServiceBean")
    private UserService userService;
    @Autowired
    private UserOperationService userOperationService;

    /**
     * 用户列表
     *
     * @param formbean
     * @param pageForm
     * @param visible  null或true:正常页面  false:回收站
     * @param model
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/control/user/list")
    public String execute(User formbean, PageForm pageForm, Boolean visible, ModelMap model,
                          HttpServletRequest request, HttpServletResponse response)
            throws Exception {


        //调用分页算法代码
        PageView<User> pageView = new PageView<User>(settingService.findSystemSetting_cache().getBackstagePageNumber(), pageForm.getPage(), 10);
        //当前页
        int firstIndex = (pageForm.getPage() - 1) * pageView.getMaxresult();

        String param = "";//sql参数
        List<Object> paramValue = new ArrayList<Object>();//sql参数值


        if (visible != null && visible == false) {//回收站
            param = " o.state>? ";
            paramValue.add(2);
        } else {//正常页面
            param = " o.state<=? ";
            paramValue.add(2);
        }

        QueryResult<User> qr = userService.findUserByCondition(param, paramValue, firstIndex, pageView.getMaxresult(), false);

        //将查询结果集传给分页List
        pageView.setQueryResult(qr);
        List<UserGrade> userGradeList = userGradeService.findAllGrade();
        if (userGradeList != null && userGradeList.size() > 0) {
            for (User user : pageView.getRecords()) {//取得所有用户
                for (UserGrade userGrade : userGradeList) {//取得所有等级
                    if (user.getPoint() >= userGrade.getNeedPoint()) {
                        user.setGradeName(userGrade.getName());//将等级值设进等级参数里
                        break;
                    }
                }

            }
        }

        if (pageView.getRecords() != null && pageView.getRecords().size() > 0) {
            for (User user : pageView.getRecords()) {//取得所有用户
                if (user.getType() > 10) {
                    user.setPlatformUserId(userManage.platformUserIdToThirdPartyUserId(user.getPlatformUserId()));
                }

            }
            //仅显示指定字段
            List<User> userViewList = new ArrayList<User>();
            for (User user : pageView.getRecords()) {//取得所有用户
                User userView = new User();

                userView.setId(user.getId());
                userView.setUserName(user.getUserName());
                userView.setAccount(user.getAccount());
                userView.setNickname(user.getNickname());
                userView.setCancelAccountTime(user.getCancelAccountTime());
                userView.setAllowUserDynamic(user.getAllowUserDynamic());
                userView.setEmail(user.getEmail());
                userView.setIssue(user.getIssue());
                userView.setMobile(user.getMobile());
                userView.setId(user.getId());
                userView.setRealNameAuthentication(user.isRealNameAuthentication());
                userView.setRegistrationDate(user.getRegistrationDate());
                userView.setRemarks(user.getRemarks());
                userView.setPoint(user.getPoint());
                userView.setDeposit(user.getDeposit());


                userView.setType(user.getType());
                userView.setPlatformUserId(user.getPlatformUserId());
                userView.setState(user.getState());
                userView.setUserVersion(user.getUserVersion());
                userView.setUserRoleNameList(user.getUserRoleNameList());
                userView.setGradeId(user.getGradeId());
                userView.setGradeName(user.getGradeName());
                userView.setAvatarPath(user.getAvatarPath(fileManage.fileOssAddress()));
                userView.setAvatarName(user.getAvatarName());
                userViewList.add(userView);
            }
            pageView.setRecords(userViewList);
        }

        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, pageView));
    }


    /**
     * 搜索用户列表
     *
     * @param pageForm
     * @param model
     * @param searchType             查询类型
     * @param account                账号     *
     * @param start_point            起始积分
     * @param end_point              结束积分
     * @param start_registrationDate 起始注册日期
     * @param end_registrationDate   结束注册日期
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/control/user/search")
    public String search(ModelMap model, PageForm pageForm,
                         Integer searchType, String account,
                         String start_point, String end_point,
                         String start_registrationDate, String end_registrationDate,
                         HttpServletRequest request, HttpServletResponse response)
            throws Exception {

        if (searchType == null) {//如果查询类型为空，则默认为用户名查询
            searchType = 1;
        }
        //错误
        Map<String, String> error = new HashMap<String, String>();
        //调用分页算法代码
        PageView<User> pageView = new PageView
                <User>(settingService.findSystemSetting_cache().getBackstagePageNumber(), pageForm.getPage(), 10);
        userOperationService.getUserPage(pageView, error, pageForm, searchType, account, start_point, end_point, start_registrationDate, end_registrationDate, request);

        if (error.size() > 0) {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
        } else {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, pageView));
        }
    }
}
