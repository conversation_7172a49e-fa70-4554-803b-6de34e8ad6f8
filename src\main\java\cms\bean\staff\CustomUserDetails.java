package cms.bean.staff;

import org.springframework.security.core.userdetails.UserDetails;

import java.util.Set;

/**
 * 实现了UserDetails，扩展几项信息
 */
public interface CustomUserDetails extends UserDetails {

    //用户id
	String getUserId();

    //用户账户
	String getUserAccount();

    //用户名
	String getUserName();

    //用户密码
	String getUserPassword();

    //用户描述或简介
	String getUserDesc();

    //用户是否能用
	Boolean getEnabled();

    //是否超级用户
	boolean getIssys();

    //所属的单位
//	public String getUserDept();

    //用户职位
	String getUserDuty();

    //用户分管的子系统
//	public String getSubSystem();

    //用户相对应的角色集
	Set<SysUsersRoles> getUsersRolesSet();

}
