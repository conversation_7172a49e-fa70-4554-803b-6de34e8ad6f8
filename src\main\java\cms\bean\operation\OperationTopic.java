package cms.bean.operation;

import cms.bean.BaseUserDel;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/30 15:42
 */
@Entity
@Table(name = "operation_topic", indexes = {@Index(name = "idx_tId_oId", columnList = "topicId,sponsor,deleted,operationId"), @Index(name = "idx_operationId", columnList = "operationId")})
@Data
public class OperationTopic extends BaseUserDel implements Serializable {

    private Long operationId;

    private Long topicId;

    private Boolean sponsor;

    public static OperationTopic build(Long operationId, Long topicId) {
        OperationTopic operationTopic = new OperationTopic();
        operationTopic.setOperationId(operationId);
        operationTopic.setTopicId(topicId);
        operationTopic.setSponsor(false);
        return operationTopic;
    }
}
