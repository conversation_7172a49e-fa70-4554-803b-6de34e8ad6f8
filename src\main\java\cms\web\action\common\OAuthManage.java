package cms.web.action.common;

import cms.bean.user.*;
import cms.service.user.UserService;
import cms.utils.IpAddress;
import cms.utils.WebUtil;
import cms.utils.threadLocal.AccessUserThreadLocal;
import cms.web.action.fileSystem.FileManage;
import cms.web.action.membershipCard.MembershipCardGiftTaskManage;
import cms.web.action.user.UserLoginLogManage;
import cms.web.action.user.UserManage;
import com.google.common.collect.Maps;
import org.apache.logging.log4j.util.Strings;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

/**
 * 开放授权管理
 */
@Component("oAuthManage")
public class OAuthManage {

    public static Map<Long, Object> synchronizedLockMap = Maps.newConcurrentMap();

    @Resource
    OAuthManage oAuthManage;
    @Resource
    UserManage userManage;
    @Resource
    UserLoginLogManage userLoginLogManage;
    @Resource
    UserService userService;
    @Resource
    FileManage fileManage;
    @Resource
    MembershipCardGiftTaskManage membershipCardGiftTaskManage;

    /**
     * 添加刷新令牌
     *
     * @param refreshToken 刷新令牌
     */
    @CachePut(value = "oAuthManage_cache_refreshToken_userId", key = "#userId")
    public String addRefreshTokenByUserId(Long userId, String refreshToken) {
        return refreshToken;
    }

    /**
     * 添加刷新令牌
     *
     * @param refreshToken
     * @param refreshUser  刷新令牌
     */
    @CachePut(value = "oAuthManage_cache_refreshToken", key = "#refreshToken")
    public RefreshUser addRefreshToken(String refreshToken, RefreshUser refreshUser) {
        return refreshUser;
    }

    /**
     * 添加访问令牌
     *
     * @param accessToken
     * @param user        仅userId userName两个字段有值
     */
    @CachePut(value = "oAuthManage_cache_accessToken", key = "#accessToken")
    public AccessUser addAccessToken(String accessToken, AccessUser accessUser) {
        return accessUser;
    }

    /**
     * 添加第三方用户的唯一标识(OpenId绑定到刷新令牌)
     *
     * @param openId       第三方用户的唯一标识
     * @param refreshToken 刷新令牌号
     */
    @CachePut(value = "oAuthManage_cache_openId", key = "#openId")
    public String addOpenId(String openId, String refreshToken) {
        return refreshToken;
    }

    /**
     * 根据刷新令牌获取刷新用户
     *
     * @param userId
     * @return
     */
    @Cacheable(value = "oAuthManage_cache_refreshToken_userId", key = "#userId")
    public String getRefreshTokenByUserId(Long userId) {
        return null;
    }

    /**
     * 根据刷新令牌获取刷新用户
     *
     * @param refreshToken
     * @return
     */
    @Cacheable(value = "oAuthManage_cache_refreshToken", key = "#refreshToken")
    public RefreshUser getRefreshUserByRefreshToken(String refreshToken) {
        return null;
    }

    /**
     * 根据访问令牌获取用户
     *
     * @param accessToken
     * @return
     */
    @Cacheable(value = "oAuthManage_cache_accessToken", key = "#accessToken")
    public AccessUser getAccessUserByAccessToken(String accessToken) {
        return null;
    }

    /**
     * 根据第三方用户的唯一标识获取刷新令牌号
     *
     * @param openId 第三方用户的唯一标识
     * @return
     */
    @Cacheable(value = "oAuthManage_cache_openId", key = "#openId")
    public String getRefreshTokenByOpenId(String openId) {
        return null;
    }


    /**
     * 删除用户id对应刷新令牌
     *
     * @param userId
     * @return
     */
    @CacheEvict(value = "oAuthManage_cache_refreshToken_userId", key = "#userId")
    public void deleteRefreshTokenByUserId(Long userId) {
    }


    /**
     * 删除刷新令牌
     *
     * @param refreshToken
     * @return
     */
    @CacheEvict(value = "oAuthManage_cache_refreshToken", key = "#refreshToken")
    public void deleteRefreshToken(String refreshToken) {
    }

    /**
     * 删除访问令牌
     *
     * @param accessToken
     * @return
     */
    @CacheEvict(value = "oAuthManage_cache_accessToken", key = "#accessToken")
    public void deleteAccessToken(String accessToken) {
    }


    /**
     * 删除第三方用户的唯一标识
     *
     * @param openId 第三方用户的唯一标识
     * @return
     */
    @CacheEvict(value = "oAuthManage_cache_openId", key = "#openId")
    public void deleteOpenId(String openId) {
    }

    /**
     * 发送邮件
     *
     * @param email
     * @param type
     * @return
     */
    @CachePut(value = "oAuthManage_cache_email", key = "#email+'_'+#type")
    public Boolean addEmailMsg(String email, String type) {
        return true;
    }

    /**
     * 获取是否60s内有获取邮件
     *
     * @param email
     * @param type
     * @return
     */
    @Cacheable(value = "oAuthManage_cache_email", key = "#email+'_'+#type")
    public Boolean getEmailMsg(String email, String type) {
        return null;
    }

    /**
     * 获取登录用户
     */
    public AccessUser getUserName(HttpServletRequest request) {
        //从Cookie获取
        String accessToken = WebUtil.getCookieByName(request, "cms_accessToken");
        //从Header获取
        UserAuthorization headerUserAuthorization = WebUtil.getAuthorization(request);
        if (headerUserAuthorization != null) {
            accessToken = headerUserAuthorization.getAccessToken();
        }
        if (accessToken != null && !"".equals(accessToken.trim())) {
            AccessUser accessUser = oAuthManage.getAccessUserByAccessToken(accessToken.trim());
            return accessUser;
        }
        return null;
    }


    /**
     * 令牌续期
     *
     * @param oldRefreshToken 旧刷新令牌号
     * @param refreshUser     刷新用户
     * @param newAccessToken  新访问令牌  值为UUIDUtil.getUUID32()
     * @param newRefreshToken 新刷新令牌  值为UUIDUtil.getUUID32()
     * @param request
     * @param response
     * @return 返回true表示续期成功
     */
    public boolean tokenRenewal(String oldRefreshToken, RefreshUser refreshUser, String newAccessToken, String newRefreshToken,
                                HttpServletRequest request, HttpServletResponse response) {
        UserState userState = userManage.query_userState(refreshUser.getUserName().trim());//用户状态
        if (userState == null || !userState.getSecurityDigest().equals(refreshUser.getSecurityDigest())) {
            return false;
        }


        User user = userManage.query_cache_findUserById(refreshUser.getUserId());
        if (user != null) {
            //账号
            String account = user.getAccount();
            //呢称
            String nickname = user.getNickname();
            //头像路径
            String avatarPath = user.getAvatarPath(fileManage.fileOssAddress());
            //头像名称
            String avatarName = user.getAvatarName();

            AccessUser accessTokenUser = new AccessUser(refreshUser.getUserId(), refreshUser.getUserName(), account, nickname, avatarPath, avatarName, refreshUser.getSecurityDigest(), refreshUser.isRememberMe(), refreshUser.getOpenId());
            refreshUser.setAccessToken(newAccessToken);
            initToken(user, Optional.ofNullable(accessTokenUser), Optional.ofNullable(refreshUser), newAccessToken, newRefreshToken, refreshUser.getOpenId(), avatarPath, refreshUser.isRememberMe());

            if (refreshUser.getOpenId() != null && !"".equals(refreshUser.getOpenId().trim())) {
                //第三方openId
                oAuthManage.addOpenId(refreshUser.getOpenId(), newRefreshToken);
            }


            //将旧的刷新令牌的accessToken设为0
            oAuthManage.addRefreshToken(oldRefreshToken, new RefreshUser("0", refreshUser.getUserId(), refreshUser.getUserName(), account, nickname, avatarPath, avatarName, refreshUser.getSecurityDigest(), refreshUser.isRememberMe(), refreshUser.getOpenId()));
            AccessUserThreadLocal.set(new AccessUser(refreshUser.getUserId(), refreshUser.getUserName(), account, nickname, avatarPath, avatarName, refreshUser.getSecurityDigest(), refreshUser.isRememberMe(), refreshUser.getOpenId()));


            //存放时间 单位/秒
            int maxAge = 0;
            if (refreshUser.isRememberMe()) {
                maxAge = WebUtil.cookieMaxAge;//默认Cookie有效期
            }

            //将访问令牌添加到Cookie
            WebUtil.addCookie(response, "cms_accessToken", newAccessToken, maxAge);
            //将刷新令牌添加到Cookie
            WebUtil.addCookie(response, "cms_refreshToken", newRefreshToken, maxAge);

            //写入登录日志
            UserLoginLog userLoginLog = new UserLoginLog();
            userLoginLog.setId(userLoginLogManage.createUserLoginLogId(user.getId()));
            userLoginLog.setIp(IpAddress.getClientIpAddress(request));
            userLoginLog.setTypeNumber(20);//续期
            userLoginLog.setUserId(user.getId());
            userLoginLog.setLogonTime(new Date());
            Object new_userLoginLog = userLoginLogManage.createUserLoginLogObject(userLoginLog);
            userService.saveUserLoginLog(new_userLoginLog);

            return true;
        }


        return false;
    }

    public void initToken(User user, Optional<AccessUser> accessTokenUser, Optional<RefreshUser> refreshTokenUser,
                          String accessToken, String refreshToken, String openId, String avatarPath, Boolean rememberMe) {
        synchronizedLockMap.putIfAbsent(user.getId(), new Object());
        synchronized (synchronizedLockMap.get(user.getId())) {
            Optional.ofNullable(oAuthManage.getRefreshTokenByUserId(user.getId())).filter(Strings::isNotEmpty).ifPresent(rt -> {
                RefreshUser refreshUser = oAuthManage.getRefreshUserByRefreshToken(rt);
                oAuthManage.deleteRefreshToken(rt);
                Optional.ofNullable(refreshUser).map(RefreshUser::getAccessToken).ifPresent(oAuthManage::deleteAccessToken);
            });
            oAuthManage.addAccessToken(accessToken, accessTokenUser.orElseGet(() -> new AccessUser(user.getId(), user.getUserName(), user.getAccount(), user.getNickname(), avatarPath, user.getAvatarName(), user.getSecurityDigest(), rememberMe, openId)));
            oAuthManage.addRefreshToken(refreshToken, refreshTokenUser.orElseGet(() -> new RefreshUser(accessToken, user.getId(), user.getUserName(), user.getAccount(), user.getNickname(), avatarPath, user.getAvatarName(), user.getSecurityDigest(), rememberMe, openId)));
            oAuthManage.addRefreshTokenByUserId(user.getId(), refreshToken);
        }
    }

    public void clearToken(Long userId, Optional<String> _refreshToken) {
        synchronizedLockMap.putIfAbsent(userId, new Object());
        synchronized (synchronizedLockMap.get(userId)) {
            Optional.ofNullable(oAuthManage.getRefreshTokenByUserId(userId)).filter(Strings::isNotEmpty).filter(rt -> _refreshToken.map(rt::equals).orElse(Boolean.TRUE)).ifPresent(rt -> {
                RefreshUser refreshUser = oAuthManage.getRefreshUserByRefreshToken(rt);
                oAuthManage.deleteRefreshToken(rt);
                Optional.ofNullable(refreshUser).map(RefreshUser::getAccessToken).ifPresent(oAuthManage::deleteAccessToken);
                oAuthManage.deleteRefreshTokenByUserId(userId);
            });
        }
    }
}
