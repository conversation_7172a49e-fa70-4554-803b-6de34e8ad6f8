package cms.config;

import cms.bean.message.EmailRemind;
import cms.bean.user.User;
import cms.constant.enums.RemindTypeEnums;
import cms.service.message.impl.EmailRemindService;
import cms.utils.EmailUtil;
import cms.utils.SpringUtils;
import cms.web.action.user.UserManage;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/3 17:31
 */
@Component
@Slf4j
@Order(2)
public class EmailRemindRunner implements ApplicationRunner {

    private final static int MAX_RESULT = 200;

    private static int adjustIntervalMinutes = 10;

    private static List<Future> futures = Lists.newArrayList();
    private static ExecutorService emailRemindExecutors = new ThreadPoolExecutor(1, 5, adjustIntervalMinutes * 2, TimeUnit.MINUTES, new LinkedBlockingQueue<Runnable>(1)
            , new DefaultThreadFactory(Optional.of("emailRemind")));

    private static ExecutorService adjustThreadExecutors = new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MINUTES, new LinkedBlockingQueue<Runnable>(1)
            , new DefaultThreadFactory(Optional.of("adjustThread")));
    private static AtomicReference<LocalDateTime> lastAdjustThreadTime = new AtomicReference<LocalDateTime>(LocalDateTime.now());
    private static Semaphore adjustSmp = new Semaphore(0);
    private static int addThreadLimit = EmailRemindService.EmailRemindQueueSize / 2;
    private static int subThreadLimit = EmailRemindService.EmailRemindQueueSize / 10;
    private static AtomicBoolean isAddThread = new AtomicBoolean(Boolean.TRUE);
    private static int adjustSmpWaitTime = 720;

    @Override
    public void run(ApplicationArguments args) {
        Future future = emailRemindExecutors.submit(() -> {
            this.emailRemindRun();
        });
        adjustThreadExecutors.submit(() -> {
            this.adjustEmailRemindThread();
        });
    }


    private void emailRemindRun() {
        log.info(">>>>>>>> start email remind {} <<<<<<<<", Thread.currentThread().getName());
        EmailRemindService emailRemindService = SpringUtils.getBean(EmailRemindService.class);
        emailRemindService.initEmailRemindSendCountMap();
        UserManage userManage = SpringUtils.getBean(UserManage.class);
        while (true) {
            Optional.of(emailRemindService.getSendTime()).filter(o -> LocalDate.now().compareTo(o) != 0).filter(emailRemindService::removeSendTime).ifPresent(o -> emailRemindService.removeAll());
            EmailRemind emailRemind = null;
            try {
                if (Thread.currentThread().isInterrupted()) {
                    log.info(">>>>>>>> currency thread {} interrupted,return thread <<<<<<<<", Thread.currentThread().getName());
                    return;
                }
                emailRemind = EmailRemindService.takeEmailRemind();
                EmailRemind finalEmailRemind = emailRemind;
                List<String> emailRemindTrue = Optional.ofNullable(userManage.getEmailRemindCache()).map(l -> l.stream().map(User::getEmail).collect(Collectors.toList())).orElseGet(() -> Lists.newArrayList());
                List<String> noSendEmail = emailRemindService.getNoSendNum();
                if (emailRemindTrue.size() > 0 && emailRemindTrue.stream().filter(o -> noSendEmail.contains(o) || noSendEmail.contains(EmailRemindService.isNoSendEmailRemindKey(o, finalEmailRemind.getRemindType()))).count() == emailRemindTrue.size()) {
                    continue;
                }
                int queueSize = EmailRemindService.getEmailRemindSize();
                Optional.of(queueSize).filter(o -> o >= addThreadLimit).map(o -> lastAdjustThreadTime.get())
                        .filter(o -> o.plusMinutes(adjustIntervalMinutes).compareTo(LocalDateTime.now()) <= 0)
                        .filter(o -> lastAdjustThreadTime.compareAndSet(o, LocalDateTime.now())).map(o -> isAddThread.compareAndSet(isAddThread.get(), Boolean.TRUE)).ifPresent(o -> adjustSmp.release());
                Optional.of(queueSize).filter(o -> futures.size() > 0 && o <= subThreadLimit).map(o -> lastAdjustThreadTime.get())
                        .filter(o -> o.plusMinutes(adjustIntervalMinutes).compareTo(LocalDateTime.now()) <= 0)
                        .filter(o -> lastAdjustThreadTime.compareAndSet(o, LocalDateTime.now())).map(o -> isAddThread.compareAndSet(isAddThread.get(), Boolean.FALSE)).ifPresent(o -> adjustSmp.release());
                int page = 1;
                while (true) {
                    int firstIndex = (page - 1) * MAX_RESULT;
                    List<User> receiverUsers = emailRemind.getReceiverUser().map(ListUtil::of).orElseGet(() -> emailRemindService.findEmailByFollow(finalEmailRemind.getBusinessUserId(), finalEmailRemind.getBusinessUserName(), firstIndex, MAX_RESULT));
                    receiverUsers.stream().filter(o -> emailRemindTrue.contains(o.getEmail())).filter(o -> emailRemindService.isSendEmail(o.getEmail(), finalEmailRemind.getRemindType())).forEach(o -> {
                        try {
                            EmailRemindService.SenMessageBean senMessageBean = emailRemindService.getSendMsg(o, finalEmailRemind);
                            EmailUtil.send(o.getEmail(), senMessageBean.getTitle(), senMessageBean.getContent());
                            emailRemindService.addEmailRemindCache(o.getEmail(), finalEmailRemind.getRemindType());
                            log.info("线程 {} form {} to email {} 发送类型 {} 信息！", Thread.currentThread().getName(), finalEmailRemind.getBusinessUserId(), o, RemindTypeEnums.getDesc(finalEmailRemind.getRemindType()));
                        } catch (Throwable e) {
                            emailRemindService.decrementSendNum(o.getEmail(), finalEmailRemind.getRemindType());
                            log.error("sendEmailRemind messaging error!" + JSON.toJSONString(finalEmailRemind), e);
                        } finally {
                        }
                    });
                    if (emailRemind.getReceiverUser().isPresent() || CollUtil.isEmpty(receiverUsers)) {
                        break;
                    }
                    page++;
                }
            } catch (InterruptedException e) {
                log.error("sendEmailRemind Interrupted error!" + JSON.toJSONString(emailRemind), e);
                EmailRemindService.putEmailRemindNoThrow(emailRemind);
            } catch (Exception e) {
                log.error("sendEmailRemind error!" + JSON.toJSONString(emailRemind), e);
                EmailRemindService.putEmailRemindNoThrow(emailRemind);
            }
        }
    }

    private void adjustEmailRemindThread() {
        while (true) {
            try {
                boolean isAdjust = adjustSmp.tryAcquire(adjustSmpWaitTime, TimeUnit.MINUTES);
                if (isAdjust) {
                    if (isAddThread.get()) {
                        log.info(">>>>>>>> start add thread <<<<<<<<");
                        try {
                            Future future = emailRemindExecutors.submit(() -> {
                                this.emailRemindRun();
                            });
                            futures.add(future);
                            log.info(">>>>>>>> add thread success,currency thread size {} <<<<<<<<", futures.size() + 1);
                        } catch (Throwable e) {
                            log.error("add thread error!", e);
                        }
                    } else {
                        log.info(">>>>>>>> start sub thread <<<<<<<<");
                        try {
                            Optional.ofNullable(futures).filter(o -> o.size() > 0).map(o -> o.get(o.size() - 1)).ifPresent(o -> {
                                o.cancel(true);
                                futures.remove(o);
                            });
                            log.info(">>>>>>>> sub thread success,currency thread size {} <<<<<<<<", futures.size() + 1);
                        } catch (Throwable e) {
                            log.error("sub thread error!", e);
                        }
                    }
                } else {
                    log.info(">>>>>>>> {} minutes,not need adjust thread,currency thread size {} <<<<<<<<", adjustSmpWaitTime, futures.size() + 1);
                }
            } catch (InterruptedException e) {
                log.error("adjustThread Interrupted error!", e);
            }
        }
    }

    static class DefaultThreadFactory implements ThreadFactory {
        private static final AtomicInteger poolNumber = new AtomicInteger(1);
        private final ThreadGroup group;
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        DefaultThreadFactory(Optional<String> poolName) {
            SecurityManager s = System.getSecurityManager();
            group = (s != null) ? s.getThreadGroup() :
                    Thread.currentThread().getThreadGroup();
            namePrefix = "pool-" +
                    poolNumber.getAndIncrement() + poolName.map(o -> "-" + o).orElse("") +
                    "-thread-";
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(group, r,
                    namePrefix + threadNumber.getAndIncrement(),
                    0);
            if (t.isDaemon()) {
                t.setDaemon(false);
            }
            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY);
            }
            return t;
        }
    }
}
