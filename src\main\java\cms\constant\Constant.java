package cms.constant;

import cn.hutool.core.collection.ListUtil;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Semaphore;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/4/20 14:14
 */
public interface Constant {

    String DATE_YMD_HMS = "yyyy-MM-dd HH:mm:ss";
    String DATE_YMD = "yyyy-MM-dd";

    String business_avatar = "avatar";
    String DEFAULT = "default";
    String EMAIL = "email";

    int WAIT_SECONDS = 30;

    int MAX_GRADE = 2;

    List<String> sortWays = Arrays.asList("desc", "asc");

    List<String> booleanString = Arrays.asList("true", "false", "1", "0");

    String EMAIL_REMIND_TEMPLATE_KEY_RECEIVEDNICKNAME = "receivedNickName";
    String EMAIL_REMIND_TEMPLATE_KEY_MESSAGEPAGE = "messagePage";

    String SUFFIX_MP4 = "mp4";

    String MARK_UPDATE_STATUS_QUESTION = "question";

    String PAGE_MAX_RESULT = "maxResult";


    int REBUILD_TOPIC_INDEX_SECONDS = 20;
    int UPDATE_TOPIC_INDEX_SECONDS = 40;
    Semaphore TOPIC_INDEX_SH = new Semaphore(1);
    int REBUILD_QUESTION_INDEX_SECONDS = 20;
    int UPDATE_QUESTION_INDEX_SECONDS = 40;
    Semaphore QUESTION_INDEX_SH = new Semaphore(1);

    int TOPIC_TAG_GRADE_ONE = 1;
    int TOPIC_TAG_GRADE_TWO = 2;
    int QUESTION_TAG_GRADE_ONE = TOPIC_TAG_GRADE_ONE;

    String UPLOAD_MEDIA_IMAGE_DIR = "mediaImage";
    List<String> MEDIA_IMAGE_FORMAT = ListUtil.toList("png");
    String MEDIA_IMAGE_SUFFIX = "png";

    String ACTIVE_PRO = "pro";

    // 本地账号密码用户
    Integer USER_TYPE_LOCAL = 10;

    Integer TOPIC_STATUS_RELEASE = 20;

    Integer COMMENT_STATUS_RELEASE = 20;

    Integer POINT_OPERATION_USER_TYPE_uSER = 2; // 操作用户类型  0:系统  1: 员工  2:会员
    Integer POINT_MODULE_COMMENT = 200; // 模块 100.话题  200.评论  300.回复 400.积分解锁话题隐藏内容 500.会员卡订单支付 600.账户充值 700.问题 800.答案 900.答案回复 1000.悬赏积分 1100.采纳答案 1200.调整赏金

    Integer CLOSE_SITE_ONLY_READ = 2; // 关闭站点 1.打开 2.只读模式 3.全站关闭
}
