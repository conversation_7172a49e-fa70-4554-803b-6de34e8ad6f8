package cms.config;

import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.time.ZoneOffset;
import java.util.TimeZone;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/25 17:18
 */
@Configuration
public class GlobalZoneConfig {

    @PostConstruct
    void zone() {
        TimeZone.setDefault(TimeZone.getTimeZone(ZoneOffset.of("+0")));
    }
}
