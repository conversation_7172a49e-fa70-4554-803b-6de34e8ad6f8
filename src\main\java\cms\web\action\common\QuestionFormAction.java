package cms.web.action.common;


import cms.bean.setting.EditorTag;
import cms.bean.setting.SystemSetting;
import cms.bean.user.AccessUser;
import cms.bean.user.User;
import cms.constant.ErrorCode;
import cms.constant.FileConstant;
import cms.handle.CustomException;
import cms.service.aliyun.OssFileChangeService;
import cms.service.aliyun.OssService;
import cms.service.messageSource.ErrorMessageService;
import cms.service.question.impl.QuestionFormService;
import cms.service.setting.SettingService;
import cms.utils.JsonUtils;
import cms.utils.WebUtil;
import cms.utils.threadLocal.AccessUserThreadLocal;
import cms.web.action.setting.SettingManage;
import cms.web.action.user.UserManage;
import cms.web.action.user.UserRoleManage;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 问题接收表单
 */
@Controller
@RequestMapping("user/control/question")
@Slf4j
public class QuestionFormAction {

    private static Map<Long, Object> appendQuestionLock = Maps.newConcurrentMap();

    @Resource
    SettingManage settingManage;
    @Resource
    SettingService settingService;
    @Resource
    UserManage userManage;
    @Resource
    UserRoleManage userRoleManage;
    @Autowired
    private QuestionFormService questionFormService;
    @Autowired
    private ErrorMessageService errorMessageService;
    @Autowired
    private OssFileChangeService ossFileChangeService;

    /**
     * 问题  添加
     *
     * @param model
     * @param tagId    标签Id
     * @param title    标题
     * @param content  内容
     * @param amount   悬赏金额
     * @param point    悬赏积分
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public String add(ModelMap model, long oneTagId, @RequestParam(value = "tagId[]", required = false) Long[] tagId, String title, String content, String amount, String point,
                      String token, String captchaKey, String captchaValue, String jumpUrl,
                      RedirectAttributes redirectAttrs,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {
        return questionFormService.add(model, oneTagId, tagId, title, content, amount, point, token, captchaKey, captchaValue, jumpUrl, redirectAttrs, request, response);
    }

    /**
     * 问题  追加
     *
     * @param model
     * @param questionId 问题Id
     * @param content    内容
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/appendQuestion", method = RequestMethod.POST)
    public String appendQuestion(ModelMap model, Long questionId, String content,
                                 String token, String captchaKey, String captchaValue, String jumpUrl,
                                 RedirectAttributes redirectAttrs,
                                 HttpServletRequest request, HttpServletResponse response) throws Exception {
        Object o = null;
        try {
            if (null != (o = appendQuestionLock.putIfAbsent(questionId, new Object()))) {
                throw new CustomException(ErrorCode.C_2_0008_0040, "question");
            }
            return questionFormService.appendQuestion(model, questionId, content, token, captchaKey, captchaValue, jumpUrl, redirectAttrs, request, response);
        } finally {
            if (null == o) {
                appendQuestionLock.remove(questionId);
            }
        }
    }

    /**
     * 文件上传
     *
     * @param dir:     上传类型，分别为image、file
     * @param fileName 文件名称 预签名时有值
     *                 员工发话题 上传文件名为UUID + a + 员工Id
     *                 用户发话题 上传文件名为UUID + b + 用户Id
     */
    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    @ResponseBody//方式来做ajax,直接返回字符串
    public String
    upload(ModelMap model, String dir, String fileName,
           MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Optional<EditorTag> editorTagOptional = Optional.ofNullable(settingManage.readQuestionEditorTag());
        Optional<AccessUser> accessUser = Optional.ofNullable(AccessUserThreadLocal.get());
        String userName = accessUser.map(AccessUser::getUserName).orElse("");
        Optional<User> user = Optional.ofNullable(userManage.query_cache_findUserByUserName(userName));
        String date = new DateTime().toString("yyyy-MM-dd");
        return ossFileChangeService.upload(errorMessageService -> {
            Optional.ofNullable(dir).filter(FileConstant.FILE_TYPE_IMG::equals).orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0016));
            Optional<SystemSetting> systemSetting = Optional.ofNullable(settingService.findSystemSetting_cache());
            // 只读模式不允许提交数据
            systemSetting.map(SystemSetting::getCloseSite).filter(o -> !o.equals(2)).orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0001));
            //如果全局不允许提交,如果实名用户才允许提交提问
            systemSetting.filter(o -> o.isAllowQuestion()).filter(o -> o.isRealNameUserAllowQuestion() == Boolean.FALSE || user.map(User::isRealNameAuthentication).orElse(Boolean.TRUE)).orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0015));
        }, file, editorTagOptional, dir, accessUser.map(o -> String.format("b%s", o.getUserId())), "question", Optional.of(date + OssService.separator + "image"));
    }


    /**
     * 采纳答案
     *
     * @param model
     * @param answerId 答案Id
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/adoptionAnswer", method = RequestMethod.POST)
    public String adoptionAnswer(ModelMap model, Long answerId, String token, String jumpUrl,
                                 RedirectAttributes redirectAttrs,
                                 HttpServletRequest request, HttpServletResponse response) throws Exception {
        return questionFormService.adoptionAnswer(model, answerId, token, jumpUrl, redirectAttrs, request, response);
    }

    /**
     * 取消采纳答案
     *
     * @param model
     * @param answerId
     * @param token
     * @param jumpUrl
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/cancelAdoptionAnswer", method = RequestMethod.POST)
    public String cancelAdoptionAnswer(ModelMap model, Long answerId, String token, String jumpUrl,
                                       HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, Object> returnValue = new HashMap<String, Object>();//返回值
        Map<String, String> returnError = new HashMap<String, String>();//错误
        try {
            questionFormService.cancelAdoptionAnswer(answerId, token, request, response);
            returnValue.put("success", "true");
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } catch (CustomException e) {
            returnError.put(e.getFiledName(), errorMessageService.getMessage(e.getCode()));
        } catch (Exception e) {
            log.error("cancelAdoptionAnswer error!", e);
            returnError.put("adoptionAnswer", errorMessageService.getMessage(ErrorCode.C_2_0001_0500));
        }
        returnValue.put("success", "false");
        returnValue.put("error", returnError);
        WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
        return null;
    }

}
