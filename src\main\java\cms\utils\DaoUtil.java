package cms.utils;

import javax.persistence.Entity;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/4/19 18:21
 */
public class DaoUtil {

    public static <T> String getEntityName(Class<T> entityClass) {
        //默认情况下实体名称为这个类的简单名称,即实体类上的这个标志@Entity
        String entityname = entityClass.getSimpleName();
        //获取实体类Entity注解上的属性,如Entity(name="xxx")这种情况
        Entity entity = entityClass.getAnnotation(Entity.class);
        //判断实体类Entity注解上是否设置了name属性
        if (entity.name() != null && !"".equals(entity.name())) {
            //把实体名称修改为它的属性值
            entityname = entity.name();
        }
        return entityname;
    }
}
