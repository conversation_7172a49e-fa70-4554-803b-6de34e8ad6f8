package cms.constant;

import cms.handle.CustomException;
import org.springframework.http.HttpStatus;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.Random;

import static cms.constant.ErrorCode.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/23 17:16
 */
public class BbsInnerApiAuthUtil {

    public final static String HEADER_CLIENTID = "In-client-id";//Innerapi鉴权信息头（共三个）
    public final static String HEADER_STAMP = "In-stamp";//Innerapi鉴权信息头（共三个）
    public final static String HEADER_SIGN = "In-sign";//Innerapi鉴权信息头（共三个）

    public final static HttpStatus HTTP_STATUS_CODE_AUTH_FAIL = HttpStatus.OK;//鉴权错误的状态码
    public final static HttpStatus HTTP_STATUS_CODE_INNER_ERROR = HttpStatus.OK;//服务端内部错误状态码

    /**
     * 生成HEADER_STAMP值
     */
    public static String genHeaderStamp() {
        long timestamp = System.currentTimeMillis() / 1000;
        return timestamp + "-" + randStr(5);
    }

    /**
     * 生成HEADER_SIGN值
     */
    public static String genHeaderSign(String clientSecret, String stamp) {
        String str = clientSecret + stamp;
        String md5 = DigestUtils.md5DigestAsHex(str.getBytes(StandardCharsets.UTF_8));
        return md5;
    }

    /**
     * 校验 HEADER_STAMP与HEADER_SIGN
     */
    public static void verifyStampAndSign(String clientSecret, String stamp, String sign) {
        stamp = stamp.trim();
        int pos = stamp.indexOf("-");
        if (pos != 10 || stamp.length() < 16) {
            throw new CustomException(C_1_0001_0016, "stamp");
        }
        try {
            long timestamp = Long.parseLong(stamp.substring(0, pos));
            long now = System.currentTimeMillis() / 1000;
            long diffSecond = timestamp - now;
            if (diffSecond < -900 || diffSecond > 900) {
                throw new CustomException(C_1_0001_0017, "diff");
            }
        } catch (Throwable e) {
            throw new CustomException(C_1_0001_0016, "stamp");
        }

        String rightSign = genHeaderSign(clientSecret, stamp);
        if (!rightSign.equalsIgnoreCase(sign)) {
            throw new CustomException(C_1_0001_0018, "sign");
        }
    }

    public static String randStr(int len) {
        String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random1 = new Random();
        char[] chars = new char[len];
        for (int i = 0; i < len; i++) {
            int number = random1.nextInt(str.length());
            chars[i] = str.charAt(number);
        }
        return new String(chars);
    }
}
