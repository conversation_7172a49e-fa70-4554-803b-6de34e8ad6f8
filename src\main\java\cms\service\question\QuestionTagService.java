package cms.service.question;

import cms.bean.question.QuestionTag;
import cms.service.besa.DAO;

import java.util.List;

/**
 * 问题标签接口
 *
 * <AUTHOR>
 */
public interface QuestionTagService extends DAO<QuestionTag> {
    /**
     * 根据Id查询标签
     *
     * @param questionTagId 标签Id
     * @return
     */
    QuestionTag findById(Long questionTagId);

    /**
     * 根据标签查询所有父类标签
     *
     * @param questionTag 标签
     * @return
     */
    List<QuestionTag> findAllParentById(QuestionTag questionTag);

    /**
     * 根据标签Id查询子标签(下一节点)
     *
     * @param questionTagId 标签Id
     * @return
     */
    List<QuestionTag> findChildTagById(Long questionTagId);

    /**
     * 查询标签的QA数量
     * @param tagName
     * @return
     */
    int findTagCountTopic(String tagName);

    /**
     * 查询所有问题标签
     *
     * @return
     */
    List<QuestionTag> findAllQuestionTag();

    /**
     * 查询所有问题标签 - 缓存
     *
     * @return
     */
    List<QuestionTag> findAllQuestionTag_cache();

    /**
     * 保存标签
     *
     * @param questionTag
     */
    void saveQuestionTag(QuestionTag questionTag);

    /**
     * 修改标签
     *
     * @param questionTag
     * @return
     */
    Integer updateQuestionTag(QuestionTag questionTag);

    /**
     * 删除标签
     *
     * @param questionTag 标签
     */
    Integer deleteQuestionTag(QuestionTag questionTag);
}
