package cms.service.question;


import cms.bean.platformShare.QuestionRewardPlatformShare;
import cms.bean.question.Answer;
import cms.bean.question.AnswerReply;
import cms.service.besa.DAO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 答案管理接口
 */
public interface AnswerService extends DAO<Answer> {
    /**
     * 根据答案Id查询答案
     *
     * @param answerId 答案Id
     * @return
     */
    Answer findByAnswerId(Long answerId);

    /**
     * 根据答案Id集合查询答案
     *
     * @param answerIdList 答案Id集合
     * @return
     */
    List<Answer> findByAnswerIdList(List<Long> answerIdList);

    /**
     * 根据用户名称查询回答数量
     *
     * @param userName 用户名称
     * @return
     */
    Long findAnswerCountByUserName(String userName);

    /**
     * 根据答案Id查询答案在表的第几行
     *
     * @param answerId   答案Id
     * @param questionId 问题Id
     * @return
     */
    Long findRowByAnswerId(Long answerId, Long questionId);

    /**
     * 根据答案Id查询答案在表的第几行
     *
     * @param answerId   答案Id
     * @param questionId 问题Id
     * @param status     状态
     * @param sort       按发表时间排序 1.desc 2.asc
     * @return
     */
    Long findRowByAnswerId(Long answerId, Long questionId, Integer status, Integer sort);

    /**
     * 分页查询答案内容
     *
     * @param firstIndex
     * @param maxResult
     * @param userName   用户名称
     * @param isStaff    是否为员工
     * @return
     */
    List<String> findAnswerContentByPage(int firstIndex, int maxResult, String userName, boolean isStaff);

    Optional<List<Long>> findIdByPageAndLtLastUpdateTime(Date ltLastUpdateTime, int firstIndex, int maxResult);

    /**
     * 保存答案
     *
     * @param answer
     */
    void saveAnswer(Answer answer);

    /**
     * 修改答案
     *
     * @param answerId 答案Id
     * @param content  内容
     * @param status   状态
     * @param userName 用户名称
     * @return
     */
    Integer updateAnswer(Long answerId, String content, Integer status, String userName);

    /**
     * 修改答案
     *
     * @param answerId       答案Id
     * @param content        内容
     * @param status         状态
     * @param lastUpdateTime 最后修改时间
     * @param userName       用户名称
     * @return
     */
    Integer updateAnswer(Long answerId, String content, Integer status, Date lastUpdateTime, String userName);

    Integer updateAnswerByFilterKeyWord(Long answerId, Date eqLastUpdateTime, String content);

    /**
     * 采纳答案
     *
     * @param questionId                        问题Id
     * @param answerId                          答案Id
     * @param changeAdoption                    是否更改采纳答案
     * @param cancelAdoptionUserName            取消采纳用户名称
     * @param cancelAdoptionPointLogObject      取消采纳用户退还悬赏积分日志
     * @param cancelAdoptionUserNameShareAmount 取消采纳用户退还分成金额
     * @param cancelAdoptionPaymentLogObject    取消采纳用户退还悬赏金额日志
     * @param userName                          回答的用户名称
     * @param point                             扣减用户积分
     * @param pointLogObject                    积分日志
     * @param amount                            扣减用户预存款
     * @param paymentLogObject                  支付日志
     * @param questionRewardPlatformShare       平台分成
     * @return
     */
    int updateAdoptionAnswer(Long questionId, Long answerId, boolean changeAdoption, String cancelAdoptionUserName, Object cancelAdoptionPointLogObject, BigDecimal cancelAdoptionUserNameShareAmount, Object cancelAdoptionPaymentLogObject,
                             String userName, Long point, Object pointLogObject, BigDecimal amount, Object paymentLogObject, QuestionRewardPlatformShare questionRewardPlatformShare);

    /**
     * 取消采纳答案
     *
     * @param questionId                        问题Id
     * @param cancelAdoptionUserName            取消采纳用户名称
     * @param cancelAdoptionPointLogObject      取消采纳用户退还悬赏积分日志
     * @param cancelAdoptionUserNameShareAmount 取消采纳用户退还分成金额
     * @param cancelAdoptionPaymentLogObject    取消采纳用户退还悬赏金额日志
     * @param point                             扣减用户积分
     * @return
     */
    int updateCancelAdoptionAnswer(Long questionId, String cancelAdoptionUserName, Object cancelAdoptionPointLogObject, BigDecimal cancelAdoptionUserNameShareAmount, Object cancelAdoptionPaymentLogObject, Long point);

    /**
     * 修改答案状态
     *
     * @param answerId 答案Id
     * @param status   状态
     * @return
     */
    int updateAnswerStatus(Long answerId, Integer status);

    /**
     * 删除答案
     *
     * @param questionId                        问题Id
     * @param answerId                          答案Id
     * @param cancelAdoptionUserName            取消采纳用户名称
     * @param cancelAdoptionPointLogObject      取消采纳用户退还悬赏积分日志
     * @param cancelAdoptionUserNameShareAmount 取消采纳用户退还分成金额
     * @param cancelAdoptionPaymentLogObject    取消采纳用户退还悬赏金额日志
     * @param point                             扣减用户积分
     * @return
     */
    Integer deleteAnswer(Long questionId, Long answerId, String cancelAdoptionUserName, Object cancelAdoptionPointLogObject, BigDecimal cancelAdoptionUserNameShareAmount, Object cancelAdoptionPaymentLogObject, Long point);

    /**
     * 根据用户名称集合删除答案
     *
     * @param userNameList 用户名称集合
     * @return
     */
    Integer deleteAnswer(List<String> userNameList);

    /**
     * 标记删除答案
     *
     * @param answerIdId 答案Id
     * @param constant   常数 例如 "110.待审核用户删除" 则加上100
     * @return
     */
    Integer markDeleteAnswer(Long questionId, Long answerId, Integer constant);

    /**
     * 查询待审核答案数量
     *
     * @return
     */
    Long auditAnswerCount();

    /**
     * 添加回复
     *
     * @param answerReply
     */
    void saveReply(AnswerReply answerReply);

    /**
     * 根据答案Id查询回复
     *
     * @param answerId 答案Id
     * @return
     */
    List<AnswerReply> findReplyByAnswerId(Long answerId);

    /**
     * 根据答案Id集合查询回复
     *
     * @param answerIdList 答案Id集合
     * @param status       状态
     * @return
     */
    List<AnswerReply> findReplyByAnswerId(List<Long> answerIdList, Integer status);

    /**
     * 根据答案Id集合查询回复
     *
     * @param answerIdList 答案Id集合
     * @return
     */
    List<AnswerReply> findReplyByAnswerId(List<Long> answerIdList);

    /**
     * 根据答案回复Id查询答案回复
     *
     * @param answerReplyId 答案回复Id
     * @return
     */
    AnswerReply findReplyByReplyId(Long answerReplyId);

    /**
     * 根据答案回复Id集合查询答案回复
     *
     * @param answerReplyIdList 答案Id集合
     * @return
     */
    List<AnswerReply> findByAnswerReplyIdList(List<Long> answerReplyIdList);

    Optional<List<Long>> findReplyIdByByPageAndLtLastUpdateTime(Date ltLastUpdateTime, int firstIndex, int maxResult);

    /**
     * 修改回复
     *
     * @param answerReplyId 回复Id
     * @param content       回复内容
     * @param userName      用户名称
     * @param status        状态
     * @return
     */
    Integer updateReply(Long answerReplyId, String content, String userName, Integer status);

    /**
     * 修改回复
     *
     * @param replyId        回复Id
     * @param content        回复内容
     * @param userName       用户名称
     * @param status         状态
     * @param lastUpdateTime 最后修改时间
     * @return
     */
    Integer updateReply(Long replyId, String content, String userName, Integer status, Date lastUpdateTime);

    Integer updateReplyByFilterKeyWord(Long replyId, Date eqLastUpdateTime, String content);

    /**
     * 修改回复状态
     *
     * @param answerReplyId 回复Id
     * @param status        状态
     * @return
     */
    int updateReplyStatus(Long answerReplyId, Integer status);

    /**
     * 标记删除回复
     *
     * @param replyId  回复Id
     * @param constant 常数 例如 "110.待审核用户删除" 则加上100
     * @return
     */
    Integer markDeleteReply(Long replyId, Integer constant);

    /**
     * 删除回复
     *
     * @param answerReplyId 回复Id
     * @return
     */
    Integer deleteReply(Long answerReplyId);

    /**
     * 根据用户名称集合删除答案回复
     *
     * @param userNameList 用户名称集合
     * @return
     */
    Integer deleteAnswerReply(List<String> userNameList);

    /**
     * 查询待审核回复数量
     *
     * @return
     */
    Long auditReplyCount();
}
