package cms.constant.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/18 9:52
 */
public enum LikeType {
    TOPIC(1, "话题"),
    COMMENT(20, "话题评论"),
    REPLY(30, "话题评论回复"),
    ;

    private int value;
    private String label;
    private static final Map<Integer, LikeType> map;

    static {
        map = Arrays.stream(LikeType.values()).collect(Collectors.toMap(LikeType::getValue, o -> o, (a, b) -> a));
    }

    LikeType(int value, String label) {
        this.value = value;
        this.label = label;
    }

    public int getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static Optional<LikeType> getByValue(Integer value) {
        return Optional.ofNullable(value).map(map::get);
    }
}
