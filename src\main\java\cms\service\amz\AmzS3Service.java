package cms.service.amz;

import cms.handle.MsgException;
import cms.service.aliyun.OssService;
import cms.service.fileCloud.OssInterface;
import cms.service.messageSource.ErrorMessageService;
import cn.hutool.core.collection.CollectionUtil;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Optional;

import static cms.constant.ErrorCode.C_2_0001_0500;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/9/21 17:25
 */
@Slf4j
@Service
@ConditionalOnProperty(value = "fileCloud.type", havingValue = "s3")
public class AmzS3Service implements OssInterface {

    @Value("${fileCloud.amz.s3.endpoint:''}")
    private String endpoint;
    @Value("${fileCloud.amz.s3.accessKeyId:''}")
    private String accessKeyId;
    @Value("${fileCloud.amz.s3.accessKeySecret:''}")
    private String accessKeySecret;
    @Value("${fileCloud.amz.s3.bucketName:''}")
    private String bucketName;
    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private ErrorMessageService errorMessageService;

    public AmazonS3 getClient() {
        AwsClientBuilder.EndpointConfiguration endpointConfig =
                new AwsClientBuilder.EndpointConfiguration(endpoint, Region.getRegion(Regions.CN_NORTH_1).getName());

        AWSCredentials awsCredentials = new BasicAWSCredentials(accessKeyId, accessKeySecret);
        AWSCredentialsProvider awsCredentialsProvider = new AWSStaticCredentialsProvider(awsCredentials);
        ClientConfiguration clientConfig = new ClientConfiguration();
        clientConfig.setProtocol(Protocol.HTTPS);

        AmazonS3 S3client = AmazonS3Client.builder()
                .withEndpointConfiguration(endpointConfig)
                .withClientConfiguration(clientConfig)
                .withCredentials(awsCredentialsProvider)
                .disableChunkedEncoding()
                .withPathStyleAccessEnabled(true)
                .withForceGlobalBucketAccessEnabled(true)
                .build();
        log.info("create s3Client success");
        return S3client;
    }

    @Override
    public String upload(InputStream input, String objectName) {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        String path = this.getTitlePath() + objectName.replaceAll("\\\\", OssService.separator);
        AmazonS3 amazonS3 = getClient();
        try {
            amazonS3.putObject(new PutObjectRequest(bucketName, path, input, objectMetadata)
                    .withCannedAcl(CannedAccessControlList.PublicRead));   //配置文件访问权限
            return String.format("%s%s", getUploadStartPath(), path);
        } finally {
            Optional.ofNullable(amazonS3).ifPresent(AmazonS3::shutdown);
        }
    }

    @Override
    public List<String> listFile() {
        List<String> result = Lists.newArrayList();
        AmazonS3 amazonS3 = getClient();
        try {
            ObjectListing objectListing = amazonS3.listObjects(bucketName);

            objectListing.getObjectSummaries().stream().map(S3ObjectSummary::getKey).forEach(result::add);
            objectListing.getCommonPrefixes().stream().forEach(result::add);
        } finally {
            Optional.ofNullable(amazonS3).ifPresent(AmazonS3::shutdown);
        }
        return result;
    }

    @Override
    public void del(List<String> path) {
        if (CollectionUtil.isEmpty(path)) {
            return;
        }
        AmazonS3 amazonS3 = getClient();
        try {
            path.forEach(o -> amazonS3.deleteObject(bucketName, o));
        } finally {
            Optional.ofNullable(amazonS3).ifPresent(AmazonS3::shutdown);
        }
    }

    @Override
    public InputStream getFile(String path) {
        AmazonS3 amazonS3 = getClient();
        try {
            S3Object s3Object = amazonS3.getObject(bucketName, path);
            return new ByteArrayInputStream(IOUtils.toByteArray(s3Object.getObjectContent()));
        } catch (IOException e) {
            throw new MsgException(errorMessageService.getMessage(C_2_0001_0500));
        } finally {
            Optional.ofNullable(amazonS3).ifPresent(AmazonS3::shutdown);
        }
    }

    @Override
    public String getTitlePath() {
        return Optional.ofNullable(active).filter("pro"::equals).map(o -> "").orElseGet(() -> "bbs" + OssService.separator);
    }

    @Override
    public String getUploadStartPath() {
        return String.format("https://%s.%s/", bucketName, this.endpoint);
    }
}
