package cms.web.action.fileSystem;


import cms.bean.setting.SystemSetting;
import cms.bean.thumbnail.Thumbnail;
import cms.bean.topic.ImageInfo;
import cms.bean.user.User;
import cms.constant.ErrorCode;
import cms.handle.CustomException;
import cms.service.aliyun.OssFileChangeService;
import cms.service.aliyun.OssService;
import cms.service.setting.SettingService;
import cms.utils.FileUtil;
import cms.utils.PathUtil;
import cms.utils.SecureLink;
import cms.web.action.fileSystem.localImpl.LocalFileManage;
import cms.web.action.thumbnail.ThumbnailManage;
import cms.web.taglib.Configuration;
import cn.hutool.core.collection.ListUtil;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static cms.constant.Constant.DATE_YMD;
import static cms.constant.Constant.business_avatar;
import static cms.constant.ErrorCode.*;

/**
 * 文件管理
 *
 * <AUTHOR>
 */
@Component("fileManage")
public class FileManage {
    //允许上传图片大小 单位KB
    private final static long avatar_imageSize = 3 * 1024L;
    //最大宽度
    private final static int avatar_maxWidth = 200;
    //最大高度
    private final static int avatar_maxHeight = 200;
    private final static String suffix_png = "png";
    private final static String fileType_blob = "blob";
    //允许上传图片格式
    private final static List<String> formatList = Lists.newArrayList();

    static {
        formatList.add("gif");
        formatList.add("jpg");
        formatList.add("jpeg");
        formatList.add("bmp");
        formatList.add("png");
    }

    @Resource
    SettingService settingService;
    @Resource
    LocalFileManage localFileManage;
    @Resource
    ThumbnailManage thumbnailManage;
    @Autowired
    protected OssFileChangeService ossFileChangeService;


    /**
     * 获得正在使用的文件系统
     *
     * @return 0.本地系统 10.SeaweedFS 20.MinIO 30.阿里云OSS
     */
    public int getFileSystem() {

        return 0;//本地文件系统

    }

    /**
     * 未实现
     * 如有多个地址，则随机返回一个地址，分布式文件存储系统服务器会自动302跳转(302暂时性转移)到正确的地址
     * 本地文件存储系统返回‘空值’
     *
     * @return
     */
    public String fileServerAddress() {
        return this.fileServerAddress(null);
    }

    /**
     * 未实现
     * 获取文件服务器地址 如http://s3-1.diyhi.com/
     * 如有多个地址，则随机返回一个地址，分布式文件系统服务器会自动302跳转(302暂时性转移)到正确的地址
     * 本地文件存储系统返回‘访问地址’
     *
     * @return
     */
    public String fileServerAddress(HttpServletRequest request) {
        if (request != null) {
            return Configuration.getUrl(request);//本地文件存储系统
        }
        return "";//本地文件系统
    }

    public String fileOssAddress() {
        return ossFileChangeService.getOssUrl();
    }

    /**
     * 未实现
     * 获取文件服务器所有地址 如http://s3-1.diyhi.com/
     * 如有多个地址，分布式文件存储系统服务器会自动302跳转(302暂时性转移)到正确的地址
     * 本地文件存储系统返回‘空值’
     *
     * @return
     */
    public List<String> fileServerAllAddress() {
        return this.fileServerAllAddress(null);
    }

    /**
     * 未实现
     * 获取文件服务器所有地址 如http://s3-1.diyhi.com/
     * 如有多个地址，分布式文件存储系统服务器会自动302跳转(302暂时性转移)到正确的地址
     * 本地文件存储系统返回‘访问地址’
     *
     * @return
     */
    public List<String> fileServerAllAddress(HttpServletRequest request) {
        if (request != null) {
            List<String> newEndpoint = new ArrayList<String>();
            newEndpoint.add(Configuration.getUrl(request)); //本地文件存储系统
            return newEndpoint;
        }
        return null;
    }


    /**
     * 处理富文本文件路径
     *
     * @param html 富文本内容
     * @param item 项目
     * @return
     */
    public String processRichTextFilePath(String html, String item) {
        /*HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

        if (html != null && !"".equals(html.trim())) {
            return textFilterManage.processFilePath(html, item, this.fileServerAddress(request));
        }*/

        return html;
    }


    /**
     * 生成文件夹
     *
     * @param path 路径
     */
    public Boolean createFolder(String path) {
        //本地文件系统
        return FileUtil.createFolder(path);
    }

    /**
     * 未实现
     * 创建预签名
     *
     * @param pathName 文件路径名称 例如 file/links/111.jpg
     * @param fileSize 允许上传文件大小 单位/K
     * @return
     */
    public String createPresigned(String pathName, Long fileSize) {

        return "";
    }

    /**
     * 写文件
     *
     * @param path        路径
     * @param newFileName 新文件名称
     * @param content     内容
     */
    public void writeFile(String path, String newFileName, byte[] content) {
        //本地文件系统
        localFileManage.writeFile(path, newFileName, content);

    }

    /**
     * 复制文件
     *
     * @param resFilePath 源文件路径
     * @param distFolder  目标文件夹
     * @IOException 当操作发生异常时抛出
     */
    public void copyFile(String resFilePath, String distFolder) throws IOException {
        //本地文件系统
        localFileManage.copyFile(resFilePath, distFolder);
    }

    /**
     * 复制目录
     *
     * @param resDirectory 源目录路径
     * @param distFolder   目标文件夹
     * @IOException 当操作发生异常时抛出
     */
    public void copyDirectory(String resDirectory, String distFolder) throws IOException {
        //本地文件存储系统
        localFileManage.copyDirectory(resDirectory, distFolder);

    }

    /**
     * 删除失败状态文件
     *
     * @param path 路径
     */
    public void failedStateFile(String path) {
        //本地文件系统
        localFileManage.failedStateFile(path);

    }


    /**
     * 删除文件
     *
     * @param path 路径
     * @return
     */
    public Boolean deleteFile(String path) {
        //本地文件系统
        return localFileManage.deleteFile(path);

    }


    /**
     * 删除目录
     *
     * @param path 路径 (格式"file"+File.separator+"目录"+File.separator)
     */
    public Boolean removeDirectory(String path) {
        //本地文件系统
        return localFileManage.removeDirectory(path);


    }


    /**
     * 添加锁
     *
     * @param path         路径(格式"file"+File.separator+"目录"+File.separator+"lock"+File.separator)
     * @param lockFileName 锁文件名称
     */
    public void addLock(String path, String lockFileName) throws IOException {
        //本地文件系统
        localFileManage.addLock(path, lockFileName);
    }


    /**
     * 删除锁
     *
     * @param path     路径(格式"file"+File.separator+"目录"+File.separator+"lock"+File.separator)
     * @param fileName 文件名称
     */
    public void deleteLock(String path, String fileName) {
        //本地文件系统
        localFileManage.deleteLock(path, fileName);
    }


    /**
     * 图片格式转换
     *
     * @param resFilePath 原文件路径
     * @param newFilePath 生成文件路径
     * @param suffix      新文件后缀    jpg  bmp
     * @throws IOException
     */
    public void converterImage(String resFilePath, String newFilePath, String suffix)
            throws IOException {
        //本地文件系统
        localFileManage.converterImage(resFilePath, newFilePath, suffix);
    }


    /**
     * 异步增加缩略图
     *
     * @param thumbnailList 缩略图对象集合
     * @param imageInfoList 图片信息集合
     */
    public void addThumbnail(List<Thumbnail> thumbnailList, List<ImageInfo> imageInfoList) {
        thumbnailManage.addThumbnail(thumbnailList, imageInfoList);
    }

    /**
     * 异步删除缩略图
     *
     * @param thumbnailList 缩略图对象集合
     * @param imageInfoList 图片信息集合
     */
    public void deleteThumbnail(List<Thumbnail> thumbnailList, List<ImageInfo> imageInfoList) {
        //本地文件系统
        thumbnailManage.deleteThumbnail(thumbnailList, imageInfoList);

    }

    /**
     * 生成缩略图
     *
     * @param sourceInputStream 源图片路径
     * @param outputPath        输出图片路径
     * @param extension         后缀名
     * @param scaleWidth        缩放宽
     * @param scaleHeight       缩放高
     **/
    public void createImage(InputStream sourceInputStream, String outputPath, String extension, int scaleWidth, int scaleHeight) {
        //本地文件系统
        thumbnailManage.createImage(sourceInputStream, PathUtil.defaultExternalDirectory() + File.separator + outputPath, extension, scaleWidth, scaleHeight);
    }

    public InputStream createImage(InputStream sourceInputStream, String extension, int scaleWidth, int scaleHeight) {
        byte[] bytes = thumbnailManage.createImage(sourceInputStream, extension, scaleWidth, scaleHeight);
        return new ByteArrayInputStream(bytes);
    }

    /**
     * 生成缩略图
     *
     * @param sourceInputStream 源图片路径
     * @param outputPath        输出图片路径
     * @param extension         后缀名
     * @param x                 坐标X轴
     * @param y                 坐标Y轴
     * @param width             剪裁区域宽
     * @param height            剪裁区域高
     * @param scaleWidth        缩放宽
     * @param scaleHeight       缩放高
     **/
    public void createImage(InputStream sourceInputStream, String outputPath, String extension, int x, int y, int width, int height, int scaleWidth, int scaleHeight) {
        //本地文件系统
        thumbnailManage.createImage(sourceInputStream, PathUtil.defaultExternalDirectory() + File.separator + outputPath, extension, x, y, width, height, scaleWidth, scaleHeight);
    }

    public InputStream createImage(InputStream sourceInputStream, String extension, int x, int y, int width, int height, int scaleWidth, int scaleHeight) {
        //本地文件系统
        byte[] bytes = thumbnailManage.createImage(sourceInputStream, extension, x, y, width, height, scaleWidth, scaleHeight);
        return new ByteArrayInputStream(bytes);
    }


    /**----------------------------------- 定时删除文件 ----------------------------------**/


    /**
     * 删除无效文件
     */
    public void deleteInvalidFile() {
        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting != null) {
            if (systemSetting.getTemporaryFileValidPeriod() != null && systemSetting.getTemporaryFileValidPeriod() > 0) {
                //最大删除时间
                Long maxDeleteTime = new Date().getTime() - systemSetting.getTemporaryFileValidPeriod() * 60 * 1000;
                localFileManage.lockRemoveFile("template", maxDeleteTime);//删除模板 文件

                localFileManage.lockRemoveFile("topic", maxDeleteTime);//删除话题文件
                localFileManage.lockRemoveFile("comment", maxDeleteTime);//评论文件
                localFileManage.lockRemoveFile("question", maxDeleteTime);//删除问题文件
                localFileManage.lockRemoveFile("answer", maxDeleteTime);//答案文件

                localFileManage.lockRemoveFile("help", maxDeleteTime);//删除帮助文件
                localFileManage.lockRemoveFile("helpType", maxDeleteTime);//删除帮助分类文件
                localFileManage.lockRemoveFile("links", maxDeleteTime);//删除友情链接文件
                localFileManage.lockRemoveFile("membershipCard", maxDeleteTime);//会员卡


            }
        }

    }


    /**
     * 生成签名链接
     *
     * @param link     链接
     * @param fileName 文件名称
     * @param secret   密钥
     * @param expires  有效时间 单位/秒
     * @return
     */
    public String createSignLink(String link, String fileName, String secret, Long expires) {

        String newSecureLink = SecureLink.createSecureLink(link, fileName, secret, expires);
        if (Configuration.getPath() != null && !"".equals(Configuration.getPath().trim())) {
            //删除虚拟路径
            newSecureLink = StringUtils.removeStartIgnoreCase(newSecureLink, Configuration.getPath() + "/");//移除开始部分的相同的字符,不区分大小写
        }
        return newSecureLink;

    }

    public String updateAvatar(MultipartFile imgFile, User user, Integer width, Integer height, Integer x, Integer y) throws Exception {
        DateTime dateTime = Optional.ofNullable(user.getRegistrationDate()).map(DateTime::new).orElseThrow(() -> new CustomException(ErrorCode.C_2_0001_0023, ""));
        String date = dateTime.toString(DATE_YMD);

        //当前文件名称
        String fileName = imgFile.getOriginalFilename();
        //文件大小
        Optional.ofNullable(imgFile.getSize()).filter(o -> o / 1024 <= avatar_imageSize).orElseThrow(() -> new CustomException(C_2_2001_0075, "imgFile"));
        Optional<String> pathDir = Optional.of(new StringBuffer(date).toString());
        Optional<String> pathDir_100 = pathDir.map(StringBuffer::new).map(o -> o.append(OssService.separator).append("100x100").toString());
        // 文件信息
        BufferedImage bufferImage = ImageIO.read(imgFile.getInputStream());
        int srcWidth = bufferImage.getWidth();
        int srcHeight = bufferImage.getHeight();
        String newFileName;
        // Blob类型
        if (fileType_blob.equalsIgnoreCase(imgFile.getOriginalFilename())) {
            newFileName = ossFileChangeService.getNewFileName(suffix_png, String.format("b%s", user.getId()));
            //获取图片的宽和高
            Optional.ofNullable(srcWidth).filter(o -> o.intValue() <= avatar_maxWidth).orElseThrow(() -> new CustomException(C_2_2001_0072, "imgFile"));
            Optional.ofNullable(srcHeight).filter(o -> o.intValue() <= avatar_maxHeight).orElseThrow(() -> new CustomException(C_2_2001_0073, "imgFile"));

            //生成100*100缩略图
            lt200gt100Avatar(imgFile, newFileName, pathDir, pathDir_100);
        } else {
            String suffix = FileUtil.getExtension(fileName).toLowerCase();
            newFileName = ossFileChangeService.getNewFileName(suffix, String.format("b%s", user.getId()));
            if (srcWidth <= 200 && srcHeight <= 200) {
                if (srcWidth <= 100 && srcHeight <= 100) {
                    ossFileChangeService.uploadFile(null, imgFile, Optional.of(newFileName), Optional.of(formatList), Optional.empty(), business_avatar, pathDir);
                    ossFileChangeService.uploadFile(null, imgFile, Optional.of(newFileName), Optional.of(formatList), Optional.empty(), business_avatar, pathDir_100);
                } else {
                    //生成100*100缩略图
                    lt200gt100Avatar(imgFile, newFileName, pathDir, pathDir_100);
                }
            } else {
                //生成200*200缩略图
                InputStream is200 = this.createImage(imgFile.getInputStream(), suffix, x, y, width, height, 200, 200);
                //生成100*100缩略图
                InputStream is100 = this.createImage(imgFile.getInputStream(), suffix, x, y, width, height, 100, 100);

                ossFileChangeService.uploadFile(is200, Optional.of(newFileName), Optional.empty(), Optional.empty(), business_avatar, pathDir);
                ossFileChangeService.uploadFile(is100, Optional.of(newFileName), Optional.empty(), Optional.empty(), business_avatar, pathDir_100);
            }
        }

        //删除旧头像
        Optional.ofNullable(user.getAvatarName()).map(String::trim).filter(Strings::isNotEmpty).ifPresent(o -> {
            String oldPathFile = new StringBuffer(ossFileChangeService.getPathDir(business_avatar, pathDir)).append(o).toString();
            String oldPathFile_100 = new StringBuffer(ossFileChangeService.getPathDir(business_avatar, pathDir_100)).append(o).toString();
            ossFileChangeService.delFileNoTile(ListUtil.toList(oldPathFile, oldPathFile_100));
        });

        return newFileName;
    }

    private void lt200gt100Avatar(MultipartFile imgFile, String newFileName, Optional<String> pathDir, Optional<String> pathDir_100) throws Exception {
        //生成100*100缩略图
        InputStream is = this.createImage(imgFile.getInputStream(), suffix_png, 100, 100);
        ossFileChangeService.uploadFile(null, imgFile, Optional.of(newFileName), Optional.of(formatList), Optional.empty(), business_avatar, pathDir);
        ossFileChangeService.uploadFile(is, Optional.of(newFileName), Optional.empty(), Optional.empty(), business_avatar, pathDir_100);
    }
}
