package cms.service.setting.impl;

import cms.bean.setting.EmailRemindSetting;
import cms.service.besa.DaoSupport;
import cms.service.message.impl.EmailRemindService;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static cms.constant.Constant.EMAIL_REMIND_TEMPLATE_KEY_MESSAGEPAGE;
import static cms.constant.Constant.EMAIL_REMIND_TEMPLATE_KEY_RECEIVEDNICKNAME;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/28 13:55
 */
@Service
@Slf4j
public class EmailRemindSettingService extends DaoSupport<EmailRemindSetting> {

    public static final Map<Integer, EmailRemindSetting> defaultMap = Maps.newHashMap();

    static {
        StringBuffer content = new StringBuffer("");
        content.append("<table width='100%'>");
        content.append("<tr>");
        content.append("<td align='center'>").append(String.format("Dear {%s},", EMAIL_REMIND_TEMPLATE_KEY_RECEIVEDNICKNAME)).append("</td>");
        content.append("</tr>");
        content.append("<tr>");
        content.append("<td align='center'>").append("<p style='word-break: normal;overflow-wrap: break-word;'>").append("You received a new message in the TOPDON Community.").append("</p>").append("</td>");
        content.append("</tr>");
        content.append("<tr>");
        content.append("<td align='center'>").append(String.format("<a href='{%s}'>Click here</a> to view the details.", EMAIL_REMIND_TEMPLATE_KEY_MESSAGEPAGE)).append("</td>");
        content.append("</tr>");
        content.append("<tr>");
        content.append("<td align='center'>").append("Best,").append("</td>");
        content.append("</tr>");
        content.append("<tr>");
        content.append("<td align='center'>").append("The TOPDON SERVICE TEAM").append("</td>");
        content.append("</tr>");
        content.append("</table>");
        EmailRemindSetting emailRemindSetting = new EmailRemindSetting();
        emailRemindSetting.setRemindType(EmailRemindService.totalEmailRemindNumKey);
        emailRemindSetting.setSendNum(1);
        emailRemindSetting.setTitleTemplate("You've received a new message in the TOPDON Community.");
        emailRemindSetting.setContentTemplate(content.toString());
        defaultMap.put(EmailRemindService.totalEmailRemindNumKey, emailRemindSetting);
    }

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    public List<EmailRemindSetting> getEmailRemindSetting() {
        Query query = em.createQuery("select o from EmailRemindSetting o");
        return query.getResultList();
    }

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    @Cacheable(value = "emailRemindSetting_cache", key = "'findEmailRemindSetting_default'")
    public Map<Integer, EmailRemindSetting> findEmailRemindSetting_cache() {
        return Optional.ofNullable(this.getEmailRemindSetting()).filter(CollUtil::isNotEmpty).map(l -> l.stream().collect(Collectors.toMap(EmailRemindSetting::getRemindType, o -> o, (a, b) -> a))).orElseGet(() -> defaultMap);
    }

    @CacheEvict(value = "emailRemindSetting_cache", allEntries = true)
    public void updateSystemSetting() {
    }

    public Map<Integer, EmailRemindSetting> test() {
        this.updateSystemSetting();
        return findEmailRemindSetting_cache();
    }
}
