errors.required={0}
errors.minlength={0} 不能小于{1}个字符
errors.maxlength={0} 不能大于{1}个字符
errors.invalid={0} 是无效的

errors.byte={0} \u5FC5\u987B\u662Fbyte
errors.short={0} \u5FC5\u987B\u662Fshort
errors.integer={0} \u5FC5\u987B\u662Finteger
errors.long={0} \u5FC5\u987B\u662Flong
errors.float={0} \u5FC5\u987B\u662Ffloat
errors.double={0} \u5FC5\u987B\u662Fdouble

errors.date={0} is not a date. 
errors.range={0} 不在{1}到{2}范围内
errors.creditcard={0} is an invalid credit card number. 
errors.email={0} \u662F\u65E0\u6548\u7684\u7535\u5B50\u90AE\u4EF6\u5730\u5740


## Spring校验信息
## 全局的配置信息 ...输入的数据格式不正确 
typeMismatch=\u8F93\u5165\u7684\u6570\u636E\u683C\u5F0F\u4E0D\u6B63\u786E
## 针对BigDecimal类型的错误信息 ...请输入货币类型
typeMismatch.java.math.BigDecimal=\u8BF7\u8F93\u5165\u8D27\u5E01\u7C7B\u578B
## 针对Date类型的错误信息...请输入正确的日期格式
typeMismatch.java.util.Date=\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u65E5\u671F\u683C\u5F0F
##校验Integer类型错误...请输入数字类型
typeMismatch.java.lang.Integer=\u8BF7\u8F93\u5165\u6570\u5B57\u7C7B\u578B

