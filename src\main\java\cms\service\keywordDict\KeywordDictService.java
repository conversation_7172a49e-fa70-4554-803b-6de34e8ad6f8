package cms.service.keywordDict;

import cms.bean.keywordDict.KeywordDict;
import cms.service.besa.DaoSupport;
import cn.hutool.core.collection.CollectionUtil;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/9 10:50
 */
@Service
public class KeywordDictService extends DaoSupport<KeywordDict> {

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    @Cacheable(value = "findAllKeyword_cache", key = "'findAllKeyword_default'")
    public List<KeywordDict> findAllKeyword_cache() {
        return this.findAllTag();
    }

    public Optional<KeywordDict> findById(Long id) {
        Query query = em.createQuery("select o from KeywordDict o where o.deleted = false and o.id = ?1");
        query.setParameter(1, id);
        return Optional.ofNullable(query.getResultList()).filter(CollectionUtil::isNotEmpty).map(o -> (KeywordDict) o.get(0));
    }

    private List<KeywordDict> findAllTag() {
        Query query = em.createQuery("select o from KeywordDict o where o.deleted = false");
        query.setMaxResults(1000);
        return query.getResultList();
    }

    @CacheEvict(value = "findAllKeyword_cache", allEntries = true)
    public void add(List<KeywordDict> keywordDicts) {
        this.save(keywordDicts);
    }

    public Optional<List<KeywordDict>> findByKeyWords(List<String> keyWords) {
        Query query = em.createQuery("select o from KeywordDict o where o.deleted = false and o.keyWord in(:keyWord)");
        //设置参数
        query.setParameter("keyWord", keyWords);
        return Optional.ofNullable(query.getResultList()).filter(CollectionUtil::isNotEmpty).map(o -> o);
    }

    @CacheEvict(value = "findAllKeyword_cache", allEntries = true)
    public void delById(long id) {
        Query query = em.createQuery("update KeywordDict o set o.deleted=true  where o.id=?1")
                .setParameter(1, id);
        int i = query.executeUpdate();
    }

    @CacheEvict(value = "findAllKeyword_cache", allEntries = true)
    public void delAll() {
        Query query = em.createQuery("update KeywordDict o set o.deleted=true");
        query.executeUpdate();
    }
}
