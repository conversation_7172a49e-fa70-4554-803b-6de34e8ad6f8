package cms.service.like.impl;

import cms.bean.like.CommentLike;
import cms.service.besa.DaoSupport;
import cms.web.action.like.CommentLikeConfig;
import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.beans.BeanCopier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.Query;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/18 10:53
 */
@Service
@Slf4j
public class CommentLikeService extends DaoSupport<CommentLike> {

    @Autowired
    private CommentLikeConfig commentLikeConfig;

    public Optional<CommentLike> getById(String id) {
        //表编号
        int tableNumber = commentLikeConfig.commentLikeIdRemainder(id);
        if (tableNumber == 0) {//默认对象
            Query query = em.createQuery("select o from CommentLike o where o.id=?1")
                    .setParameter(1, id);
            return Optional.ofNullable(query.getResultList()).filter(CollUtil::isNotEmpty).map(l -> (CommentLike) l.get(0));
        } else {//带下划线对象
            Query query = em.createQuery("select o from CommentLike_" + tableNumber + " o where o.id=?1")
                    .setParameter(1, id);

            try {
                //带下划线对象
                Class<?> c = Class.forName("cms.bean.like.CommentLike_" + tableNumber);
                Object object = c.newInstance();
                BeanCopier copier = BeanCopier.create(object.getClass(), CommentLike.class, false);

                return Optional.ofNullable(query.getResultList()).filter(CollUtil::isNotEmpty).map(l -> {
                    Object obj = l.get(0);
                    CommentLike commentLike = new CommentLike();
                    copier.copy(obj, commentLike, null);
                    return commentLike;
                });
            } catch (Exception e) {
                log.error("根据Id查询话题评论点赞", e);
            }
        }
        return Optional.empty();
    }

    public Object createCommentLikeObject(CommentLike commentLike) {
        //表编号
        int tableNumber = commentLikeConfig.commentLikeIdRemainder(commentLike.getId());
        if (tableNumber == 0) {//默认对象
            return commentLike;
        } else {//带下划线对象
            Class<?> c;
            try {
                c = Class.forName("cms.bean.like.CommentLike_" + tableNumber);
                Object object = c.newInstance();

                BeanCopier copier = BeanCopier.create(CommentLike.class, object.getClass(), false);

                copier.copy(commentLike, object, null);
                return object;
            } catch (ClassNotFoundException e) {
                log.error("生成话题评论点赞对象", e);
            } catch (InstantiationException e) {
                log.error("生成话题评论点赞对象", e);
            } catch (IllegalAccessException e) {
                log.error("生成话题评论点赞对象", e);
            }
        }
        return null;
    }
}
