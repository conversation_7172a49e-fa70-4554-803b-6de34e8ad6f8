package cms.bean.feedback;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 在线留言
 */
@Entity
@Table(indexes = {@Index(name = "feedback_1_idx", columnList = "createDate")})
public class Feedback implements Serializable {
    private static final long serialVersionUID = -4891301979499383115L;

    /**
     * ID
     **/
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 名称
     **/
    @Column(length = 190)
    private String name;
    /**
     * 联系方式
     **/
    @Column(length = 190)
    private String contact;
    /**
     * 内容
     **/
    @Lob
    private String content;
    /**
     * IP
     **/
    @Column(length = 45)
    private String ip;
    /**
     * IP归属地
     **/
    @Transient
    private String ipAddress;
    /**
     * 创建时间
     **/
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate = new Date();


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }


}
