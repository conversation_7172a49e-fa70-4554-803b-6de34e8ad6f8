package cms.constraints;

import cms.constraints.validator.ContentSpStringConstraintValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/4/23 14:52
 */
@Documented
@Constraint(validatedBy = {
        ContentSpStringConstraintValidator.class
})
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.TYPE_USE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ContentSpString {

    String message() default "文本中不能有空格";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
