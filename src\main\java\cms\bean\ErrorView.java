package cms.bean;

import cms.constant.ErrorCode;
import cms.service.messageSource.ErrorMessageService;
import cms.utils.SpringUtils;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static cms.constant.ErrorCode.*;

/**
 * 前台错误提示
 * 这个类 以后不用了，请用ErrroCode
 */
@Deprecated
public enum ErrorView {

    _1("路径来路错误", C_1_0001_0005),
    _11("令牌为空", C_1_0001_0003),
    _12("令牌过期", C_1_0001_0002),
    _13("令牌错误", C_1_0001_0001),
    _14("需要输入验证码", C_1_0004_0007),//验证码参数错误
    _15("验证码错误", C_1_0004_0009),
    _16("请输入验证码", C_1_0004_0008),
    _17("验证码过期", C_1_0004_0010),
    _18("密钥错误", C_1_0001_0006),
    _20("字符超长", C_1_0001_0007),
    _21("只读模式不允许提交数据", C_2_0001_0001),
    _22("系统繁忙", C_1_0001_0008),
    _23("模块错误", C_1_0001_0009),
    _24("令牌检测未开启", C_1_0001_0004),
    _25("刷新令牌不能为空", C_1_0001_0010),

    /**
     * -------------------------------- 话题 -----------------------------------
     **/
    _101("内容不能为空", C_2_0007_0002),
    _103("话题Id不能为空", C_2_0007_0003),
    _104("引用评论不能为空", C_2_0007_0004),
    _105("评论不存在", C_2_0007_0005),
    _106("禁止评论", C_2_0007_0006),
    _107("话题不存在", C_2_0007_0001),
    _108("实名用户才允许评论", C_2_0007_0007),
    _109("实名用户才允许提交话题", C_2_0007_0008),
    _110("不允许提交话题", C_2_0007_0009),
    _111("已发布话题才允许评论", C_2_0007_0010),
    _112("话题不存在", ErrorCode.C_2_0007_0001),
    _113("只允许修改自己发布的话题", C_2_0007_0011),
    _114("话题已删除", C_2_0007_0012),
    _115("修改话题失败", C_2_0007_0013),
    _116("评论不存在", C_2_0007_0005),
    _117("只允许修改自己发布的评论", C_2_0007_0015),
    _118("评论已删除", C_2_0007_0016),
    _119("修改评论失败", C_2_0007_0017),
    _120("评论Id不能为空", C_2_0007_0018),
    _121("修改回复失败", C_2_0007_0019),
    _122("只允许修改自己发布的回复", C_2_0007_0020),
    _123("禁止回复", C_2_0007_0021),
    _124("实名用户才允许提交回复", C_2_0007_0022),
    _125("回复不存在", C_2_0007_0023),
    _126("回复Id不能为空", C_2_0007_0024),
    _127("请填写总金额", C_2_0007_0025),
    _128("不能超过12位数字", C_2_0007_0026),
    _129("不能超过3位数字", C_2_0007_0027),
    _130("请填写正整数", C_2_0007_0028),
    _131("不允许发红包", C_2_0007_0029),
    _132("不能小于发红包总金额下限", C_2_0007_0030),
    _133("不能大于发红包总金额上限", C_2_0007_0031),
    _134("总金额不能为空", C_2_0007_0032),
    _135("拆分后最低单个红包金额不足0.01元", C_2_0007_0033),
    _136("金额不能为空", C_2_0007_0034),
    _137("不能小于0.01元", C_2_0007_0035),
    _138("请填写货币格式", C_2_0007_0036),
    _139("不能为空", C_2_0007_0037),
    _140("提交话题错误", C_2_0007_0038),
    _141("删除评论失败", C_2_0007_0039),
    _142("删除回复失败", C_2_0007_0040),
    _143("只允许删除自己发布的评论", C_2_0007_0041),
    _144("只允许删除自己发布的回复", C_2_0007_0042),

    /**
     * -------------------------------- 问答 -----------------------------------
     **/
    _201("内容不能为空", C_2_0008_0001),
    _203("问题Id不能为空", C_2_0008_0002),
    _205("答案不存在", C_2_0008_0003),
    _206("禁止回答", C_2_0008_0004),
    _207("问题不存在", C_2_0008_0005),
    _208("实名用户才允许回答", C_2_0008_0006),
    _209("实名用户才允许提交问题", C_2_0008_0007),
    _210("不允许提交问题", C_2_0008_0008),
    _211("已提交问题才允许评论", C_2_0008_0009),
    _212("问题不存在", C_2_0008_0010),
    _214("问题已删除", C_2_0008_0011),
    _215("答案Id不能为空", C_2_0008_0012),
    _216("该问题已经采纳答案", C_2_0008_0013),
    _217("不是提交该问题的用户不允许采纳答案", C_2_0008_0014),
    _218("追加内容不能为空", C_2_0008_0015),
    _219("不是提交该问题的用户不允许追加提问", C_2_0008_0016),
    _220("不能超过12位数字", C_2_0008_0017),
    _221("请填写金额", C_2_0008_0018),
    _222("不能超过8位数字", C_2_0008_0019),
    _223("请填写正整数或0", C_2_0008_0020),

    _224("不能大于账户预存款", C_2_0008_0022),
    _225("不能小于0", C_2_0008_0023),
    _226("不能大于账户积分", C_2_0008_0024),
    _227("提交问题错误", C_2_0008_0025),
    _228("该问题不允许采纳答案", C_2_0008_0026),
    _229("不允许悬赏金额", C_2_0008_0027),
    _230("不能小于悬赏金额下限", C_2_0008_0028),
    _231("不能大于悬赏金额上限", C_2_0008_0029),
    _232("不允许悬赏积分", C_2_0008_0030),
    _233("不能小于悬赏积分下限", C_2_0008_0031),
    _234("不能大于悬赏积分上限", C_2_0008_0032),

    _240("只允许修改自己发布的答案", C_2_0008_0034),
    _241("答案已删除", C_2_0008_0035),
    _242("修改答案失败", C_2_0008_0036),
    _243("答案Id不能为空", C_2_0008_0037),
    _244("不允许修改已采纳的答案", C_2_0008_0038),
    _245("删除答案失败", C_2_0008_0039),
    /**
     * -------------------------------- 在线留言 -----------------------------------
     **/
    _301("不能超过100个字符", C_2_0009_0001),
    _302("名称不能为空", C_2_0009_0002),
    _303("联系方式不能为空", C_2_0009_0003),
    _304("内容不能为空", C_2_0009_0004),
    _305("在线留言已关闭", C_2_0009_0005),
    _306("字符超长", C_2_0009_0006),

    /**
     * -------------------------------- 修改用户 -----------------------------------
     **/
    _801("密码长度错误", C_2_2001_0001),
    _802("旧密码错误", C_2_2001_0002),
    _803("旧密码不能为空", C_2_2001_0003),
    _804("只允许输入数字", C_2_2001_0004),
    _805("只允许输入字母", C_2_2001_0005),
    _806("只允许输入数字和字母", C_2_2001_0006),
    _807("只允许输入汉字", C_2_2001_0007),
    _808("输入错误", C_2_2001_0008),
    _809("必填项", C_2_2001_0009),
    _810("修改用户失败", C_2_2001_0010),
    _811("账号不能小于3个字符", C_2_2001_0011),
    _812("账号不能大于25个字符", C_2_2001_0012),
    _813("账号只能输入由数字、26个英文字母或者下划线组成", C_2_2001_0013),
    _814("该账号已注册", C_2_2001_0014),
    _815("账号不能为空", C_2_2001_0015),
    _816("密码不能为空", C_2_2001_0016),
    _817("密码提示问题不能超过50个字符", C_2_2001_0017),
    _818("密码提示问题不能为空", C_2_2001_0018),
    _819("密码提示答案长度错误", C_2_2001_0019),
    _820("密码提示答案不能为空", C_2_2001_0020),
    _821("邮箱地址不正确", C_2_2001_0021),
    _822("邮箱地址不能超过60个字符", C_2_2001_0022),
    _823("注册会员出错", C_2_2001_0023),
    _824("禁止账号", C_2_2001_0024),
    _825("账号错误", C_2_2001_0025),
    _826("密码错误", C_2_2001_0026),
    _827("密码提示答案错误", C_2_2001_0027),
    _828("找回密码错误", C_2_2001_0028),
    _829("呢称不能超过15个字符", C_2_2001_0029),
    _830("该呢称已存在", C_2_2001_0030),
    _831("不允许修改呢称", C_2_2001_0031),
    _832("该呢称不允许使用", C_2_2001_0032),
    _833("呢称不能和其他用户名相同", C_2_2001_0033),
    _834("用户类型不能为空", C_2_2001_0034),
    _835("用户类型错误", C_2_2001_0035),
    _850("手机验证码错误", C_2_2001_0036),
    _851("手机号不能为空", C_2_2001_0037),
    _852("手机验证码不能为空", C_2_2001_0038),
    _853("手机号码不正确", C_2_2001_0039),
    _854("手机号码超长", C_2_2001_0040),
    _855("手机验证码超长", C_2_2001_0041),
    _856("手机验证码不存在或已过期", C_2_2001_0042),
    _857("手机号码不能重复绑定", C_2_2001_0043),
    _858("你还没有绑定手机", C_2_2001_0044),
    _859("用户不存在", C_2_2001_0045),
    _860("新手机号码不能和旧用机号码相同", C_2_2001_0046),
    _861("旧手机号码校验失败", C_2_2001_0047),
    _862("不允许注册", C_2_2001_0048),
    _863("该用户名不允许注册", C_2_2001_0049),
    _864("手机号码已注册", C_2_2001_0050),
    _865("用户类型不能为空", C_2_2001_0051),
    _866("手机号不能为空", C_2_2001_0052),
    _867("手机号错误", C_2_2001_0053),
    _868("不允许发短信", C_2_2001_0054),
    _869("手机用户不存在", C_2_2001_0055),
    _870("手机号不是手机账户", C_2_2001_0056),
    _910("用户不存在", C_2_2001_0057),
    _920("用户不是本地密码账户", C_2_2001_0058),
    _1000("不允许给当前用户发私信", C_2_2001_0059),
    _1010("不允许给自己发私信", C_2_2001_0060),
    _1020("对方用户名称不能为空", C_2_2001_0061),
    _1030("私信内容不能超过1000个字符", C_2_2001_0062),
    _1040("私信内容不能为空", C_2_2001_0063),
    _1050("删除私信失败", C_2_2001_0064),
    _1100("订阅系统通知Id不能为空", C_2_2001_0065),
    _1110("删除系统通知失败", C_2_2001_0066),
    _1200("不能超过8位数字", C_2_2001_0067),
    _1210("宽度必须大于0", C_2_2001_0068),
    _1230("高度必须大于0", C_2_2001_0069),
    _1250("X轴必须大于或等于0", C_2_2001_0070),
    _1270("Y轴必须大于或等于0", C_2_2001_0071),
    _1290("超出最大宽度", C_2_2001_0072),
    _1300("超出最大高度", C_2_2001_0073),
    _1310("当前文件类型不允许上传", C_2_2001_0074),
    _1320("文件超出允许上传大小", C_2_2001_0075),
    _1330("文件不能为空", C_2_2001_0076),
    _1400("提醒不存在", C_2_2001_0077),
    _1500("重复收藏", C_2_2001_0078),
    _1510("话题收藏Id不能为空", C_2_2001_0079),
    _1520("当前话题已经收藏", C_2_2001_0080),
    _1521("当前问题已经收藏", C_2_2001_0081),
    _1530("收藏Id不存在", C_2_2001_0082),
    _1540("删除收藏失败", C_2_2001_0083),
    _1550("收藏不存在", C_2_2001_0084),
    _1560("本收藏不属于当前用户", C_2_2001_0085),
    _1570("问题收藏Id不能为空", C_2_2001_0086),
    _1580("待收藏数据不存在", C_2_2001_0087),
    _1590("不允许同时收藏多项数据", C_2_2001_0088),


    _1600("话题重复取消隐藏", C_2_2001_0091),
    _1610("当前话题已经取消隐藏", C_2_2001_0092),
    _1620("隐藏标签不存在", C_2_2001_0093),
    _1630("密码错误", C_2_2001_0094),
    _1640("提交过于频繁，请稍后再提交", C_2_2001_0095),
    _1650("密码不能为空", C_2_2001_0096),
    _1660("话题内容不含当前标签", C_2_2001_0097),
    _1670("用户不存在", C_2_2001_0098),
    _1680("用户积分不足", C_2_2001_0099),
    _1685("用户余额不足", C_2_2001_0100),
    _1690("不允许解锁自已发表的话题", C_2_2001_0101),
    _1700("重复点赞", C_2_2001_0102),
    _1710("话题点赞Id不能为空", C_2_2001_0103),
    _1720("当前话题已经点赞", C_2_2001_0104),
    _1730("点赞Id不存在", C_2_2001_0105),
    _1740("删除点赞失败", C_2_2001_0106),
    _1750("点赞不存在", C_2_2001_0107),
    _1760("本点赞不属于当前用户", C_2_2001_0108),
    _1800("重复关注", C_2_2001_0109),
    _1810("关注Id不能为空", C_2_2001_0110),
    _1820("当前用户已关注对方", C_2_2001_0111),
    _1830("关注Id不存在", C_2_2001_0112),
    _1840("删除关注失败", C_2_2001_0113),
    _1850("关注不存在", C_2_2001_0114),
    _1860("本关注不属于当前用户", C_2_2001_0115),
    _1870("不能关注自身", C_2_2001_0116),
    _1900("规格Id不能为空", C_2_2001_0117),
    _1910("规格不存在", C_2_2001_0118),
    _1920("会员卡不存在", C_2_2001_0119),
    _1930("会员卡已下架", C_2_2001_0120),
    _1940("创建会员卡订单错误", C_2_2001_0121),
    _1950("角色不存在", C_2_2001_0122),
    _1960("库存不足", C_2_2001_0123),
    _1970("积分不足", C_2_2001_0124),
    _1980("预存款不足", C_2_2001_0125),
    _1990("此规格已下架", C_2_2001_0126),
    _2000("此会员卡已下架", C_2_2001_0127),


    _3010("没有发红包", C_2_2001_0130),
    _3020("发红包Id不能为空", C_2_2001_0131),
    _3030("收红包错误", C_2_2001_0132),
    _3040("每个红包只能领取一次", C_2_2001_0133),
    _3050("红包已被抢光", C_2_2001_0134),
    _3060("话题未发布不允许领取红包", C_2_2001_0135),
    _3070("话题不存在不允许领取红包", C_2_2001_0136),
    _3080("红包已原路返还用户", C_2_2001_0137),
    _3090("没有领取红包权限", C_2_2001_0138);


    private static final Map<String, ErrorView> lookup = new HashMap<String, ErrorView>();

    static {
        for (ErrorView s : EnumSet.allOf(ErrorView.class)) {

            lookup.put(s.name(), s);

        }

    }

    /**
     * 内容
     **/
    private String content;
    // 错误码
    private int errorCode;

    ErrorView(String content, int errorCode) {
        this.content = content;
        this.errorCode = errorCode;
    }

    ErrorView(String content) {
        this.content = content;
        this.errorCode = 0;
    }

    public static String get(String code) {
        return Optional.ofNullable(lookup.get(code)).map(ErrorView::getErrorCode).map(SpringUtils.getBean(ErrorMessageService.class)::getMessageNo500).map(o -> o.orElse(null)).orElse(null);
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getErrorCode() {
        return errorCode;
    }
}
