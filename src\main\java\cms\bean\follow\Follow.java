package cms.bean.follow;

import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 关注
 */
@Entity
@Table(name = "follow_0", indexes = {@Index(name = "follow_1_idx", columnList = "userName,addtime")})
public class Follow extends FollowEntity implements Serializable {
    private static final long serialVersionUID = -6073764011318403096L;


    @Transient
    private List<String> userRoleNameList = new ArrayList<>();

    public List<String> getUserRoleNameList() {
        return userRoleNameList;
    }

    public void setUserRoleNameList(List<String> userRoleNameList) {
        this.userRoleNameList = userRoleNameList;
    }
}
