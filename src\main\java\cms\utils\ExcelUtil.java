package cms.utils;

import com.alibaba.excel.EasyExcel;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/2/29 12:03
 */
public class ExcelUtil {

    public static <T> void writer(HttpServletResponse response, String fileName, Class<T> tClass, List<T> list) throws IOException {
        try {
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            EasyExcel.write(response.getOutputStream(), tClass).sheet(0).doWrite(list);
        } finally {
            response.flushBuffer();
        }
    }
}
