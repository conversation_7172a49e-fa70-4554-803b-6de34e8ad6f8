package cms.service.setting;

import cms.bean.setting.SystemSetting;
import cms.service.besa.DAO;

/**
 * 系统设置
 *
 * <AUTHOR>
 */
public interface SettingService extends DAO<SystemSetting> {
    /**
     * 查询系统设置
     *
     * @return
     **/
    SystemSetting findSystemSetting();

    /**
     * 查询系统设置 - 缓存
     *
     * @return
     */
    SystemSetting findSystemSetting_cache();

    /**
     * 修改系统设置
     *
     * @param systemSetting
     * @return
     */
    void updateSystemSetting(SystemSetting systemSetting);


}
