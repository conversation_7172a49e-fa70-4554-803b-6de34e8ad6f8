package cms.web.action.common;

import cms.bean.RequestResult;
import cms.bean.ResultCode;
import cms.bean.question.QuestionTag;
import cms.service.question.QuestionTagService;
import cms.utils.JsonUtils;
import cms.web.action.CSRFTokenManage;
import cms.web.action.question.QuestionTagManage;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/6/15 17:09
 */
@Controller
@RequestMapping("staticInfo/control/question")
@Slf4j
public class QuestionStaticAction {

    @Autowired
    private QuestionTagService questionTagService;
    @Autowired
    private QuestionTagManage questionTagManage;
    @Autowired
    private CSRFTokenManage csrfTokenManage;

    @ResponseBody
    @RequestMapping(value = "/getOneTag", method = RequestMethod.GET)
    public String getOneTag(String token, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, questionTagService.findAllQuestionTag_cache().stream().filter(o -> o.getGrade() == 1).collect(Collectors.toList())));
    }

    @ResponseBody
    @RequestMapping(value = "/getDistinctTwoTag", method = RequestMethod.GET)
    public String getDistinctTwoTag(String token, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);

        List<String> tagList = questionTagService.findAllQuestionTag_cache().stream().filter(o -> o.getGrade() == 2).map(QuestionTag::getName).distinct().collect(Collectors.toList());
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, tagList));
    }

    @ResponseBody
    @RequestMapping(value = "/getTagNum", method = RequestMethod.GET)
    public String getTwoTagNum(String token, String tagName, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, Optional.ofNullable(tagName).map(String::trim).filter(StringUtils::isNotEmpty).map(questionTagService::findTagCountTopic).orElse(0)));
    }

    @ResponseBody
    @RequestMapping(value = "/getTwoTagByParent", method = RequestMethod.GET)
    public String getTwoTagByParent(String token, Long tagId, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        List<QuestionTag> tagList = Optional.ofNullable(tagId).map(x -> questionTagService.findAllQuestionTag_cache().stream().filter(o -> x.equals(o.getParentId())).collect(Collectors.toList())).orElseGet(() -> Lists.newArrayList());
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, tagList));
    }

    @ResponseBody
    @RequestMapping(value = "/getOneTagAndCount", method = RequestMethod.GET)
    public String getOneTagAndCount(String token, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, questionTagManage.getOneTagNameCount()));
    }

    @ResponseBody
    @RequestMapping(value = "/isQueryData", method = RequestMethod.GET)
    public String isQueryData(String token, Boolean isQueryData, HttpServletRequest request) {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        return JSON.toJSONString(new RequestResult(ResultCode.SUCCESS, questionTagManage.isQueryData(isQueryData)));
    }
}
