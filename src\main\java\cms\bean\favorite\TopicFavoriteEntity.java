package cms.bean.favorite;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 话题收藏 实体类的抽象基类,定义基本属性
 */
@MappedSuperclass
public class TopicFavoriteEntity implements Serializable {
    private static final long serialVersionUID = 3856197671316097553L;

    /**
     * ID 格式(话题Id_用户Id)
     **/
    @Id
    @Column(length = 40)
    protected String id;
    /**
     * 话题收藏的用户名称
     **/
    @Column(length = 30)
    protected String userName;

    /**
     * 发布话题的用户名称
     **/
    @Column(length = 30)
    protected String postUserName;
    /**
     * 加入时间
     **/
    @Temporal(TemporalType.TIMESTAMP)
    protected Date addtime = new Date();

    /**
     * 话题Id
     **/
    protected Long topicId;

    /**
     * 话题标题
     **/
    @Transient
    protected String topicTitle;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getAddtime() {
        return addtime;
    }

    public void setAddtime(Date addtime) {
        this.addtime = addtime;
    }

    public Long getTopicId() {
        return topicId;
    }

    public void setTopicId(Long topicId) {
        this.topicId = topicId;
    }

    public String getTopicTitle() {
        return topicTitle;
    }

    public void setTopicTitle(String topicTitle) {
        this.topicTitle = topicTitle;
    }

    public String getPostUserName() {
        return postUserName;
    }

    public void setPostUserName(String postUserName) {
        this.postUserName = postUserName;
    }


}
