package cms.service.user.impl;


import cms.bean.captcha.Captcha;
import cms.bean.topdon.PlatUser;
import cms.bean.topdon.TopdonResult;
import cms.bean.user.TopDonLoginResult;
import cms.bean.user.TopDonQuickLoginResult;
import cms.bean.user.User;
import cms.constant.TopDonError;
import cms.handle.CustomException;
import cms.requset.bean.SendCaptcha;
import cms.utils.TopDonUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static cms.constant.ErrorCode.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/11 11:55
 */
@Service
@Slf4j
public class TopDonUserService {

    private final static String KEY_ALGORITHM = "AES";
    private final static String TOP_DON_SUCCESS = "2000";
    private final static String TOP_DON_USE_REGISTER = "60103";

    @Value("${topdon.path}")
    private String topDonUrl;
    @Value("${topdon.password.secretKey}")
    private String secretKey;

    public void verificationCode(String email, String type) {
        Map<String, Object> requestMap = Maps.newHashMap();
        requestMap.put("email", email);
        requestMap.put("type", type);
        String code = Optional.ofNullable(postTopDon(Maps.newHashMap(), "POST", requestMap, null, topDonUrl + "/api/v1/user/platUser/verificationCodeNew")).filter(Strings::isNotEmpty)
                .map(o -> JSON.parseObject(o, HashMap.class)).map(o -> o.get("code")).map(Object::toString).orElse(null);
        Optional.ofNullable(code).filter(o -> TOP_DON_SUCCESS.equals(o)).orElseThrow(() -> cusTomException(code, C_2_1001_0003, "userName"));
    }

    public String encrypt(String value) {
        AES aes = new AES(Mode.CBC, Padding.ZeroPadding, new SecretKeySpec(secretKey.getBytes(), KEY_ALGORITHM), new IvParameterSpec(secretKey.getBytes()));
        return Base64.encode(aes.encrypt(value.getBytes()));
    }

    public void register(User user) {
        Map<String, Object> requestMap = Maps.newHashMap();
        Optional.ofNullable(user.getEmail()).filter(Strings::isNotEmpty).ifPresent(o -> requestMap.put("email", o));
        requestMap.put("password", user.getPassword());
        requestMap.put("confirmPassword", user.getPassword());
        requestMap.put("validateCode", user.getValidateCode());
        String code = Optional.ofNullable(postTopDon(Maps.newHashMap(), "POST", requestMap, null, topDonUrl + "/api/v1/user/platUser/register")).filter(Strings::isNotEmpty)
                .map(o -> JSON.parseObject(o, HashMap.class)).map(o -> o.get("code")).map(Object::toString).orElse(null);
        Optional.ofNullable(code).filter(o -> TOP_DON_SUCCESS.equals(o) || TOP_DON_USE_REGISTER.equals(o)).orElseThrow(() -> cusTomException(code, C_2_1001_0002, "userName"));
    }

    public TopDonLoginResult loginByCode(String account, String code) {
        Map<String, String> queryParam = Maps.newHashMap();
        Map<String, String> headMap = Maps.newHashMap();
        headMap.put("Authorization", "Basic bGVua29yOmxlbmtvcg==");
        HashMap<String, Object> postParam = Maps.newHashMap();
        postParam.put("code", code);
        postParam.put("type", 4);
        postParam.put("email", account);

        TopDonQuickLoginResult topDonLoginResult = Optional.ofNullable(postTopDon(queryParam, "POST", postParam, headMap, topDonUrl + "/api/v1/user/platUserPro/quickLogin"))
                .filter(Strings::isNotEmpty)
                .map(o -> JSON.parseObject(o, TopDonQuickLoginResult.class))
                .orElse(null);
        if (topDonLoginResult == null || !topDonLoginResult.getSuccess()) {
            throw cusTomException(String.valueOf(topDonLoginResult.getCode()), C_2_1001_0001, "account");
        }

        TopDonQuickLoginResult.DataDTO data = topDonLoginResult.getData();

        TopDonLoginResult topDonLoginResult1 = new TopDonLoginResult();
        topDonLoginResult1.setCode(0);
        topDonLoginResult1.setAccess_token(data.getToken());
        topDonLoginResult1.setToken_type("");
        topDonLoginResult1.setRefresh_token(data.getRefreshToken());
        topDonLoginResult1.setExpires_in(data.getExpiresIn());
        topDonLoginResult1.setUser_type(0);
        topDonLoginResult1.setUser_id(data.getUserId());

        return topDonLoginResult1;
    }

    public TopDonLoginResult login(String account, String password) {
        Map<String, String> queryParam = Maps.newHashMap();
        queryParam.put("grant_type", "password");
        queryParam.put("password", password);
        queryParam.put("scope", "server");
        queryParam.put("username", account);
        Map<String, String> headMap = Maps.newHashMap();
        headMap.put("Authorization", "Basic bGVua29yOmxlbmtvcg==");
        TopDonLoginResult topDonLoginResult = Optional.ofNullable(postTopDon(queryParam, "POST", Maps.newHashMap(), headMap, topDonUrl + "/api/v1/auth/oauth/token"))
                .filter(Strings::isNotEmpty)
                .map(o -> JSON.parseObject(o, TopDonLoginResult.class))
                .orElse(null);
        Optional.ofNullable(topDonLoginResult)
                .filter(o -> null != o.getUser_id())
                .orElseThrow(() -> cusTomException(String.valueOf(topDonLoginResult.getCode()), C_2_1001_0001, "account"));
        return topDonLoginResult;
    }

    public void forgetPassword(String email, String password, String validateCode) {
        Map<String, Object> requestMap = Maps.newHashMap();
        requestMap.put("email", email);
        requestMap.put("password", password);
        requestMap.put("confirmPassword", password);
        requestMap.put("validateCode", validateCode);
        String code = Optional.ofNullable(postTopDon(Maps.newHashMap(), "POST", requestMap, null, topDonUrl + "/api/v1/user/platUser/forgetPassword")).filter(Strings::isNotEmpty)
                .map(o -> JSON.parseObject(o, HashMap.class)).map(o -> o.get("code")).map(Object::toString).orElse(null);
        Optional.ofNullable(code).filter(o -> TOP_DON_SUCCESS.equals(o)).orElseThrow(() -> cusTomException(code, C_2_1001_0002, "password"));
    }

    public TopdonResult<PlatUser> getUserInfoByApp(String token) {
        Map<String, String> headMap = Maps.newHashMap();
        headMap.put("Authorization", String.format("Bearer %s", token));
        // APP
        headMap.put("clientType", "2");
        TopdonResult<PlatUser> topdonResult = Optional.ofNullable(postTopDon(Maps.newHashMap(), "GET", Maps.newHashMap(), headMap, topDonUrl + "/api/v1/user/platUser/query")).filter(Strings::isNotEmpty)
                .map(o -> JSONUtil.parseObj(o).toBean(TopdonResult.class)).map(o -> Optional.ofNullable(o.getData()).filter(v -> v instanceof JSONObject).map(v -> {
                    o.setData(JSONUtil.toBean((JSONObject) v, PlatUser.class));
                    return o;
                }).orElse(o)).orElse(null);
        Optional<String> code = Optional.ofNullable(topdonResult).map(TopdonResult::getCode).map(o -> o.toString());
        code.filter(TOP_DON_SUCCESS::equals).orElseThrow(() -> cusTomException(code.orElse("0").toString(), C_2_1001_0005, "Authorization"));
        return topdonResult;
    }

    public String postTopDon(Map<String, String> queryParam, String method, Map<String, Object> requestMap, Map<String, String> headMap, String url) {
        String result = null;
        TopDonUtil.initSign(queryParam);

        RequestBody requestBody = Optional.ofNullable(method).filter(o -> "POST".equals(method.toUpperCase())).map(o -> RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(requestMap))).orElse(null);
        Map<String, String> h = Optional.ofNullable(headMap).orElse(Maps.newHashMap());
        h.put("Content-Type", "application/json");
        Optional.ofNullable(h).filter(o -> Strings.isEmpty(o.get("clientType"))).ifPresent(o -> o.put("clientType", "3"));
        Headers headers = Headers.of(h);
        Request request = new Request.Builder()
                .url(String.format("%s?%s", url, TopDonUtil.getParamString(queryParam)))
                .method(method, requestBody)
                .headers(headers)
                .build();
        try {
            result = postTopDon(request);
            log.info(url + result);
        } catch (RuntimeException e) {
            throw new RuntimeException(String.format("%s 调用失败！", url));
        } catch (Exception e) {
            throw new RuntimeException(String.format("%s 调用失败！", url));
        }
        return result;
    }

    private String postTopDon(Request request) {
        Response response = null;
        try {
            OkHttpClient httpClient = new OkHttpClient().newBuilder()
                    .connectTimeout(60, TimeUnit.SECONDS)
                    .readTimeout(60, TimeUnit.SECONDS)
                    .writeTimeout(60, TimeUnit.SECONDS).build();
            response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                return response.body().string();
            } else {
                Optional<String> responseJson = Optional.of(response.body()).map(o -> {
                    try {
                        return o.string();
                    } catch (Exception e) {
                        return "";
                    }
                }).filter(Strings::isNotEmpty);
                return responseJson.orElse("没有");
            }
        } catch (Throwable e) {
            log.error("调远程调用topDon失败", e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return null;
    }

    private CustomException cusTomException(String errorCode, int defaultErrorCode, String filedName) {
        return new CustomException(Integer.valueOf(String.format("%s%s", "3", errorCode)), defaultErrorCode, TopDonError.getFiledName(errorCode).orElse(filedName));
    }

    public Captcha sendCaptcha(SendCaptcha sendCaptcha) {
        Map<String, String> queryParam = Maps.newHashMap();
        Map<String, String> headMap = Maps.newHashMap();
        headMap.put("Authorization", "Basic bGVua29yOmxlbmtvcg==");
        HashMap<String, Object> postParam = Maps.newHashMap();
        postParam.put("contactWay", sendCaptcha.getEmail());
        postParam.put("type", 0);
        postParam.put("operationType", 6);

        return Optional.ofNullable(postTopDon(queryParam, "POST", postParam, headMap, topDonUrl + "/api/v1/user/platUserPro/sendCaptcha"))
                .filter(Strings::isNotEmpty)
                .map(o -> JSON.parseObject(o, Captcha.class))
                .orElse(null);
    }
}
