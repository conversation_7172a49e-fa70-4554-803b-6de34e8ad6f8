package cms.bean.like;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/18 10:27
 */
@MappedSuperclass
@Data
public class ReplyLikeEntity implements Serializable {
    private static final long serialVersionUID = 3112294654438891737L;

    @Id
    @Column(length = 40)
    protected String id;

    @Column(length = 30)
    protected String userName;

    @Column(length = 30)
    protected String postUserName;

    @Temporal(TemporalType.TIMESTAMP)
    protected Date addtime = new Date();

    protected Long replyId;
}
