package cms.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/19 10:01
 */
public class ErrorUtil {

    public static void putFalseReturnValue(Map<String, Object> returnValue, Map<String, String> returnError) {
        returnValue.put("success", "false");
        returnValue.put("error", returnError);
    }

    public static Map<String, String> getErrorMap(String filedName, String msg) {
        Map<String, String> returnError = new HashMap<String, String>();
        returnError.put(filedName, msg);
        return returnError;
    }

    public static Map<String, Object> getErrorReturnValue(String filedName, String msg) {
        Map<String, Object> returnValue = new HashMap<String, Object>();//返回值
        returnValue.put("success", "false");
        returnValue.put("error", getErrorMap(filedName,msg));
        return returnValue;
    }
}
