package cms.service.user;

import cms.bean.user.UserCustom;
import cms.bean.user.UserInputValue;
import cms.service.besa.DAO;

import java.util.List;

/**
 * 用户自定义注册功能项接口
 */
public interface UserCustomService extends DAO<UserCustom> {
    /**
     * 根据Id查询用户自定义注册功能项
     */
    UserCustom findUserCustomById(Integer userCustomId);

    /**
     * 查询所有用户自定义注册功能项
     */
    List<UserCustom> findAllUserCustom();

    /**
     * 查询所有用户自定义注册功能项
     */
    List<UserCustom> findAllUserCustom_cache();

    /**
     * 根据用户名称查询自定义项值
     *
     * @param userId 用户Id
     * @return
     */
    List<UserInputValue> findUserInputValueByUserName(Long userId);

    /**
     * 保存自定义项
     *
     * @param userCustom 用户自定义项
     */
    void saveUserCustom(UserCustom userCustom);

    /**
     * 修改自定义项值
     *
     * @param userCustom 自定义项
     * @param deleteItem 删除自定义单选按钮.多选按钮.下拉列表  key参数
     */
    Integer updateUserCustom(UserCustom userCustom, List<String> deleteItem);

    /**
     * 删除自定义项值
     *
     * @param userCustomId 自定义项Id
     */
    Integer deleteUserCustom(Integer userCustomId);
}
