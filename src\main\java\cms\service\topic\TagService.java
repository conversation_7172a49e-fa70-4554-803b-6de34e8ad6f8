package cms.service.topic;

import cms.bean.topic.Tag;
import cms.service.besa.DAO;

import java.util.List;
import java.util.Map;

/**
 * 标签
 */
public interface TagService extends DAO<Tag> {
    /**
     * 根据Id查询标签
     *
     * @param tagId 标签Id
     * @return
     */
    Tag findById(Long tagId);

    /**
     * 查询所有标签
     *
     * @return
     */
    List<Tag> findAllTag();

    /**
     * 根据上级Id传销标签
     *
     * @return
     */
    List<Tag> findTagByParentId(Long tagId);

    /**
     * 根据上级Id传销标签
     *
     * @return
     */
    List<Tag> findTagByParentIdAndName(Long tagId, String name);

    /**
     * 查询二级标签命下话题总数
     *
     * @param twoTagName
     * @return
     */
    int findTwoTagCountTopic(String twoTagName);

    Map<Long, Integer> findTwoTagCountTopic();

    /**
     * 查询标签命下话题总数
     *
     * @param tagIds
     * @return
     */
    List<Map<String, Long>> findTagCountTopic(List<Long> tagIds);

    /**
     * 查询所有标签 - 缓存
     *
     * @return
     */
    List<Tag> findAllTag_cache();

    /**
     * 保存标签
     *
     * @param tag
     */
    void saveTag(Tag tag);

    /**
     * 修改标签
     *
     * @param tag
     * @return
     */
    Integer updateTag(Tag tag);

    /**
     * 删除标签
     *
     * @param tagId 标签Id
     */
    Integer deleteTag(Long tagId);
}
