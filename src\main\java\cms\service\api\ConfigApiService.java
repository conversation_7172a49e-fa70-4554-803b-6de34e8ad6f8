package cms.service.api;

import cms.bean.operation.OperationTag;
import cms.bean.setting.EditorTag;
import cms.bean.topic.Tag;
import cms.service.operation.OperationTagService;
import cms.service.topic.TagService;
import cms.web.action.setting.SettingManage;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static cms.constant.Constant.TOPIC_TAG_GRADE_ONE;
import static cms.constant.Constant.TOPIC_TAG_GRADE_TWO;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/25 11:40
 */
@Service
public class ConfigApiService {

    @Autowired
    private TagService tagService;
    @Autowired
    private OperationTagService operationTagService;
    @Resource
    SettingManage settingManage;

    @Cacheable(value = "api_topic_sys_config_cache", key = "'getTopicAndSys_'+#operationId")
    public TopicSysConfig getTopicAndSys(long operationId) {
        TopicSysConfig topicSysConfig = new TopicSysConfig();
        operationTagService.getByOperationId(operationId).map(l -> l.stream().map(OperationTag::getTagId).collect(Collectors.toList())).ifPresent(l -> {
            List<Tag> tags = tagService.findAllTag_cache();
            topicSysConfig.setOneTag(tags.stream().filter(o -> o.getGrade() == TOPIC_TAG_GRADE_ONE && l.contains(o.getId())).collect(Collectors.toList()));
            topicSysConfig.setTwoTag(tags.stream().filter(o -> o.getGrade() == TOPIC_TAG_GRADE_TWO && l.contains(o.getId())).collect(Collectors.toList()));
        });
        Optional<EditorTag> editorTagOptional = Optional.ofNullable(settingManage.readTopicEditorTag());
        // 文件大小校验
        editorTagOptional.ifPresent(o -> {
            topicSysConfig.setImage(o.isImage());
            topicSysConfig.setImageSize(o.getImageSize());
            topicSysConfig.setImageFormat(o.getImageFormat());
        });
        return topicSysConfig;
    }

    @Data
    public static class TopicSysConfig implements Serializable {
        private static final long serialVersionUID = -684257451052921859L;

        List<Tag> oneTag;
        List<Tag> twoTag;
        // 允许上传图片
        private boolean image = false;
        // 允许上传图片大小
        private Long imageSize;
        // 允许上传图片格式
        private List<String> imageFormat;
    }
}
