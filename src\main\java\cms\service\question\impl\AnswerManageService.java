package cms.service.question.impl;

import cms.bean.PageForm;
import cms.bean.QueryResult;
import cms.bean.RequestResult;
import cms.bean.ResultCode;
import cms.bean.platformShare.QuestionRewardPlatformShare;
import cms.bean.question.Answer;
import cms.bean.question.AnswerReply;
import cms.bean.question.Question;
import cms.bean.staff.SysUsers;
import cms.bean.user.PointLog;
import cms.bean.user.User;
import cms.constant.ErrorCode;
import cms.handle.CustomException;
import cms.service.aliyun.OssFileChangeService;
import cms.service.platformShare.PlatformShareService;
import cms.service.question.AnswerService;
import cms.service.question.QuestionService;
import cms.service.setting.SettingService;
import cms.service.user.UserService;
import cms.utils.IpAddress;
import cms.utils.JsonUtils;
import cms.web.action.SystemException;
import cms.web.action.TextFilterManage;
import cms.web.action.fileSystem.FileManage;
import cms.web.action.payment.PaymentManage;
import cms.web.action.question.AnswerManage;
import cms.web.action.question.QuestionManage;
import cms.web.action.setting.SettingManage;
import cms.web.action.user.PointManage;
import cms.web.action.user.UserManage;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/6/30 18:07
 */
@Service
public class AnswerManageService {

    @Resource
    AnswerService answerService;//通过接口引用代理返回的对象
    @Resource
    SettingManage settingManage;
    @Resource
    AnswerManage answerManage;

    @Resource
    TextFilterManage textFilterManage;

    @Resource
    SettingService settingService;
    @Resource
    FileManage fileManage;
    @Resource
    UserService userService;
    @Resource
    UserManage userManage;
    @Resource
    QuestionManage questionManage;
    @Resource
    QuestionService questionService;
    @Resource
    PointManage pointManage;
    @Resource
    PaymentManage paymentManage;
    @Resource
    PlatformShareService platformShareService;
    @Autowired
    private OssFileChangeService ossFileChangeService;

    @Transactional(propagation = Propagation.REQUIRED)
    public String add(ModelMap model, Long questionId, String content,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {

        Map<String, String> error = new HashMap<String, String>();
        if (questionId == null || questionId <= 0L) {
            error.put("questionId", "问题Id不能为空");
        }
        if (error.size() == 0 && content != null && !"".equals(content.trim())) {

            //过滤标签
            content = textFilterManage.filterTag(request, content, settingManage.readAnswerEditorTag());
            Object[] object = textFilterManage.filterHtml(request, content, "answer", settingManage.readAnswerEditorTag());
            String value = (String) object[0];
            List<String> imageNameList = (List<String>) object[1];
            boolean isImage = (Boolean) object[2];//是否含有图片
            boolean isMedia = (Boolean) object[6];//是否含有音视频
            //不含标签内容
            String text = textFilterManage.filterText(content);
            //清除空格&nbsp;
            String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();

            if (isImage == true || isMedia || (!"".equals(text.trim()) && !"".equals(trimSpace))) {
                String username = "";//用户名称
                String userId = "";//用户Id
                Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
                if (obj instanceof SysUsers) {
                    userId = ((SysUsers) obj).getUserId();
                    username = ((SysUsers) obj).getUserAccount();
                }

                Answer answer = new Answer();
                answer.setQuestionId(questionId);
                answer.setContent(value);
                answer.setIsStaff(true);
                answer.setUserName(username);
                answer.setIp(IpAddress.getClientIpAddress(request));
                answer.setStatus(20);
                //保存答案
                answerService.saveAnswer(answer);

                //修改问题最后回答时间
                questionService.updateQuestionAnswerTime(questionId, new Date());
            }
        } else {
            error.put("content", "回答内容不能为空");
        }

        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String edit(PageForm pageForm, ModelMap model, Long answerId, String content, Integer status,
                       HttpServletRequest request, HttpServletResponse response) throws Exception {

        Answer answer = null;
        Integer old_status = -1;
        Map<String, String> error = new HashMap<String, String>();
        if (answerId == null || answerId <= 0) {
            error.put("answerId", "答案Id不能为空");
        } else {
            answer = answerService.findByAnswerId(answerId);
        }
        if (content != null && !"".equals(content.trim())) {
            if (answer != null) {
                old_status = answer.getStatus();
                answer.setStatus(status);

                //过滤标签
                content = textFilterManage.filterTag(request, content, settingManage.readAnswerEditorTag());
                Object[] object = textFilterManage.filterHtml(request, content, "answer", settingManage.readAnswerEditorTag());
                String value = (String) object[0];
                List<String> imageNameList = (List<String>) object[1];
                boolean isImage = (Boolean) object[2];//是否含有图片
                boolean isMedia = (Boolean) object[6];//是否含有音视频
                //不含标签内容
                String text = textFilterManage.filterText(content);
                //清除空格&nbsp;
                String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();

                if (isImage == true || isMedia || (!"".equals(text.trim()) && !"".equals(trimSpace))) {

                    String username = answer.getUserName();//用户名称

                    //修改答案
                    int i = answerService.updateAnswer(answer.getId(), value, status, username);

                    if (i > 0 && !old_status.equals(status)) {
                        User user = userManage.query_cache_findUserByUserName(answer.getUserName());
                        if (user != null) {
                            //修改答案状态
                            userService.updateUserDynamicAnswerStatus(user.getId(), answer.getUserName(), answer.getQuestionId(), answer.getId(), answer.getStatus());
                        }
                    }

                    //删除缓存
                    answerManage.delete_cache_findByAnswerId(answer.getId());
                    answerManage.delete_cache_answerCount(answer.getUserName());

                    //旧图片名称
                    ossFileChangeService.ossImgFileChange(answer.getContent(), "answer", imageNameList);
                } else {
                    error.put("content", "答案内容不能为空");
                }
            } else {
                error.put("answerId", "答案不能为空");
            }
        } else {
            error.put("content", "答案内容不能为空");
        }

        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String delete(ModelMap model, Long[] answerId,
                         HttpServletRequest request, HttpServletResponse response) throws Exception {

        String username = "";//用户名称
        String userId = "";//用户Id
        Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (obj instanceof SysUsers) {
            userId = ((SysUsers) obj).getUserId();
            username = ((SysUsers) obj).getUserAccount();
        }
        Map<String, String> error = new HashMap<String, String>();//错误

        if (answerId != null && answerId.length > 0) {
            List<Long> answerIdList = new ArrayList<Long>();
            for (Long l : answerId) {
                if (l != null && l > 0L) {
                    answerIdList.add(l);
                }
            }
            if (answerIdList != null && answerIdList.size() > 0) {
                List<Answer> answerList = answerService.findByAnswerIdList(answerIdList);
                if (answerList != null && answerList.size() > 0) {
                    Optional.of(answerList.stream().filter(o -> o.getAdoption()).count()).filter(o -> o > 0).orElseThrow(() -> new CustomException(ErrorCode.C_2_0008_0043, "answer"));
                    for (Answer answer : answerList) {
                        if (answer.getStatus() < 100) {//标记删除
                            Integer constant = 100000;
                            int i = answerService.markDeleteAnswer(answer.getQuestionId(), answer.getId(), constant);

                            if (i > 0) {
                                User user = userManage.query_cache_findUserByUserName(answer.getUserName());
                                if (user != null) {
                                    //修改答案状态
                                    userService.updateUserDynamicAnswerStatus(user.getId(), answer.getUserName(), answer.getQuestionId(), answer.getId(), answer.getStatus() + constant);
                                }
                                //删除缓存
                                answerManage.delete_cache_findByAnswerId(answer.getId());
                                questionManage.delete_cache_findById(answer.getQuestionId());
                            }
                        } else {//物理删除
                            Question question = questionManage.query_cache_findById(answer.getQuestionId());
                            if (question != null) {
                                Date time = new Date();

                                User user = userManage.query_cache_findUserByUserName(answer.getUserName());

                                //取消采纳用户名称
                                String cancelAdoptionUserName = null;
                                //取消采纳用户退还悬赏积分日志
                                Object cancelAdoptionPointLogObject = null;
                                //取消采纳用户退还分成金额
                                BigDecimal cancelAdoptionUserNameShareAmount = new BigDecimal("0");
                                //取消采纳用户退还悬赏金额日志
                                Object cancelAdoptionPaymentLogObject = null;

                                if (question.getAdoptionAnswerId() > 0L) {//已悬赏
                                    cancelAdoptionUserName = answer.getUserName();

                                    if (user != null && answer.getIsStaff() == false) {
                                        if (question.getPoint() != null && question.getPoint() > 0L) {
                                            PointLog reward_pointLog = new PointLog();
                                            reward_pointLog.setId(pointManage.createPointLogId(user.getId()));
                                            reward_pointLog.setModule(1100);//1100.采纳答案
                                            reward_pointLog.setParameterId(answer.getId());//参数Id
                                            reward_pointLog.setOperationUserType(1);//操作用户类型  0:系统  1: 员工  2:会员
                                            reward_pointLog.setOperationUserName(username);//操作用户名称
                                            reward_pointLog.setPointState(2);//积分状态  1:账户存入  2:账户支出
                                            reward_pointLog.setPoint(question.getPoint());//积分
                                            reward_pointLog.setUserName(answer.getUserName());//用户名称
                                            reward_pointLog.setRemark("");
                                            reward_pointLog.setTimes(time);
                                            cancelAdoptionPointLogObject = pointManage.createPointLogObject(reward_pointLog);
                                        }
                                    }
                                }

                                try {
                                    int i = answerService.deleteAnswer(answer.getQuestionId(), answer.getId(), cancelAdoptionUserName, cancelAdoptionPointLogObject, cancelAdoptionUserNameShareAmount, cancelAdoptionPaymentLogObject, question.getPoint());
                                    if (i > 0) {
                                        //根据答案Id删除用户动态(答案下的回复也同时删除)
                                        userService.deleteUserDynamicByAnswerId(answer.getQuestionId(), answer.getId());

                                        //删除缓存
                                        answerManage.delete_cache_findByAnswerId(answer.getId());
                                        answerManage.delete_cache_answerCount(answer.getUserName());
                                        if (user != null) {
                                            userManage.delete_cache_findUserById(user.getId());
                                            userManage.delete_cache_findUserByUserName(user.getUserName());
                                        }

                                        //删除图片
                                        ossFileChangeService.ossImgFileChange(answer.getContent(), "answer", Lists.newArrayList());
                                    }
                                } catch (SystemException e) {
                                    error.put("answer", e.getMessage());
                                }

                            } else {
                                error.put("question", "问题不存在");
                            }

                        }
                    }
                }
            }
        } else {
            error.put("answerId", "答案Id不能为空");
        }
        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String auditAnswer(ModelMap model, Long answerId,
                              HttpServletResponse response) throws Exception {
        Map<String, String> error = new HashMap<String, String>();

        if (answerId != null && answerId > 0L) {
            int i = answerService.updateAnswerStatus(answerId, 20);

            Answer answer = answerManage.query_cache_findByAnswerId(answerId);
            if (i > 0 && answer != null) {
                User user = userManage.query_cache_findUserByUserName(answer.getUserName());
                if (user != null) {
                    //修改答案状态
                    userService.updateUserDynamicAnswerStatus(user.getId(), answer.getUserName(), answer.getQuestionId(), answer.getId(), 20);
                }
                //删除缓存
                answerManage.delete_cache_findByAnswerId(answerId);
                answerManage.delete_cache_answerCount(answer.getUserName());
                return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
            } else {
                error.put("answerId", "答案不存在或修改答案状态失败");
            }
        } else {
            error.put("answerId", "答案Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String addAnswerReply(ModelMap model, Long answerId, String content,
                                 HttpServletRequest request, HttpServletResponse response) throws Exception {
        Answer answer = null;
        Map<String, String> error = new HashMap<String, String>();
        if (answerId == null || answerId <= 0) {
            error.put("answerId", "答案Id不能为空");
        } else {
            answer = answerService.findByAnswerId(answerId);
        }


        if (content != null && !"".equals(content.trim())) {
            if (answer != null) {
                //过滤标签
                content = textFilterManage.filterTag(request, content, settingManage.readEditorTag());
                Object[] object = textFilterManage.filterHtml(request, content, "comment", settingManage.readEditorTag());
                String value = (String) object[0];
                boolean isImage = (Boolean) object[2];//是否含有图片
                boolean isMedia = (Boolean) object[6];//是否含有音视频
                //不含标签内容
                String text = textFilterManage.filterText(content);
                //清除空格&nbsp;
                String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();

                if (isImage || isMedia || (!"".equals(text.trim()) && !"".equals(trimSpace))) {
                    String username = "";//用户名称

                    Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
                    if (obj instanceof UserDetails) {
                        username = ((UserDetails) obj).getUsername();
                    }


                    //回复
                    AnswerReply answerReply = new AnswerReply();
                    answerReply.setAnswerId(answer.getId());
                    answerReply.setIsStaff(true);
                    answerReply.setUserName(username);
                    answerReply.setContent(value);
                    answerReply.setQuestionId(answer.getQuestionId());
                    answerReply.setStatus(20);
                    answerReply.setIp(IpAddress.getClientIpAddress(request));
                    //保存答案回复
                    answerService.saveReply(answerReply);


                    //修改问题最后回答时间
                    questionService.updateQuestionAnswerTime(answer.getQuestionId(), new Date());
                } else {
                    error.put("content", "回复内容不能为空");

                }
            } else {
                error.put("answerId", "答案不存在");
            }
        } else {
            error.put("content", "回复内容不能为空");
        }

        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String editAnswerReply(ModelMap model, Long answerReplyId, String content, Integer status,
                                  HttpServletRequest request, HttpServletResponse response) throws Exception {
        AnswerReply answerReply = null;
        Integer old_status = -1;
        Map<String, String> error = new HashMap<String, String>();
        if (answerReplyId != null && answerReplyId > 0) {
            answerReply = answerService.findReplyByReplyId(answerReplyId);
        } else {
            error.put("answerReplyId", "回复Id不能为空");

        }

        if (content != null && !"".equals(content.trim())) {
            if (answerReply != null) {
                old_status = answerReply.getStatus();
                answerReply.setStatus(status);
                //过滤标签
                content = textFilterManage.filterTag(request, content, settingManage.readEditorTag());
                Object[] object = textFilterManage.filterHtml(request, content, "comment", settingManage.readEditorTag());
                String value = (String) object[0];
                boolean isImage = (Boolean) object[2];//是否含有图片
                boolean isMedia = (Boolean) object[6];//是否含有音视频
                //不含标签内容
                String text = textFilterManage.filterText(content);
                //清除空格&nbsp;
                String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();

                if (isImage || isMedia || (!"".equals(text.trim()) && !"".equals(trimSpace))) {
                    String username = answerReply.getUserName();//用户名称
                    //修改回复
                    int i = answerService.updateReply(answerReplyId, value, username, status);

                    if (i > 0 && !old_status.equals(status)) {
                        User user = userManage.query_cache_findUserByUserName(answerReply.getUserName());
                        if (user != null) {
                            //修改答案回复状态
                            userService.updateUserDynamicAnswerReplyStatus(user.getId(), answerReply.getUserName(), answerReply.getQuestionId(), answerReply.getAnswerId(), answerReply.getId(), answerReply.getStatus());
                        }

                    }


                    //删除缓存
                    answerManage.delete_cache_findReplyByReplyId(answerReplyId);
                } else {
                    error.put("content", "回复内容不能为空");

                }
            } else {
                error.put("answerReplyId", "回复不存在");
            }
        } else {
            error.put("content", "回复内容不能为空");
        }

        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String deleteAnswerReply(ModelMap model, Long[] answerReplyId,
                                    HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, String> error = new HashMap<String, String>();

        if (answerReplyId != null && answerReplyId.length > 0) {
            List<Long> answerReplyIdList = new ArrayList<Long>();
            for (Long l : answerReplyId) {
                if (l != null && l > 0L) {
                    answerReplyIdList.add(l);
                }
            }
            if (answerReplyIdList != null && answerReplyIdList.size() > 0) {
                List<AnswerReply> answerReplyList = answerService.findByAnswerReplyIdList(answerReplyIdList);
                if (answerReplyList != null && answerReplyList.size() > 0) {
                    for (AnswerReply answerReply : answerReplyList) {
                        if (answerReply.getStatus() < 100) {//标记删除
                            Integer constant = 100000;
                            int i = answerService.markDeleteReply(answerReply.getId(), constant);


                            if (i > 0) {
                                User user = userManage.query_cache_findUserByUserName(answerReply.getUserName());
                                if (user != null) {
                                    //修改回复状态
                                    userService.updateUserDynamicAnswerReplyStatus(user.getId(), answerReply.getUserName(), answerReply.getQuestionId(), answerReply.getAnswerId(), answerReply.getId(), answerReply.getStatus() + constant);
                                }
                                //删除缓存
                                answerManage.delete_cache_findReplyByReplyId(answerReply.getId());
                            }
                        } else {//物理删除
                            int i = answerService.deleteReply(answerReply.getId());
                            if (i > 0 && answerReply != null) {
                                User user = userManage.query_cache_findUserByUserName(answerReply.getUserName());
                                if (user != null) {
                                    userService.deleteUserDynamicByAnswerReplyId(user.getId(), answerReply.getQuestionId(), answerReply.getAnswerId(), answerReply.getId());
                                }
                            }

                            //删除缓存
                            answerManage.delete_cache_findReplyByReplyId(answerReply.getId());
                        }

                    }
                }
            }
        } else {
            error.put("answerReplyId", "回复Id不能为空");
        }

        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String auditAnswerReply(ModelMap model, Long answerReplyId,
                                   HttpServletResponse response) throws Exception {
        Map<String, String> error = new HashMap<String, String>();
        if (answerReplyId != null && answerReplyId > 0L) {
            int i = answerService.updateReplyStatus(answerReplyId, 20);

            AnswerReply answerReply = answerManage.query_cache_findReplyByReplyId(answerReplyId);
            if (answerReply != null) {
                User user = userManage.query_cache_findUserByUserName(answerReply.getUserName());
                if (i > 0 && user != null) {
                    //修改答案回复状态
                    userService.updateUserDynamicAnswerReplyStatus(user.getId(), answerReply.getUserName(), answerReply.getQuestionId(), answerReply.getAnswerId(), answerReply.getId(), 20);
                }
            }

            //删除缓存
            answerManage.delete_cache_findReplyByReplyId(answerReplyId);
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        } else {
            error.put("answerReplyId", "回复Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String recoveryReply(ModelMap model, Long replyId,
                                HttpServletRequest request, HttpServletResponse response) throws Exception {

        Map<String, String> error = new HashMap<String, String>();
        if (replyId != null && replyId > 0) {
            AnswerReply answerReply = answerService.findReplyByReplyId(replyId);
            if (answerReply != null && answerReply.getStatus() > 100) {
                int originalState = this.parseInitialValue(answerReply.getStatus());
                int i = answerService.updateReplyStatus(replyId, originalState);

                User user = userManage.query_cache_findUserByUserName(answerReply.getUserName());
                if (i > 0 && user != null) {
                    //修改回复状态
                    userService.updateUserDynamicAnswerReplyStatus(user.getId(), answerReply.getUserName(), answerReply.getQuestionId(), answerReply.getAnswerId(), answerReply.getId(), originalState);
                }
                //删除缓存
                answerManage.delete_cache_findReplyByReplyId(replyId);

                return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
            } else {
                error.put("replyId", "回复不存在或未标记删除");
            }

        } else {
            error.put("replyId", "回复Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Map<String, String> adoptionAnswer(Long answerId) {
        Map<String, String> error = new HashMap<String, String>();//错误
        String username = "";//用户名称
        String userId = "";//用户Id
        Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (obj instanceof SysUsers) {
            userId = ((SysUsers) obj).getUserId();
            username = ((SysUsers) obj).getUserAccount();
        }

        if (answerId != null && answerId > 0L) {
            Answer answer = answerManage.query_cache_findByAnswerId(answerId);
            if (answer != null) {
                Question question = questionManage.query_cache_findById(answer.getQuestionId());
                if (question != null) {
                    Date time = new Date();

                    User user = userManage.query_cache_findUserByUserName(answer.getUserName());
                    User old_user = null;
                    //取消采纳用户名称
                    String cancelAdoptionUserName = null;
                    //取消采纳用户退还悬赏积分日志
                    Object cancelAdoptionPointLogObject = null;
                    //取消采纳用户退还分成金额
                    BigDecimal cancelAdoptionUserNameShareAmount = new BigDecimal("0");
                    //取消采纳用户退还悬赏金额日志
                    Object cancelAdoptionPaymentLogObject = null;

                    //是否更改采纳答案
                    boolean changeAdoption = false;
                    if (question.getAdoptionAnswerId() > 0L) {
                        changeAdoption = true;


                        StringBuffer jpql = new StringBuffer();
                        //存放参数值
                        List<Object> params = new ArrayList<Object>();
                        //排序
                        LinkedHashMap<String, String> orderby = new LinkedHashMap<String, String>();

                        jpql.append(" o.questionId=?" + (params.size() + 1));
                        params.add(question.getId());//设置参数


                        orderby.put("adoption", "desc");//采纳答案时间排序   新-->旧
                        //根据sort字段降序排序
                        QueryResult<Answer> qr = answerService.getScrollData(Answer.class, 0, 1, jpql.toString(), params.toArray(), orderby);

                        List<Answer> answerList = qr.getResultlist();
                        if (answerList != null && answerList.size() > 0) {
                            //上一个答案被采纳用户退还积分和金额到问题
                            for (Answer old_answer : answerList) {
                                old_user = userManage.query_cache_findUserByUserName(old_answer.getUserName());
                                cancelAdoptionUserName = old_answer.getUserName();


                                if (old_user != null && old_answer.getIsStaff() == false) {
                                    if (question.getPoint() != null && question.getPoint() > 0L) {
                                        PointLog reward_pointLog = new PointLog();
                                        reward_pointLog.setId(pointManage.createPointLogId(old_user.getId()));
                                        reward_pointLog.setModule(1100);//1100.采纳答案
                                        reward_pointLog.setParameterId(old_answer.getId());//参数Id
                                        reward_pointLog.setOperationUserType(1);//操作用户类型  0:系统  1: 员工  2:会员
                                        reward_pointLog.setOperationUserName(username);//操作用户名称
                                        reward_pointLog.setPointState(2);//积分状态  1:账户存入  2:账户支出
                                        reward_pointLog.setPoint(question.getPoint());//积分
                                        reward_pointLog.setUserName(old_answer.getUserName());//用户名称
                                        reward_pointLog.setRemark("");
                                        reward_pointLog.setTimes(time);
                                        cancelAdoptionPointLogObject = pointManage.createPointLogObject(reward_pointLog);
                                    }
                                }
                            }
                        }
                    }

                    //回答用户获得积分
                    Long point = 0L;

                    //用户悬赏积分日志
                    Object pointLogObject = null;
                    if (user != null && answer.getIsStaff() == false && question.getPoint() != null && question.getPoint() > 0L) {
                        point = question.getPoint();

                        PointLog reward_pointLog = new PointLog();
                        reward_pointLog.setId(pointManage.createPointLogId(user.getId()));
                        reward_pointLog.setModule(1100);//1100.采纳答案
                        reward_pointLog.setParameterId(answer.getId());//参数Id
                        reward_pointLog.setOperationUserType(1);//操作用户类型  0:系统  1: 员工  2:会员
                        reward_pointLog.setOperationUserName(username);//操作用户名称
                        reward_pointLog.setPointState(1);//积分状态  1:账户存入  2:账户支出
                        reward_pointLog.setPoint(question.getPoint());//积分
                        reward_pointLog.setUserName(answer.getUserName());//用户名称
                        reward_pointLog.setRemark("");
                        reward_pointLog.setTimes(time);
                        pointLogObject = pointManage.createPointLogObject(reward_pointLog);
                    }

                    //用户悬赏金额日志
                    Object paymentLogObject = null;
                    //平台分成
                    QuestionRewardPlatformShare questionRewardPlatformShare = null;
                    //回答用户分成金额
                    BigDecimal userNameShareAmount = new BigDecimal("0");

                    try {
                        int i = answerService.updateAdoptionAnswer(answer.getQuestionId(), answerId, changeAdoption, cancelAdoptionUserName, cancelAdoptionPointLogObject, cancelAdoptionUserNameShareAmount, cancelAdoptionPaymentLogObject,
                                answer.getUserName(), point, pointLogObject, userNameShareAmount, paymentLogObject, questionRewardPlatformShare);
                    } catch (SystemException e) {
                        error.put("adoptionAnswer", e.getMessage());
                    }
                    //删除缓存
                    answerManage.delete_cache_findByAnswerId(answerId);
                    questionManage.delete_cache_findById(answer.getQuestionId());
                    answerManage.delete_cache_answerCount(answer.getUserName());
                    if (user != null) {
                        userManage.delete_cache_findUserById(user.getId());
                        userManage.delete_cache_findUserByUserName(user.getUserName());
                    }
                    if (old_user != null) {
                        userManage.delete_cache_findUserById(old_user.getId());
                        userManage.delete_cache_findUserByUserName(old_user.getUserName());
                    }
                } else {
                    error.put("question", "问题不存在");
                }
            } else {
                error.put("answer", "答案不存在");
            }

        } else {
            error.put("answerId", "答案Id不能为空");
        }
        return error;
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Map<String, String> cancelAdoptionAnswer(Long answerId) {
        String username = "";//用户名称
        String userId = "";//用户Id
        Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (obj instanceof SysUsers) {
            userId = ((SysUsers) obj).getUserId();
            username = ((SysUsers) obj).getUserAccount();
        }
        Map<String, String> error = new HashMap<String, String>();//错误

        if (answerId != null && answerId > 0L) {
            Answer answer = answerManage.query_cache_findByAnswerId(answerId);
            if (answer != null) {
                Question question = questionManage.query_cache_findById(answer.getQuestionId());
                if (question != null) {
                    Date time = new Date();

                    User user = userManage.query_cache_findUserByUserName(answer.getUserName());

                    //取消采纳用户名称
                    String cancelAdoptionUserName = answer.getUserName();
                    //取消采纳用户退还悬赏积分日志
                    Object cancelAdoptionPointLogObject = null;
                    //取消采纳用户退还分成金额
                    BigDecimal cancelAdoptionUserNameShareAmount = new BigDecimal("0");
                    //取消采纳用户退还悬赏金额日志
                    Object cancelAdoptionPaymentLogObject = null;


                    if (user != null && answer.getIsStaff() == false) {
                        if (question.getPoint() != null && question.getPoint() > 0L) {
                            PointLog reward_pointLog = new PointLog();
                            reward_pointLog.setId(pointManage.createPointLogId(user.getId()));
                            reward_pointLog.setModule(1100);//1100.采纳答案
                            reward_pointLog.setParameterId(answer.getId());//参数Id
                            reward_pointLog.setOperationUserType(1);//操作用户类型  0:系统  1: 员工  2:会员
                            reward_pointLog.setOperationUserName(username);//操作用户名称
                            reward_pointLog.setPointState(2);//积分状态  1:账户存入  2:账户支出
                            reward_pointLog.setPoint(question.getPoint());//积分
                            reward_pointLog.setUserName(answer.getUserName());//用户名称
                            reward_pointLog.setRemark("");
                            reward_pointLog.setTimes(time);
                            cancelAdoptionPointLogObject = pointManage.createPointLogObject(reward_pointLog);
                        }
                    }

                    try {
                        int i = answerService.updateCancelAdoptionAnswer(answer.getQuestionId(), cancelAdoptionUserName, cancelAdoptionPointLogObject, cancelAdoptionUserNameShareAmount, cancelAdoptionPaymentLogObject, question.getPoint());
                    } catch (SystemException e) {
                        error.put("adoptionAnswer", e.getMessage());
                    }

                    //删除缓存
                    answerManage.delete_cache_findByAnswerId(answerId);
                    questionManage.delete_cache_findById(answer.getQuestionId());
                    answerManage.delete_cache_answerCount(answer.getUserName());

                    if (user != null) {
                        userManage.delete_cache_findUserById(user.getId());
                        userManage.delete_cache_findUserByUserName(user.getUserName());
                    }
                } else {
                    error.put("question", "问题不存在");
                }


            } else {
                error.put("answer", "答案不存在");
            }

        } else {
            error.put("answerId", "答案Id不能为空");
        }
        return error;
    }

    /**
     * 解析初始值
     *
     * @param status 状态
     * @return
     */
    private int parseInitialValue(Integer status) {
        int tens = status % 100 / 10;//十位%100/10
        int units = status % 10;//个位直接%10

        return Integer.parseInt(tens + "" + units);
    }
}
