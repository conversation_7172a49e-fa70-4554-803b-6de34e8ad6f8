package cms.web.action.common;


import cms.bean.setting.EditorTag;
import cms.bean.user.AccessUser;
import cms.constant.FileConstant;
import cms.service.aliyun.OssFileChangeService;
import cms.service.question.impl.AnswerFormService;
import cms.utils.threadLocal.AccessUserThreadLocal;
import cms.web.action.setting.SettingManage;
import cms.web.action.user.UserRoleManage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;

/**
 * 答案接收表单
 */
@Controller
@RequestMapping("user/control/answer")
public class AnswerFormAction {

    @Resource
    UserRoleManage userRoleManage;

    @Resource
    SettingManage settingManage;

    @Autowired
    private AnswerFormService answerFormService;
    @Autowired
    private OssFileChangeService ossFileChangeService;

    /**
     * 答案   添加
     *
     * @param model
     * @param questionId 问题Id
     * @param content    评论内容
     * @param jumpUrl    跳转地址
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public String add(ModelMap model, Long questionId, String content,
                      String token, String captchaKey, String captchaValue, String jumpUrl,
                      RedirectAttributes redirectAttrs,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {
        return answerFormService.add(model, questionId, content, token, captchaKey, captchaValue, jumpUrl, redirectAttrs, request, response);
    }

    /**
     * 答案  修改
     *
     * @param model
     * @param answerId 答案Id
     * @param content  内容
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    public String edit(ModelMap model, Long answerId, String content,
                       String token, String captchaKey, String captchaValue, String jumpUrl,
                       RedirectAttributes redirectAttrs,
                       HttpServletRequest request, HttpServletResponse response) throws Exception {
        return answerFormService.edit(model, answerId, content, token, captchaKey, captchaValue, jumpUrl, redirectAttrs, request, response);
    }

    /**
     * 答案  删除
     *
     * @param model
     * @param answerId 答案Id
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public String delete(ModelMap model, Long answerId,
                         String token, String jumpUrl,
                         RedirectAttributes redirectAttrs,
                         HttpServletRequest request, HttpServletResponse response) throws Exception {
        return answerFormService.delete(model, answerId, token, jumpUrl, redirectAttrs, request, response);
    }

    /**
     * 答案  图片上传
     *
     * @param model
     * @param questionId 问题Id
     * @param fileName   文件名称 预签名时有值
     * @param file
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/uploadImage", method = RequestMethod.POST)
    @ResponseBody//方式来做ajax,直接返回字符串
    public String uploadImage(ModelMap model, Long questionId, String fileName,
                              MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //获取登录用户
        Optional<AccessUser> accessUser = Optional.ofNullable(AccessUserThreadLocal.get());
        Optional<EditorTag> editorTagOptional = Optional.ofNullable(settingManage.readAnswerEditorTag());
        return ossFileChangeService.upload(null, file, editorTagOptional, FileConstant.FILE_TYPE_IMG, accessUser.map(o -> String.format("b%s", o.getUserId())), "answer", Optional.of(questionId).map(String::valueOf));
    }


    /**
     * 回复  添加
     *
     * @param model
     * @param answerId     答案回复Id
     * @param content
     * @param token
     * @param captchaKey
     * @param captchaValue
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/addAnswerReply", method = RequestMethod.POST)
    public String addAnswerReply(ModelMap model, Long answerId, String content,
                                 String token, String captchaKey, String captchaValue, String jumpUrl,
                                 RedirectAttributes redirectAttrs,
                                 HttpServletRequest request, HttpServletResponse response) throws Exception {
        return answerFormService.addAnswerReply(model, answerId, content, token, captchaKey, captchaValue, jumpUrl, redirectAttrs, request, response);
    }


    /**
     * 答案回复  修改
     *
     * @param model
     * @param replyId  回复Id
     * @param content  内容
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/editReply", method = RequestMethod.POST)
    public String editReply(ModelMap model, Long replyId, String content,
                            String token, String captchaKey, String captchaValue, String jumpUrl,
                            RedirectAttributes redirectAttrs,
                            HttpServletRequest request, HttpServletResponse response) throws Exception {
        return answerFormService.editReply(model, replyId, content, token, captchaKey, captchaValue, jumpUrl, redirectAttrs, request, response);
    }

    /**
     * 答案回复  删除
     *
     * @param model
     * @param replyId  回复Id
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/deleteReply", method = RequestMethod.POST)
    public String deleteReply(ModelMap model, Long replyId,
                              String token, String jumpUrl,
                              RedirectAttributes redirectAttrs,
                              HttpServletRequest request, HttpServletResponse response) throws Exception {
        return answerFormService.deleteReply(model, replyId, token, jumpUrl, redirectAttrs, request, response);
    }
}
