package cms.bean.operation;

import cms.bean.BaseUserDel;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/25 11:59
 */
@Entity
@Table(name = "operation")
@Data
public class Operation extends BaseUserDel implements Serializable {

    private static final long serialVersionUID = -1L;

    @Column(length = 190)
    private String title;

    @Column(length = 500)
    private String remark;
}
