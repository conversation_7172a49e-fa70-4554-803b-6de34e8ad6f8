package cms.bean.user;

import cms.constraints.ContainsObject;
import cms.constraints.PatternString;
import cms.constraints.PhoneNumber;
import cms.constraints.validateGroups.*;
import cms.listeners.UserNullInitListeners;
import cms.service.aliyun.OssFileChangeService;
import lombok.Data;
import org.apache.logging.log4j.util.Strings;
import org.hibernate.validator.constraints.Length;
import org.joda.time.DateTime;

import javax.persistence.*;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

import static cms.constant.Constant.DATE_YMD;
import static cms.constant.Constant.business_avatar;


/**
 * 用户管理
 */
@Entity
@Table(name = "user", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"account"}),
        @UniqueConstraint(columnNames = {"platformUserId"}
        )}, indexes = {@Index(name = "user_idx", columnList = "state"), @Index(name = "user_2_idx", columnList = "userName")}
)//给user字段添加唯一性约束
@Data
@EntityListeners(UserNullInitListeners.class)
public class User implements Serializable {
    private static final long serialVersionUID = 3692366870616346904L;

    public final static String DEFAULT_NICKNAME_TITLE = "TD";
    public static AtomicInteger DEFAULT_NICKNAME_NUM = new AtomicInteger(1);

    /**
     * ID
     **/
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 会员用户名 从5.6版开始，会员用户名由含有大小写的UUID生成，登录对接账号(account)字段
     **/
    @Column(length = 30, nullable = false)
    private String userName;

    /**
     * 账号 在注册时不能含有横杆和冒号   注销账号后，字段值会加上双冒号和注册时间，例如 test::*************
     **/
    @Column(length = 65, nullable = false)
    @NotEmpty(groups = {Add.class, LocalUserRegister.class})
    @Length(min = 3, max = 30, groups = {Add.class, Update.class, LocalUserRegister.class})
    @PatternString(regex = "^[a-zA-Z._0-9@]{0,30}$", groups = {Add.class, Update.class, LocalUserRegister.class})
    private String account;

    /**
     * 注销账号时间
     **/
    private Long cancelAccountTime = -1L;

    /**
     * 呢称
     **/
    @Column(length = 50, nullable = false)
    @Length(max = 15, groups = {Add.class, Update.class})
    private String nickname;
    /**
     * 密码 密码结构: sha256(sha256(密码)+[盐值])
     **/
    @Column(length = 160)
    @NotEmpty(groups = {Add.class, ForgetPassword.class, UserRegister.class})
    @Length(max = 30, groups = {Add.class, Update.class})
    private String password;
    /**
     * 盐值
     **/
    @Column(length = 80)
    private String salt;


    /**
     * 安全摘要 需要用户重新登录时改变此值
     **/
    private Long securityDigest;

    /**
     * 是否允许显示用户动态
     **/
    @Column(nullable = false)
    @NotNull(groups = {Add.class, Update.class})
    private Boolean allowUserDynamic = true;

    /**
     * 邮箱地址
     **/
    @Column(length = 60, nullable = false)
    @Length(max = 60, groups = {Add.class, Update.class, LocalUserRegister.class})
    @Email(groups = {Add.class, Update.class, LocalUserRegister.class})
    @NotEmpty(groups = {Add.class, Update.class, LocalUserRegister.class})
    private String email;

    /**
     * 简介
     **/
    @Column(length = 500, nullable = false)
    @Length(max = 500, groups = {Add.class, Update.class})
    private String intro = "";

    /**
     * 密码提示问题
     **/
    @Column(length = 50)
    @Length(max = 50, groups = {Add.class, Update.class, LocalUserRegister.class})
    private String issue;
    /**
     * 密码提示答案
     **/
    @Column(length = 80, nullable = false)
    @Length(max = 50, groups = {Add.class, Update.class})
    private String answer;


    /**
     * 实名认证绑定手机
     **/
    @Column(length = 20)
    @Length(max = 18, groups = {Add.class, Update.class, PhoneUserRegister.class})
    @PhoneNumber(groups = {Add.class, Update.class, PhoneUserRegister.class})
    @NotEmpty(groups = {PhoneUserRegister.class})
    private String mobile;
    /**
     * 是否实名认证
     **/
    @Column(nullable = false)
    @NotNull(groups = {Add.class, Update.class})
    private boolean realNameAuthentication;

    /**
     * 注册日期
     **/
    @Temporal(TemporalType.TIMESTAMP)
    private Date registrationDate;
    /**
     * 备注
     **/
    @Lob
    private String remarks;
    /**
     * 当前积分
     **/
    @Min(value = 0, groups = {Add.class, Update.class})
    private Long point = 0L;
    /**
     * 当前预存款
     **/
    @Column(precision = 14, scale = 4, nullable = false)
    private BigDecimal deposit = new BigDecimal("0");

    /**
     * 用户类型 10:本地账号密码用户 20: 手机用户 30: 邮箱用户 40:微信用户 80:其他用户
     **/
    @NotNull(groups = {Add.class, Update.class, ForgetPassword.class, UserRegister.class})
    @ContainsObject(contains = {"10", "20"}, groups = {ForgetPassword.class, UserRegister.class})
    private Integer type = 10;
    /**
     * 平台用户Id   本地账号密码用户类型为'会员用户名';  手机用户类型为'手机号-mobile';  邮箱用户类型为'邮箱-email';  第三方用户类型的为'第三方用户Id-第三方平台标记',例如微信为'unionid-weixin'    注销后本字段值会加上双冒号和注册时间，例如 oORvU5oUs7AAAsBhw59G3jkUCQlk-weixin::1640850298000
     **/
    @Column(length = 90, nullable = false)
    private String platformUserId;

    /**
     * 用户状态    1:启用   2:停用   11: 启用时删除   12: 停用时删除
     **/
    @Column(nullable = false)
    @NotNull(groups = {Add.class, Update.class})
    @Max(value = 2, groups = {Add.class, Update.class})
    @Min(value = 1, groups = {Add.class, Update.class})
    private Integer state = 1;

    @Column(nullable = false)
    private Boolean emailRemind = true;

    /**
     * 用户版本号
     **/
    private Integer userVersion = 0;

    /**
     * 用户角色名称集合
     **/
    @Transient
    private List<String> userRoleNameList = new ArrayList<String>();
    /**
     * 当前等级ID  只用于统计显示,不写入数据库
     **/
    @Transient
    private Integer gradeId;
    /**
     * 当前等级  只用于统计显示,不写入数据库
     **/
    @Transient
    private String gradeName;

    @Transient
    private Integer userGrade;
    /**
     * 头像路径 不写入数据库
     **/
    @Transient
    private String avatarPath;
    /**
     * 头像名称
     **/
    @Column(length = 50)
    private String avatarName;

    /**
     * 验证码
     */
    @Transient
//    @NotEmpty(groups = {Add.class})
    private String validateCode;

    @Transient
    private UserTopDon userTopDon;

    public String getAvatarPath() {
        if (this.avatarPath == null || "".equals(this.avatarPath.trim())) {
            Optional<String> date = Optional.ofNullable(this.getRegistrationDate()).map(DateTime::new).map(o -> o.toString(DATE_YMD));
            avatarPath = date.map(o -> OssFileChangeService.getPathDirStatic(business_avatar, o).toString()).orElse("");
        }
        return avatarPath;
    }

    public String getAvatarPath(String ossUrl) {
        return Optional.ofNullable(this.getAvatarPath()).filter(Strings::isNotEmpty).map(o -> ossUrl + o).orElse(null);
    }
}
