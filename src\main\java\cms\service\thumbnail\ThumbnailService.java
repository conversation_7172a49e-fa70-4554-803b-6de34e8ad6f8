package cms.service.thumbnail;

import cms.bean.thumbnail.Thumbnail;
import cms.service.besa.DAO;

import java.util.List;

/**
 * 缩略图DAO接口
 */
public interface ThumbnailService extends DAO<Thumbnail> {
    /**
     * 查询所有缩略图
     *
     * @return
     */
	List<Thumbnail> findAllThumbnail();

    /**
     * 查询所有缩略图 缓存
     *
     * @return
     */
	List<Thumbnail> findAllThumbnail_cache();

    /**
     * 根据缩略图Id查询缩略图
     *
     * @param thumbnailId
     * @return
     */
	Thumbnail findByThumbnailId(Integer thumbnailId);

    /**
     * 根据规格组查询缩略图
     *
     * @param specificationGroup 规格组
     * @return
     */
	Thumbnail findThumbnailBySpecificationGroup(String specificationGroup);

    /**
     * 保存缩略图
     *
     * @param thumbnail
     */
	void saveThumbnail(Thumbnail thumbnail);

    /**
     * 删除缩略图
     *
     * @param thumbnailId 缩略图Id
     */
	int deleteThumbnail(Integer thumbnailId);
}
