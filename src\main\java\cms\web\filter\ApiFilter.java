package cms.web.filter;

import cms.bean.api.InnerapiUser;
import cms.bean.topdon.PlatUser;
import cms.bean.topdon.TopdonResult;
import cms.bean.user.AccessUser;
import cms.constant.BbsInnerApiAuthUtil;
import cms.handle.CustomException;
import cms.service.api.InnerapiUserService;
import cms.service.user.UserService;
import cms.service.user.impl.TopDonUserService;
import cms.utils.threadLocal.AccessUserThreadLocal;
import cms.web.action.fileSystem.FileManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;

import javax.annotation.Resource;
import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Optional;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

import static cms.constant.Constant.USER_TYPE_LOCAL;
import static cms.constant.ErrorCode.*;

/**
 * api检查
 */
@WebFilter(urlPatterns = "/api/*", filterName = "apiFilter", asyncSupported = true)
@Slf4j
@Order(2)
public class ApiFilter implements Filter {

    private static final int max_request_num = 500;
    private static final Semaphore requestSh = new Semaphore(max_request_num);

    @Autowired
    private InnerapiUserService innerapiUserService;

    @Override
    public void destroy() {

    }

    private final static ThreadLocal<String> clientIdHolder = new ThreadLocal<>();

    @Override
    public void doFilter(ServletRequest req, ServletResponse res,
                         FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) req;
        try {
            Optional.ofNullable(requestSh.tryAcquire(20, TimeUnit.SECONDS)).filter(o -> o).ifPresent(o -> clientValidate(request));
        } catch (InterruptedException e) {
            log.error("api filter InterruptedException ", e);
            throw new CustomException(C_1_0001_0013, "limit");
        } finally {
            requestSh.release();
        }

        chain.doFilter(req, res);
    }

    @Override
    public void init(FilterConfig arg0) throws ServletException {
        // TODO Auto-generated method stub

    }

    public void clientValidate(HttpServletRequest request) {
        String clientId = null;
        String stamp = null;
        String sign = null;
        String clientSecret;
        String allowUrls;
        try {
            clientId = Optional.ofNullable(request.getHeader(BbsInnerApiAuthUtil.HEADER_CLIENTID)).orElseThrow(() -> new CustomException(C_1_0001_0014, "clientId"));
            stamp = Optional.ofNullable(request.getHeader(BbsInnerApiAuthUtil.HEADER_STAMP)).orElseThrow(() -> new CustomException(C_1_0001_0014, "stamp"));
            sign = Optional.ofNullable(request.getHeader(BbsInnerApiAuthUtil.HEADER_SIGN)).orElseThrow(() -> new CustomException(C_1_0001_0014, "sign"));
            InnerapiUser innerapiUser = innerapiUserService.getByIdFromCache(clientId).filter(o -> !o.getDeleted()).orElseThrow(() -> new CustomException(C_1_0001_0015, "notFind"));
            clientSecret = Optional.ofNullable(innerapiUser.getClientSecret()).orElseThrow(() -> new CustomException(C_1_0001_0015, "secret"));
            allowUrls = Optional.ofNullable(innerapiUser.getAllowUrls()).orElse(Strings.EMPTY);

            //签名检查
            BbsInnerApiAuthUtil.verifyStampAndSign(clientSecret, stamp, sign);
            clientIdHolder.set(clientId);

            //接口权限检查
            Optional.ofNullable(isAllow(request.getMethod(), request.getServletPath(), allowUrls)).filter(o -> o).orElseThrow(() -> new CustomException(C_1_0001_0019, "allow"));
        } catch (CustomException e) {
            log.info("auth fail: clientId:" + clientId + ", stamp:" + stamp + ", sign:" + sign + ", verfy:" + e.getCode() + ", url:" + request.getRequestURI() + ", ip:" + getIpAddress(request));
            throw e;
        }
        log.info("auth success: clientId:" + clientId + ", stamp:" + stamp + ", url:" + request.getRequestURI() + ", ip:" + getIpAddress(request));

    }

    private final static boolean isAllow(String method, String servletPath, String allowUrls) {
        if (allowUrls != null) {
            allowUrls = allowUrls.trim();
        }
        if (allowUrls != null && allowUrls.length() > 0) {
            String thisRequestID = method.toLowerCase() + ":" + servletPath.toLowerCase();
            String[] allowUrlArr = allowUrls.split(",|;|，|；");
            boolean matchUrl = false;
            for (String allowUrl : allowUrlArr) {
                allowUrl = allowUrl.trim().toLowerCase();
                if (allowUrl.isEmpty()) {
                    continue;
                }
                if (allowUrl.equals("*") || allowUrl.equals("all") || allowUrl.equals(thisRequestID)
                        || Pattern.compile(allowUrl.replaceAll("\\*", ".*")).matcher(thisRequestID).find()) {
                    matchUrl = true;
                    break;
                }
            }
            if (!matchUrl) {//无接口权限
                log.info("forbid access: " + thisRequestID);
                return false;
            }
        }
        return true;
    }

    public static String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
