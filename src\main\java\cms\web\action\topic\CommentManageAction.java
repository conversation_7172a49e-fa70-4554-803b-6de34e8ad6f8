package cms.web.action.topic;

import cms.bean.PageForm;
import cms.bean.RequestResult;
import cms.bean.ResultCode;
import cms.bean.setting.EditorTag;
import cms.bean.topic.Comment;
import cms.bean.topic.Quote;
import cms.bean.topic.Reply;
import cms.bean.user.User;
import cms.constant.ErrorCode;
import cms.handle.MsgException;
import cms.service.aliyun.OssFileChangeService;
import cms.service.topic.CommentService;
import cms.service.topic.impl.CommentManageService;
import cms.service.user.UserService;
import cms.utils.IpAddress;
import cms.utils.JsonUtils;
import cms.web.action.fileSystem.FileManage;
import cms.web.action.setting.SettingManage;
import cms.web.action.user.UserManage;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static cms.constant.FileConstant.SIZE_SPLIT;

/**
 * 评论
 */
@Controller
@RequestMapping("/control/comment/manage")
public class CommentManageAction {

    @Resource
    CommentService commentService;//通过接口引用代理返回的对象
    @Resource
    SettingManage settingManage;
    @Resource
    CommentManage commentManage;

    @Resource
    FileManage fileManage;
    @Resource
    UserService userService;
    @Resource
    UserManage userManage;
    @Autowired
    private OssFileChangeService ossFileChangeService;
    @Autowired
    private CommentManageService commentManageService;


    /**
     * 评论  添加
     */
    @ResponseBody
    @RequestMapping(params = "method=add", method = RequestMethod.POST)
    public String add(ModelMap model, Long topicId, String content,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {
        return commentManageService.add(model, topicId, content, request, response);
    }


    /**
     * 评论  修改页面显示
     */
    @ResponseBody
    @RequestMapping(params = "method=edit", method = RequestMethod.GET)
    public String editUI(ModelMap model, Long commentId,
                         HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, String> error = new HashMap<String, String>();
        Map<String, Object> returnValue = new HashMap<String, Object>();


        returnValue.put("availableTag", commentManage.availableTag());

        if (commentId != null && commentId > 0L) {
            Comment comment = commentService.findByCommentId(commentId);
            if (comment != null) {
                if (comment.getContent() != null && !"".equals(comment.getContent().trim())) {
                    //处理富文本路径
                    comment.setContent(fileManage.processRichTextFilePath(comment.getContent(), "comment"));
                }
                if (comment.getQuote() != null && !"".equals(comment.getQuote().trim())) {
                    //引用
                    List<Quote> customQuoteList = JsonUtils.toGenericObject(comment.getQuote(), new TypeReference<List<Quote>>() {
                    });
                    comment.setQuoteList(customQuoteList);
                }
                returnValue.put("comment", comment);
                String username = "";
                Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
                if (obj instanceof UserDetails) {
                    username = ((UserDetails) obj).getUsername();
                }
                returnValue.put("userName", username);
            } else {
                error.put("commentId", "评论不存在");
            }

        } else {
            error.put("commentId", "评论Id不能为空");
        }


        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, returnValue));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    /**
     * 评论  修改
     */
    @ResponseBody
    @RequestMapping(params = "method=edit", method = RequestMethod.POST)
    public String edit(PageForm pageForm, ModelMap model, Long commentId, String content, Integer status,
                       HttpServletRequest request, HttpServletResponse response) throws Exception {
        return commentManageService.edit(pageForm, model, commentId, content, status, request, response);
    }

    /**
     * 评论  删除
     *
     * @param model
     * @param commentId 评论Id
     */
    @ResponseBody
    @RequestMapping(params = "method=delete", method = RequestMethod.POST)
    public String delete(ModelMap model, Long[] commentId,
                         HttpServletRequest request, HttpServletResponse response) throws Exception {
        return commentManageService.delete(model, commentId, request, response);
    }

    /**
     * 评论  恢复
     *
     * @param model
     * @param commentId 评论Id
     */
    @ResponseBody
    @RequestMapping(params = "method=recoveryComment", method = RequestMethod.POST)
    public String recoveryComment(ModelMap model, Long commentId,
                                  HttpServletRequest request, HttpServletResponse response) throws Exception {
        return commentManageService.recoveryComment(model, commentId, request, response);
    }

    /**
     * 解析初始值
     *
     * @param status 状态
     * @return
     */
    private int parseInitialValue(Integer status) {
        int tens = status % 100 / 10;//十位%100/10
        int units = status % 10;//个位直接%10

        return Integer.parseInt(tens + "" + units);
    }


    /**
     * 评论  图片上传
     * <p>
     * 员工发话题 上传文件名为UUID + a + 员工Id
     * 用户发话题 上传文件名为UUID + b + 用户Id
     *
     * @param topicId  话题Id
     * @param userName 用户名称
     * @param isStaff  是否是员工   true:员工   false:会员
     * @param fileName 文件名称 预签名时有值
     */
    @RequestMapping(params = "method=uploadImage", method = RequestMethod.POST)
    @ResponseBody//方式来做ajax,直接返回字符串
    public String uploadImage(ModelMap model, Long topicId, String userName, Boolean isStaff, String fileName,
                              MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {

        Optional<EditorTag> editorTagOptional = Optional.ofNullable(settingManage.readEditorTag());
        return ossFileChangeService.upload(errorMessageService -> {
            Long size = file.getSize();
            editorTagOptional.map(EditorTag::isImage).filter(o -> o).orElseThrow(() -> new MsgException(errorMessageService.getMessage(ErrorCode.C_2_0001_0012)));
            editorTagOptional.map(EditorTag::getImageSize).filter(o -> size / SIZE_SPLIT <= o).orElseThrow(() -> new MsgException(errorMessageService.getMessage(ErrorCode.C_2_0001_0013, String.valueOf(size / SIZE_SPLIT))));
        }, file, editorTagOptional.map(EditorTag::getImageFormat), userName, isStaff, "comment", Optional.ofNullable(topicId).map(String::valueOf));
    }

    /**
     * 引用  添加页面显示
     *
     * @param model
     * @param commentId 评论Id
     */
    @ResponseBody
    @RequestMapping(params = "method=addQuote", method = RequestMethod.GET)
    public String addQuoteUI(ModelMap model, Long commentId,
                             HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, String> error = new HashMap<String, String>();
        Map<String, Object> returnValue = new HashMap<String, Object>();


        returnValue.put("availableTag", commentManage.availableTag());
        Comment comment = commentService.findByCommentId(commentId);
        if (comment != null) {
            if (comment.getContent() != null && !"".equals(comment.getContent().trim())) {
                //处理富文本路径
                comment.setContent(fileManage.processRichTextFilePath(comment.getContent(), "comment"));
            }
            if (comment.getQuote() != null && !"".equals(comment.getQuote().trim())) {
                //引用
                List<Quote> customQuoteList = JsonUtils.toGenericObject(comment.getQuote(), new TypeReference<List<Quote>>() {
                });
                comment.setQuoteList(customQuoteList);
            }
            returnValue.put("comment", comment);
        } else {
            error.put("commentId", "评论Id不能为空");
        }
        String username = "";//用户名称

        Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (obj instanceof UserDetails) {
            username = ((UserDetails) obj).getUsername();
        }
        returnValue.put("userName", username);

        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, returnValue));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    /**
     * 引用  添加
     *
     * @param model
     * @param commentId 评论Id
     */
    @ResponseBody
    @RequestMapping(params = "method=addQuote", method = RequestMethod.POST)
    public String addQuote(ModelMap model, Long commentId, String content,
                           HttpServletRequest request, HttpServletResponse response) throws Exception {
        return commentManageService.addQuote(model, commentId, content, request, response);
    }


    /**
     * 审核评论
     *
     * @param model
     * @param commentId 评论Id
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(params = "method=auditComment", method = RequestMethod.POST)
    public String auditComment(ModelMap model, Long commentId,
                               HttpServletResponse response) throws Exception {
        return commentManageService.auditComment(model, commentId, response);
    }


    /**
     * 回复  添加页面显示
     *
     * @param pageForm
     * @param model
     * @param commentId 自定义评论Id
     */
    @ResponseBody
    @RequestMapping(params = "method=addReply", method = RequestMethod.GET)
    public String addReplyUI(ModelMap model, Long commentId,
                             HttpServletRequest request, HttpServletResponse response) throws Exception {

        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
    }

    /**
     * 回复  添加
     *
     * @param model
     * @param commentId 评论Id
     */
    @ResponseBody
    @RequestMapping(params = "method=addReply", method = RequestMethod.POST)
    public String addReply(ModelMap model, Long commentId, String content,
                           HttpServletRequest request, HttpServletResponse response) throws Exception {
        return commentManageService.addReply(model, commentId, content, request, response);
    }

    /**
     * 回复  修改页面显示
     *
     * @param pageForm
     * @param model
     * @param replyId  回复Id
     */
    @ResponseBody
    @RequestMapping(params = "method=editReply", method = RequestMethod.GET)
    public String editReplyUI(ModelMap model, Long replyId,
                              HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, String> error = new HashMap<String, String>();
        Map<String, Object> returnValue = new HashMap<String, Object>();
        if (replyId != null && replyId > 0) {
            Reply reply = commentService.findReplyByReplyId(replyId);
            if (reply != null) {
                if (reply.getIp() != null && !"".equals(reply.getIp().trim())) {

                    reply.setIpAddress(IpAddress.queryAddress(reply.getIp()));
                }
                returnValue.put("reply", reply);
                return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, returnValue));
            }
        } else {
            error.put("replyId", "回复Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    /**
     * 回复  修改
     *
     * @param model
     * @param replyId  回复Id
     * @param content  内容
     * @param status   状态
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(params = "method=editReply", method = RequestMethod.POST)
    public String editReply(ModelMap model, Long replyId, String content, Integer status,
                            HttpServletRequest request, HttpServletResponse response) throws Exception {
        return commentManageService.editReply(model, replyId, content, status, request, response);
    }

    /**
     * 回复  删除
     *
     * @param model
     * @param replyId 回复Id
     */
    @ResponseBody
    @RequestMapping(params = "method=deleteReply", method = RequestMethod.POST)
    public String deleteReply(ModelMap model, Long[] replyId,
                              HttpServletRequest request, HttpServletResponse response) throws Exception {
        return commentManageService.deleteReply(model, replyId, request, response);
    }

    /**
     * 回复  恢复
     *
     * @param model
     * @param replyId 回复Id
     */
    @ResponseBody
    @RequestMapping(params = "method=recoveryReply", method = RequestMethod.POST)
    public String recoveryReply(ModelMap model, Long replyId,
                                HttpServletRequest request, HttpServletResponse response) throws Exception {
        return commentManageService.recoveryReply(model, replyId, request, response);
    }


    /**
     * 审核回复
     *
     * @param model
     * @param replyId 回复Id
     * @return
     * @throws Exception
     */
    @ResponseBody//方式来做ajax,直接返回字符串
    @RequestMapping(params = "method=auditReply", method = RequestMethod.POST)
    public String auditReply(ModelMap model, Long replyId,
                             HttpServletResponse response) throws Exception {
        Map<String, String> error = new HashMap<String, String>();
        if (replyId != null && replyId > 0L) {
            int i = commentService.updateReplyStatus(replyId, 20);

            Reply reply = commentManage.query_cache_findReplyByReplyId(replyId);
            if (reply != null) {
                User user = userManage.query_cache_findUserByUserName(reply.getUserName());
                if (i > 0 && user != null) {
                    //修改回复状态
                    userService.updateUserDynamicReplyStatus(user.getId(), reply.getUserName(), reply.getTopicId(), reply.getCommentId(), reply.getId(), 20);
                }
            }

            //删除缓存
            commentManage.delete_cache_findReplyByReplyId(replyId);
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        } else {
            error.put("replyId", "回复Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }


}
