package cms.web.action.like;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 话题点赞配置
 */
@Component("replyLikeConfig")
public class ReplyLikeConfig {
    @Resource
    LikeManage likeManage;

    /**
     * 分表数量
     **/
    @Value("${bbs.sharding.replyLikeConfig_tableQuantity}")
    private Integer tableQuantity = 1;

    public Integer getTableQuantity() {
        return tableQuantity;
    }

    /**
     * 根据话题评论点赞Id查询分配到表编号
     * 根据话题评论点赞Id和话题点赞分表数量求余
     *
     * @param commentLikeId 话题评论评论点赞Id
     *                    注意：话题点赞Id要先判断最后4位是不是数字
     *                    likeManage.verificationTopicLikeId(?)
     * @return
     */
    public Integer replyLikeIdRemainder(String commentLikeId) {
        int topicId = likeManage.getCommonLikeId(commentLikeId);
        return topicId % this.getTableQuantity();
    }

    /**
     * 根据话题Id查询分配到表编号
     * 根据话题Id和话题点赞分表数量求余(用户Id后四位)
     *
     * @param commentId 话题Id
     * @return
     */
    public Integer replyIdRemainder(Long commentId) {
        //选取得后N位话题Id
        String afterTopicId = String.format("%04d", commentId % 10000);
        return Integer.parseInt(afterTopicId) % this.getTableQuantity();
    }

}
