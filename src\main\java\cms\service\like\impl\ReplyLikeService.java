package cms.service.like.impl;

import cms.bean.like.ReplyLike;
import cms.service.besa.DaoSupport;
import cms.web.action.like.ReplyLikeConfig;
import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.beans.BeanCopier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.Query;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/20 15:25
 */
@Service
@Slf4j
public class ReplyLikeService extends DaoSupport<ReplyLike> {

    @Autowired
    private ReplyLikeConfig replyLikeConfig;

    public Optional<ReplyLike> getById(String id) {
        //表编号
        int tableNumber = replyLikeConfig.replyLikeIdRemainder(id);
        if (tableNumber == 0) {//默认对象
            Query query = em.createQuery("select o from ReplyLike o where o.id=?1")
                    .setParameter(1, id);
            return Optional.ofNullable(query.getResultList()).filter(CollUtil::isNotEmpty).map(l -> (ReplyLike) l.get(0));
        } else {//带下划线对象
            Query query = em.createQuery("select o from ReplyLike_" + tableNumber + " o where o.id=?1")
                    .setParameter(1, id);

            try {
                //带下划线对象
                Class<?> c = Class.forName("cms.bean.like.ReplyLike_" + tableNumber);
                Object object = c.newInstance();
                BeanCopier copier = BeanCopier.create(object.getClass(), ReplyLike.class, false);

                return Optional.ofNullable(query.getResultList()).filter(CollUtil::isNotEmpty).map(l -> {
                    Object obj = l.get(0);
                    ReplyLike replyLike = new ReplyLike();
                    copier.copy(obj, replyLike, null);
                    return replyLike;
                });
            } catch (Exception e) {
                log.error("根据Id查询话题评论点赞", e);
            }
        }
        return Optional.empty();
    }

    public Object createReplyLikeObject(ReplyLike replyLike) {
        //表编号
        int tableNumber = replyLikeConfig.replyLikeIdRemainder(replyLike.getId());
        if (tableNumber == 0) {//默认对象
            return replyLike;
        } else {//带下划线对象
            Class<?> c;
            try {
                c = Class.forName("cms.bean.like.ReplyLike_" + tableNumber);
                Object object = c.newInstance();

                BeanCopier copier = BeanCopier.create(ReplyLike.class, object.getClass(), false);

                copier.copy(replyLike, object, null);
                return object;
            } catch (ClassNotFoundException e) {
                log.error("生成话题评论点赞对象", e);
            } catch (InstantiationException e) {
                log.error("生成话题评论点赞对象", e);
            } catch (IllegalAccessException e) {
                log.error("生成话题评论点赞对象", e);
            }
        }
        return null;
    }
}
