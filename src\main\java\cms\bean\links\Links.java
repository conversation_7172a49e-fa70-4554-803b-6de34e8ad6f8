package cms.bean.links;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 友情链接
 */
@Entity
public class Links implements Serializable {
    private static final long serialVersionUID = -7640610316706427936L;

    /**
     * ID
     **/
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 名称
     **/
    @Column(length = 190)
    private String name;
    /**
     * 网址
     **/
    @Column(length = 250)
    private String website;
    /**
     * 排序
     **/
    private Integer sort = 0;
    /**
     * 图片
     **/
    @Column(length = 100)
    private String image;
    /**
     * 创建时间
     **/
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate = new Date();

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }


}
