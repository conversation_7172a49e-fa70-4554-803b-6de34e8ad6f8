package cms.bean;

import cms.listeners.BaseUserDelListeners;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/9 10:45
 */
@MappedSuperclass
@Data
@EntityListeners(BaseUserDelListeners.class)
public class BaseUserDel implements Serializable {

    private static final long serialVersionUID = -3L;

    @Id
    @Column
    private Long id;

    /**
     * 创建人
     */
    @Column(length = 36, nullable = false)
    private String createUser;

    /**
     * 创建人
     */
    @Column(length = 100, nullable = false)
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    private Date createTime;

    /**
     * 修改人
     */
    @Column(length = 36, nullable = false)
    private String updateUser;

    /**
     * 修改人
     */
    @Column(length = 100, nullable = false)
    private String updateUserName;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    private Date updateTime;

    /**
     * 删除标志
     */
    @Column(nullable = false)
    private Boolean deleted;
}
