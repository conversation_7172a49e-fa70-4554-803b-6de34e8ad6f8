package cms.bean.user;

import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 用户动态
 */
@Entity
@Table(name = "userdynamic_0", indexes = {@Index(name = "userDynamic_1_idx", columnList = "userName,status,postTime"), @Index(name = "userDynamic_5_idx", columnList = "functionIdGroup,userName,module")})
public class UserDynamic extends UserDynamicEntity implements Serializable {

    private static final long serialVersionUID = 8676510375787469569L;

    @Transient
    private List<String> userRoleNameList = new ArrayList<>();

    public List<String> getUserRoleNameList() {
        return userRoleNameList;
    }

    public void setUserRoleNameList(List<String> userRoleNameList) {
        this.userRoleNameList = userRoleNameList;
    }
}
