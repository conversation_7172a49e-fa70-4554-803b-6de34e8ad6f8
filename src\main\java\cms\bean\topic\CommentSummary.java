package cms.bean.topic;

import lombok.Data;

import javax.persistence.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/19 11:15
 */
@Entity
@Table(name = "commentSummary", indexes = {@Index(name = "idx_commentId", columnList = "commentId")})
@Data
public class CommentSummary {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long commentId;

    private Integer totalLikeNum;
}
