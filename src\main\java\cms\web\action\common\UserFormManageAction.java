package cms.web.action.common;


import cms.bean.ErrorView;
import cms.bean.captcha.Captcha;
import cms.bean.setting.AllowRegisterAccount;
import cms.bean.setting.SystemSetting;
import cms.bean.thirdParty.WeiXinOpenId;
import cms.bean.user.*;
import cms.constant.ErrorCode;
import cms.constraints.validateGroups.ForgetPassword;
import cms.constraints.validateGroups.UserRegister;
import cms.handle.CustomException;
import cms.requset.bean.SendCaptcha;
import cms.requset.bean.UserLogin;
import cms.service.messageSource.ErrorMessageService;
import cms.service.setting.SettingService;
import cms.service.template.TemplateService;
import cms.service.user.UserCustomService;
import cms.service.user.UserGradeService;
import cms.service.user.UserService;
import cms.service.user.impl.RegisterUserService;
import cms.service.user.impl.TopDonUserService;
import cms.service.user.impl.UserTopDonService;
import cms.utils.*;
import cms.utils.Base64;
import cms.utils.threadLocal.AccessUserThreadLocal;
import cms.web.action.AccessSourceDeviceManage;
import cms.web.action.CSRFTokenManage;
import cms.web.action.fileSystem.FileManage;
import cms.web.action.membershipCard.MembershipCardGiftTaskManage;
import cms.web.action.setting.SettingManage;
import cms.web.action.sms.SmsManage;
import cms.web.action.thirdParty.ThirdPartyManage;
import cms.web.action.user.UserLoginLogManage;
import cms.web.action.user.UserManage;
import cms.web.taglib.Configuration;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static cms.constant.ErrorCode.C_2_0004_0021;
import static cms.requset.bean.UserLogin.TYPE_ACCOUNT;


/**
 * 用户登录/登出管理
 */
@Controller
@Slf4j
public class UserFormManageAction {
    @Resource
    TemplateService templateService;
    @Resource
    UserService userService;
    @Resource
    UserGradeService userGradeService;
    @Resource
    UserLoginLogManage userLoginLogManage;
    @Resource
    SettingService settingService;
    @Resource
    CaptchaManage captchaManage;
    @Resource
    SettingManage settingManage;
    @Resource
    AccessSourceDeviceManage accessSourceDeviceManage;
    @Resource
    UserManage userManage;
    @Resource
    CSRFTokenManage csrfTokenManage;
    @Resource
    OAuthManage oAuthManage;
    @Resource
    ThirdPartyManage thirdPartyManage;
    @Resource
    FileManage fileManage;
    @Resource
    SmsManage smsManage;
    @Resource
    MembershipCardGiftTaskManage membershipCardGiftTaskManage;
    @Resource(name = "userCustomServiceBean")
    private UserCustomService userCustomService;
    @Autowired
    private TopDonUserService topDonUserService;
    @Autowired
    private UserTopDonService userTopDonService;
    @Autowired
    private ErrorMessageService errorMessageService;
    @Autowired
    private RegisterUserService registerUserService;
    @Value("${topdon.text.skip.enable:false}")
    public Boolean skipEnable;
    @Value("${topdon.text.skip.title}")
    public String title;
    //?  匹配任何单字符
    //*  匹配0或者任意数量的字符
    //** 匹配0或者更多的目录
    private PathMatcher matcher = new AntPathMatcher();


    /**
     * 会员注册页面显示
     *
     * @param jumpUrl  跳转URL
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/register", method = RequestMethod.GET)
    public String registerUI(ModelMap model, String jumpUrl,
                             HttpServletRequest request, HttpServletResponse response)
            throws Exception {

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据
        Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

        //判断是否错误回显
        boolean errorDisplay = false;
        if (model != null && model.get("error") != null) {
            errorDisplay = true;
        }

        if (jumpUrl == null || "".equals(jumpUrl.trim()) && isAjax == false) {
            String referer = request.getHeader("referer");
            if (referer != null && !"".equals(referer.trim())) {

                //分离URL
                //移除开始部分的相同的字符,不区分大小写
                String uri = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));


                //是否为默认注册页面
                boolean isRegister = false;//false:不是

                if (uri != null && !"".equals(uri.trim())) {
                    //截取到等于第二个参数的字符串为止,从左往右
                    String before_a = StringUtils.substringBefore(uri.trim(), "?");
                    //截取到等于第二个参数的字符串为止,从左往右
                    String before_b = StringUtils.substringBefore(uri.trim(), ".");
                    //截取到等于第二个参数的字符串为止,从左往右
                    String before_c = StringUtils.substringBefore(uri.trim(), "/");

                    if (before_a.equalsIgnoreCase("register")) {
                        isRegister = true;//标记为默认登录页面
                    }
                    if (before_b.equalsIgnoreCase("register")) {
                        isRegister = true;
                    }
                    if (before_c.equalsIgnoreCase("register")) {
                        isRegister = true;
                    }
                    if (before_a.equalsIgnoreCase("login")) {
                        isRegister = true;
                    }
                    if (before_b.equalsIgnoreCase("login")) {
                        isRegister = true;
                    }
                    if (before_c.equalsIgnoreCase("login")) {
                        isRegister = true;
                    }
                } else {
                    //如果没有参数,则跳到首页
                    uri = "index";
                }
                if (isRegister == false) {//如果来源不是登录页面，则写入跳转参数
                    String encodedRedirectURL = response.encodeRedirectURL("register?jumpUrl=" + Base64.encodeBase64URL(uri));
                    response.sendRedirect(encodedRedirectURL);

                    return null;
                }
            }
        }

        List<UserCustom> userCustomList = userCustomService.findAllUserCustom_cache();
        if (userCustomList != null && userCustomList.size() > 0) {
            Iterator<UserCustom> it = userCustomList.iterator();
            while (it.hasNext()) {
                UserCustom userCustom = it.next();
                if (userCustom.isVisible() == false) {//如果不显示
                    it.remove();
                    continue;
                }
                if (userCustom.getValue() != null && !"".equals(userCustom.getValue().trim())) {
                    LinkedHashMap<String, String> itemValue = JsonUtils.toGenericObject(userCustom.getValue(), new TypeReference<LinkedHashMap<String, String>>() {
                    });
                    userCustom.setItemValue(itemValue);
                }

            }
        }

        if (errorDisplay == false) {//如果不是错误回显
            model.addAttribute("userCustomList", userCustomList);
        }
        returnValue.put("userCustomList", userCustomList);


        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.isRegisterCaptcha()) {//如果注册需要验证码
            String captchaKey = UUIDUtil.getUUID32();
            model.addAttribute("captchaKey", captchaKey);
            returnValue.put("captchaKey", captchaKey);
        }

        //允许注册账号类型
        if (systemSetting.getAllowRegisterAccount() != null && !"".equals(systemSetting.getAllowRegisterAccount().trim())) {
            AllowRegisterAccount allowRegisterAccount = JsonUtils.toObject(systemSetting.getAllowRegisterAccount(), AllowRegisterAccount.class);
            if (allowRegisterAccount != null) {
                model.addAttribute("allowRegisterAccount", allowRegisterAccount);
                returnValue.put("allowRegisterAccount", allowRegisterAccount);
            }
        }


        if (isAjax == true) {
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {
            String dirName = templateService.findTemplateDir_cache();
            return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/register";
        }
    }

    /**
     * 会员注册
     *
     * @param model
     * @param formbean
     * @param captchaKey       验证Key
     * @param captchaValue     验证码
     * @param thirdPartyOpenId 第三方用户获取唯一标识  例如微信公众号openid
     * @param smsCode          短信验证码
     * @param jumpUrl          跳转URL
     * @param token            令牌
     * @param redirectAttrs
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/register", method = RequestMethod.POST)
    public String register(ModelMap model, @Validated(UserRegister.class) User formbean,
                           String captchaKey, String captchaValue, String thirdPartyOpenId, String smsCode,
                           String jumpUrl, String token, RedirectAttributes redirectAttrs,
                           HttpServletRequest request, HttpServletResponse response)
            throws Exception {

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据
        //用户自定义注册功能项参数
        List<UserCustom> userCustomList = userCustomService.findAllUserCustom_cache();
        try {
            return register(model, formbean, userCustomList, captchaKey, captchaValue, thirdPartyOpenId, smsCode, jumpUrl, token, isAjax, redirectAttrs, request, response);
        } catch (CustomException e) {
            Map<String, String> returnError = new HashMap<String, String>();//错误
            returnError.put(e.getFiledName(), errorMessageService.customExceptionMsg(e));
            if (isAjax == true) {
                Map<String, Object> returnValue = new HashMap<String, Object>();//返回值
                returnValue.put("success", "false");
                returnValue.put("error", returnError);
                WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
                return null;
            } else {
                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addFlashAttribute("user", formbean);
                redirectAttrs.addFlashAttribute("userCustomList", userCustomList);

                String referer = request.getHeader("referer");

                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";
                return "redirect:/" + referer + queryString;
            }
        }
    }

    public String register(ModelMap model, User formbean, List<UserCustom> userCustomList,
                           String captchaKey, String captchaValue, String thirdPartyOpenId, String smsCode,
                           String jumpUrl, String token, boolean isAjax, RedirectAttributes redirectAttrs,
                           HttpServletRequest request, HttpServletResponse response) throws IOException {
        User user = new User();
        user.setEmailRemind(Optional.ofNullable(formbean.getEmailRemind()).orElse(Boolean.TRUE));
        if (Strings.isEmpty(user.getNickname())) {
            while (true) {
                user.setNickname(String.format("%s_%s", User.DEFAULT_NICKNAME_TITLE, UUIDUtil.getUUID22().substring(0, 12)));
                if (null == userService.findUserByNickname(user.getNickname())) {
                    break;
                }
            }
        } else {
            if (null != userService.findUserByNickname(user.getNickname())) {
                throw new CustomException(ErrorCode.C_2_2001_0030, "register");
            }
        }

        String dirName = templateService.findTemplateDir_cache();
        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        Optional.ofNullable(systemSetting)
                .map(SystemSetting::getCloseSite)
                .filter(o -> !o.equals(2))
                .orElseThrow(() -> new CustomException(ErrorCode.C_2_0001_0001, "user"));

        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);

        boolean isCaptcha = false;
        //读取允许注册账号类型
        AllowRegisterAccount allowRegisterAccount = settingManage.readAllowRegisterAccount();
        Optional.ofNullable(allowRegisterAccount)
                .filter(o -> o.isLocal() || o.isMobile())
                .orElseThrow(() -> new CustomException(ErrorCode.C_2_0004_0001, "register"));
        Integer type = Optional.ofNullable(formbean.getType())
                .orElseThrow(() -> new CustomException(ErrorCode.C_2_0004_0018, "type"));
        if (type.equals(10) && allowRegisterAccount.isLocal()) {//10:本地账号密码用户
            registerUserService.localUserRegister(formbean, user);
        } else if (type.equals(20) && allowRegisterAccount.isMobile()) {//20: 手机用户
            registerUserService.phoneUserRegister(formbean, user, smsCode);
        }

        //盐值
        user.setSalt(UUIDUtil.getUUID32());
        user.setPassword(formbean.getPassword().trim());
        user.setRegistrationDate(new Date());
        // 用户自定义注册功能项参数
        registerUserService.userCustom(userCustomList, request);
        //访问令牌
        String accessToken = UUIDUtil.getUUID32();
        //刷新令牌
        String refreshToken = UUIDUtil.getUUID32();
        //用户类型
        user.setType(type);

        //用户自定义注册功能项用户输入值集合
        List<UserInputValue> all_userInputValueList = Optional.ofNullable(userCustomList).filter(CollectionUtil::isNotEmpty)
                .map(l -> l.stream().map(UserCustom::getUserInputValueList).filter(CollectionUtil::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList())).orElse(Lists.newArrayList());
        user.setSecurityDigest(new Date().getTime());
        user.setValidateCode(captchaValue);
        topDonUserService.register(user);
        try {
            userService.saveUser(user, all_userInputValueList, null);
        } catch (Exception e) {
            log.error("saveUser error!", e);
            throw new CustomException(ErrorCode.C_2_0004_0020, "register");
        }
        //自动登录
        //写入登录日志
        UserLoginLog userLoginLog = new UserLoginLog();
        userLoginLog.setId(userLoginLogManage.createUserLoginLogId(user.getId()));
        userLoginLog.setIp(IpAddress.getClientIpAddress(request));
        userLoginLog.setUserId(user.getId());
        userLoginLog.setTypeNumber(10);//登录
        userLoginLog.setLogonTime(new Date());
        Object new_userLoginLog = userLoginLogManage.createUserLoginLogObject(userLoginLog);
        userService.saveUserLoginLog(new_userLoginLog);

        //自动登录
        String openId = "";//第三方openId
        if (thirdPartyOpenId != null && !"".equals(thirdPartyOpenId.trim())) {
            openId = thirdPartyOpenId;
            oAuthManage.addOpenId(openId, refreshToken);
        }

        oAuthManage.addAccessToken(accessToken, new AccessUser(user.getId(), user.getUserName(), user.getAccount(), user.getNickname(), user.getAvatarPath(fileManage.fileOssAddress()), user.getAvatarName(), user.getSecurityDigest(), false, openId));
        oAuthManage.addRefreshToken(refreshToken, new RefreshUser(accessToken, user.getId(), user.getUserName(), user.getAccount(), user.getNickname(), user.getAvatarPath(fileManage.fileOssAddress()), user.getAvatarName(), user.getSecurityDigest(), false, openId));

        //将访问令牌添加到Cookie
        WebUtil.addCookie(response, "cms_accessToken", accessToken, 0);
        //将刷新令牌添加到Cookie
        WebUtil.addCookie(response, "cms_refreshToken", refreshToken, 0);
        AccessUserThreadLocal.set(new AccessUser(user.getId(), user.getUserName(), user.getAccount(), user.getNickname(), user.getAvatarPath(fileManage.fileOssAddress()), user.getAvatarName(), user.getSecurityDigest(), false, openId));

        //删除缓存
        userManage.delete_cache_findUserById(user.getId());
        userManage.delete_cache_findUserByUserName(user.getUserName());
        userManage.delete_cache_findUserByUserName();

        if (isAjax == true) {
            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值
            //跳转URL
            String _jumpUrl = "";
            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                //Base64解码后参数进行URL编码
                String parameter = WebUtil.parameterEncoded(Base64.decodeBase64URL(jumpUrl.trim()));

                String encodedRedirectURL = response.encodeRedirectURL(parameter);
                _jumpUrl = encodedRedirectURL;
            } else {
                _jumpUrl = "index";
            }
            if ("login".equalsIgnoreCase(_jumpUrl)) {
                _jumpUrl = "index";
            }

            returnValue.put("success", "true");
            returnValue.put("jumpUrl", _jumpUrl);
            returnValue.put("systemUser", new AccessUser(user.getId(), user.getUserName(), user.getAccount(), user.getNickname(), user.getAvatarPath(fileManage.fileOssAddress()), user.getAvatarName(), null, false, ""));//登录用户
            returnValue.put("accessToken", accessToken);
            returnValue.put("refreshToken", refreshToken);

            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {
            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());
                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "注册成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                if (RefererCompare.compare(request, "register")) {//如果是注册页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);

                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }


    /**
     * 会员注册校验/验证码校验
     *
     * @param account  账号
     * @param mobile   手机号
     * @param response
     * @return true 禁止  false 允许
     * @throws Exception
     */
    @RequestMapping(value = "/userVerification", method = RequestMethod.GET)
    @ResponseBody//方式来做ajax,直接返回字符串
    public String verification(ModelMap model, String account, String mobile, String captchaKey, String captchaValue,
                               HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        if (account != null && !"".equals(account.trim())) {
            List<DisableUserName> disableUserNameList = userService.findAllDisableUserName_cache();
            if (disableUserNameList != null && disableUserNameList.size() > 0) {
                for (DisableUserName disableUserName : disableUserNameList) {
                    boolean flag = matcher.match(disableUserName.getName(), account.trim());  //参数一: ant匹配风格   参数二:输入URL
                    if (flag) {
                        return "true";
                    }
                }
            }

            User u = userService.findUserByAccount(account.trim());
            if (u != null) {
                return "true";
            }
        }
        if (mobile != null && !"".equals(mobile.trim())) {

            String platformUserId = userManage.thirdPartyUserIdToPlatformUserId(mobile.trim(), 20);
            User mobile_user = userService.findUserByPlatformUserId(platformUserId);

            if (mobile_user != null) {
                return "true";
            }

        }
        if (captchaKey != null && !"".equals(captchaKey.trim()) && captchaValue != null && !"".equals(captchaValue.trim())) {
            //增加验证码重试次数
            //统计每分钟原来提交次数
            Integer original = settingManage.getSubmitQuantity("captcha", captchaKey.trim());
            if (original != null) {
                settingManage.addSubmitQuantity("captcha", captchaKey.trim(), original + 1);//刷新每分钟原来提交次数
            } else {
                settingManage.addSubmitQuantity("captcha", captchaKey.trim(), 1);//刷新每分钟原来提交次数
            }

            String _captcha = captchaManage.captcha_generate(captchaKey.trim(), "");
            if (_captcha != null && _captcha.equalsIgnoreCase(captchaValue)) {
                return "true";
            }
        }


        return "false";
    }


    /**
     * 会员登录页面显示
     *
     * @param jumpUrl  跳转URL
     * @param code     微信公众号code
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/login", method = RequestMethod.GET)
    public String loginUI(ModelMap model, String jumpUrl, String code,
                          HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据

        //处理微信浏览器被清理缓存后公众号自动登录(本段代码功能已迁移到recoverWeChatBrowserSession方法，因需兼容前后端一体模板，本段代码暂时保留)
        if (code != null && !"".equals(code.trim()) && WebUtil.isWeChatBrowser(request)) {//如果是微信客户端

            WeiXinOpenId weiXinOpenId = thirdPartyManage.queryWeiXinOpenId(code.trim());
            if (weiXinOpenId != null && weiXinOpenId.getOpenId() != null && !"".equals(weiXinOpenId.getOpenId())) {

                //添加到缓存
                thirdPartyManage.addWeiXinOpenId(code.trim(), weiXinOpenId);


                //刷新令牌号
                String refreshToken = oAuthManage.getRefreshTokenByOpenId(weiXinOpenId.getOpenId());
                if (refreshToken != null && !"".equals(refreshToken.trim())) {


                    RefreshUser refreshUser = oAuthManage.getRefreshUserByRefreshToken(refreshToken.trim());
                    if (refreshUser != null) {

                        //存放时间 单位/秒
                        int maxAge = 0;
                        if (refreshUser.isRememberMe()) {
                            maxAge = WebUtil.cookieMaxAge;//默认Cookie有效期
                        }
                        //将令牌写入Cookie

                        //将访问令牌添加到Cookie
                        WebUtil.addCookie(response, "cms_accessToken", refreshUser.getAccessToken(), maxAge);
                        //将刷新令牌添加到Cookie
                        WebUtil.addCookie(response, "cms_refreshToken", refreshToken, maxAge);


                        if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                            //Base64解码后参数进行URL编码
                            String parameter = WebUtil.parameterEncoded(Base64.decodeBase64URL(jumpUrl));

                            String encodedRedirectURL = response.encodeRedirectURL(parameter);
                            response.sendRedirect((Configuration.getPath() != null && !"".equals(Configuration.getPath()) ? Configuration.getPath() + "/" : "") + encodedRedirectURL);
                            return null;

                        }


                    }

                }

            }
        }

        FormCaptcha formCaptcha = new FormCaptcha();
        boolean isCaptcha = false;
        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getLogin_submitQuantity() <= 0) {//每分钟连续登录密码错误N次时出现验证码
            isCaptcha = true;
        } else {
            String account = WebUtil.getCookieByName(request.getCookies(), "cms_account");
            if (account != null && !"".equals(account.trim())) {
                //是否需要验证码  true:要  false:不要
                isCaptcha = captchaManage.login_isCaptcha(account);
            }
        }

        if (isCaptcha) {
            formCaptcha.setShowCaptcha(true);
            formCaptcha.setCaptchaKey(UUIDUtil.getUUID32());
        }


        if (isAjax) {
            if (jumpUrl == null) {
                jumpUrl = "";
            }

            //	response.setHeader("login", "login?jumpUrl="+jumpUrl);//设置登录页面响应http头。用来激活Ajax请求处理方式 Session超时后的跳转
            //	response.setHeader("login", Configuration.getUrl(request)+"login?jumpUrl="+jumpUrl);//设置登录页面响应http头。用来激活Ajax请求处理方式 Session超时后的跳转

            WebUtil.writeToWeb(JsonUtils.toJSONString(formCaptcha), "json", response);

            return null;
        } else {
            model.addAttribute("formCaptcha", formCaptcha);
            if (jumpUrl == null || "".equals(jumpUrl.trim())) {
                String referer = request.getHeader("referer");
                if (referer != null && !"".equals(referer.trim())) {
                    //分离URL
                    //移除开始部分的相同的字符,不区分大小写
                    String uri = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));
                    //是否为默认登录页面
                    boolean isLogin = false;//false:不是

                    if (uri != null && !"".equals(uri.trim())) {
                        //截取到等于第二个参数的字符串为止,从左往右
                        String before_a = StringUtils.substringBefore(uri.trim(), "?");
                        //截取到等于第二个参数的字符串为止,从左往右
                        String before_b = StringUtils.substringBefore(uri.trim(), ".");
                        //截取到等于第二个参数的字符串为止,从左往右
                        String before_c = StringUtils.substringBefore(uri.trim(), "/");

                        if (before_a.equalsIgnoreCase("login")) {
                            isLogin = true;//标记为默认登录页面
                        }
                        if (before_b.equalsIgnoreCase("login")) {
                            isLogin = true;
                        }
                        if (before_c.equalsIgnoreCase("login")) {
                            isLogin = true;
                        }
                    } else {
                        //如果没有参数,则跳到首页
                        uri = "index";
                    }
                    if (isLogin == false) {//如果来源不是登录页面，则写入跳转参数

                        String encodedRedirectURL = response.encodeRedirectURL("login?jumpUrl=" + Base64.encodeBase64URL(uri));

                        response.sendRedirect((Configuration.getPath() != null && !"".equals(Configuration.getPath()) ? Configuration.getPath() + "/" : "") + encodedRedirectURL);
                        return null;
                    }
                }
            }

            String dirName = templateService.findTemplateDir_cache();

            return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/login";
        }
    }

    @ResponseBody
    @RequestMapping(value = "/sendCaptcha", method = RequestMethod.POST)
    public Captcha sendCaptcha(SendCaptcha sendCaptcha) {
        return topDonUserService.sendCaptcha(sendCaptcha);
    }

    /**
     * 会员登录
     *
     * @param model
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    @Transactional(propagation = Propagation.REQUIRED)
    public String login(ModelMap model, @Validated UserLogin userLogin,
                        RedirectAttributes redirectAttrs,
                        HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        //登录标记
        String loginAccount = null;
        boolean isCaptcha = false;
        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据
        Optional<String> platformUserId = Optional.empty();
        try {
            platformUserId = Optional.ofNullable(userLogin.getMobile()).map(String::trim).filter(Strings::isNotEmpty).map(o -> userManage.thirdPartyUserIdToPlatformUserId(o, 20));
            Optional<String> finalPlatformUserId = platformUserId;
            loginAccount = Optional.of(userLogin.getType()).filter(TYPE_ACCOUNT::equals).map(o -> userLogin.getAccount())
                    .orElseGet(() -> finalPlatformUserId.orElse(null));
            isCaptcha = Optional.of(userLogin.getType()).filter(TYPE_ACCOUNT::equals).map(o -> captchaManage.login_isCaptcha(userLogin.getAccount()))
                    .orElseGet(() -> finalPlatformUserId.map(captchaManage::login_isCaptcha).orElseThrow(() -> new CustomException(ErrorCode.C_1_0004_0006, "type")));

            String url = this.login(userLogin, redirectAttrs, platformUserId, loginAccount, isCaptcha, isAjax, request, response);
            //默认跳转
            return url;
        } catch (CustomException e) {
            log.error("login error!", e);
            //统计每分钟原来提交次数
            Integer original = settingManage.getSubmitQuantity("login", loginAccount);
            if (original != null) {
                settingManage.addSubmitQuantity("login", loginAccount, original + 1);//刷新每分钟原来提交次数
            } else {
                settingManage.addSubmitQuantity("login", loginAccount, 1);//刷新每分钟原来提交次数
            }//添加用户名到Cookie
            WebUtil.addCookie(response, "cms_account", loginAccount, 60);

            Map<String, String> returnError = new HashMap<String, String>();//错误
            returnError.put(e.getFiledName(), errorMessageService.customExceptionMsg(e));
            if (isAjax) {//Ajax方式返回数据
                Map<String, Object> ajax_return = new HashMap<String, Object>();//返回
                ajax_return.put("success", "false");
                ajax_return.put("error", returnError);

                //重新判断是否需要验证码
                if (userLogin.getType() != null) {
                    if (userLogin.getType().equals(10)) {//10:本地账号密码用户
                        if (userLogin.getAccount() != null && !"".equals(userLogin.getAccount().trim())) {
                            isCaptcha = captchaManage.login_isCaptcha(userLogin.getAccount());
                        }
                    } else if (userLogin.getType().equals(20)) {//20: 手机用户
                        if (userLogin.getMobile() != null && !"".equals(userLogin.getMobile().trim())) {
                            isCaptcha = captchaManage.login_isCaptcha(platformUserId.get());
                        }
                    }
                }
                if (isCaptcha) {
                    ajax_return.put("captchaKey", UUIDUtil.getUUID32());
                }
                WebUtil.writeToWeb(JsonUtils.toJSONString(ajax_return), "json", response);
                return null;
            } else {
                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addAttribute("jumpUrl", userLogin.getJumpUrl());
                return "redirect:/login" + (userLogin.getJumpUrl() != null && !"".equals(userLogin.getJumpUrl().trim()) ? "?jumpUrl={jumpUrl}" : "");
            }
        }
    }

    private String login(UserLogin userLogin, RedirectAttributes redirectAttrs,
                         Optional<String> platformUserId, String loginAccount, boolean isCaptcha, boolean isAjax,
                         HttpServletRequest request, HttpServletResponse response) throws IOException {
        Map<String, String> error = Maps.newHashMap();
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, userLogin.getToken());
        userLogin.setRememberMe(Optional.ofNullable(userLogin.getRememberMe()).orElse(Boolean.FALSE));

        TopDonLoginResult topDonLoginResult = Optional.ofNullable(skipEnable)
                .filter(o -> o)
                .filter(o -> Strings.isNotEmpty(title) && userLogin.getAccount().startsWith(title))
                .map(o -> new TopDonLoginResult(-1))
                .orElseGet(() -> {
                    if (userLogin.isCode()) {
                        TopDonLoginResult topDonLoginResult1 = topDonUserService.loginByCode(userLogin.getAccount(), userLogin.getPassword());
                        if (userService.findUserByAccount(userLogin.getAccount()) == null) {
                            SpringUtils.getBean(UserFormManageAction.class).createUser(userLogin, topDonLoginResult1);
                        }
                        return topDonLoginResult1;
                    } else {
                        return topDonUserService.login(userLogin.getAccount(), userLogin.getPassword());
                    }
                });

        //访问令牌
        String accessToken = UUIDUtil.getUUID32();
        //刷新令牌
        String refreshToken = UUIDUtil.getUUID32();

        User user = Optional.of(userLogin.getType())
                .filter(TYPE_ACCOUNT::equals)
                .map(o -> userService.findUserByAccount(userLogin.getAccount()))
                .orElseGet(() -> platformUserId.map(userService::findUserByPlatformUserId).orElseGet(() -> {
                    userManage.delete_cache_findUserByUserName();
                    return userTopDonService.addByLogin(userLogin, topDonLoginResult);
                }));
        List<UserGrade> userGradeList = userGradeService.findAllGrade_cache();
        if (userGradeList != null && !userGradeList.isEmpty()) {
            for (UserGrade userGrade : userGradeList) {//取得所有等级
                if (user.getPoint() >= userGrade.getNeedPoint()) {
                    user.setGradeId(userGrade.getId());
                    user.setGradeName(userGrade.getName());//将等级值设进等级参数里
                    break;
                }
            }
        }

        //用户不存在
        Optional.ofNullable(user.getCancelAccountTime()).filter(o -> o == -1L).orElseThrow(() -> new CustomException(ErrorCode.C_1_0004_0011, "account"));
        // 禁止账号
        Optional.ofNullable(user.getState()).filter(o -> o == 1).orElseThrow(() -> new CustomException(ErrorCode.C_1_0004_0012, "account"));

        //删除缓存用户状态
        userManage.delete_userState(user.getUserName());

        //写入登录日志
        UserLoginLog userLoginLog = new UserLoginLog();
        userLoginLog.setId(userLoginLogManage.createUserLoginLogId(user.getId()));
        userLoginLog.setIp(IpAddress.getClientIpAddress(request));
        userLoginLog.setUserId(user.getId());
        userLoginLog.setTypeNumber(10);//登录
        userLoginLog.setLogonTime(new Date());
        Object new_userLoginLog = userLoginLogManage.createUserLoginLogObject(userLoginLog);
        userService.saveUserLoginLog(new_userLoginLog);

        //第三方openId
        String openId = Optional.ofNullable(userLogin.getThirdPartyOpenId()).map(String::trim).filter(Strings::isNotEmpty).map(o -> {
            oAuthManage.addOpenId(o, refreshToken);
            return o;
        }).orElse("");

        oAuthManage.initToken(user, Optional.empty(), Optional.empty(), accessToken, refreshToken, openId, user.getAvatarPath(fileManage.fileOssAddress()), userLogin.getRememberMe());

        //存放时间 单位/秒
        int maxAge = Optional.ofNullable(userLogin.getRememberMe()).filter(o -> o).map(o -> WebUtil.cookieMaxAge).orElse(0);

        //将访问令牌添加到Cookie
        WebUtil.addCookie(response, "cms_accessToken", accessToken, maxAge);
        //将刷新令牌添加到Cookie
        WebUtil.addCookie(response, "cms_refreshToken", refreshToken, maxAge);
        AccessUserThreadLocal.set(new AccessUser(user.getId(), user.getUserName(), user.getAccount(), user.getNickname(), user.getAvatarPath(fileManage.fileOssAddress()), user.getAvatarName(), user.getSecurityDigest(), userLogin.getRememberMe(), openId));

        //删除每分钟原来提交次数
        settingManage.deleteSubmitQuantity("login", loginAccount);
        WebUtil.deleteCookie(response, "cms_account");

        Map<String, String> returnError = new HashMap<String, String>();//错误
        if (error.size() > 0) {
            //将枚举数据转为错误提示字符
            for (Map.Entry<String, String> entry : error.entrySet()) {
                if (ErrorView.get(entry.getValue()) != null) {
                    returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                } else {
                    returnError.put(entry.getKey(), entry.getValue());
                }

            }
        }

        //跳转URL
        String _jumpUrl = Optional.ofNullable(userLogin.getJumpUrl()).map(String::trim).filter(Strings::isNotEmpty).map(o -> WebUtil.parameterEncoded(Base64.decodeBase64URL(o))).map(response::encodeRedirectURL)
                .map(o -> "login".equalsIgnoreCase(o) ? "index" : o).orElseGet(() -> "index");

        if (isAjax) {//Ajax方式返回数据
            Map<String, Object> ajax_return = new HashMap<String, Object>();//返回
            if (error != null && error.size() > 0) {
                ajax_return.put("success", "false");
                ajax_return.put("error", returnError);

                //重新判断是否需要验证码
                if (userLogin.getType() != null) {
                    if (userLogin.getType().equals(10)) {//10:本地账号密码用户
                        if (userLogin.getAccount() != null && !"".equals(userLogin.getAccount().trim())) {
                            isCaptcha = captchaManage.login_isCaptcha(userLogin.getAccount());
                        }
                    } else if (userLogin.getType().equals(20)) {//20: 手机用户
                        if (userLogin.getMobile() != null && !"".equals(userLogin.getMobile().trim())) {
                            isCaptcha = captchaManage.login_isCaptcha(platformUserId.get());
                        }
                    }
                }
                if (isCaptcha) {
                    ajax_return.put("captchaKey", UUIDUtil.getUUID32());
                }
            } else {
                ajax_return.put("success", "true");
                ajax_return.put("jumpUrl", _jumpUrl);
                ajax_return.put("systemUser", new AccessUser(user.getId(), user.getUserName(), user.getAccount(), user.getNickname(), user.getAvatarPath(fileManage.fileOssAddress()), user.getAvatarName(), null, false, ""));//登录用户
                ajax_return.put("accessToken", accessToken);
                ajax_return.put("refreshToken", refreshToken);
            }
            WebUtil.writeToWeb(JsonUtils.toJSONString(ajax_return), "json", response);
            return null;
        } else {
            //登录失败处理
            if (error.size() > 0) {
                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addAttribute("jumpUrl", userLogin.getJumpUrl());
                return "redirect:/login" + (userLogin.getJumpUrl() != null && !"".equals(userLogin.getJumpUrl().trim()) ? "?jumpUrl={jumpUrl}" : "");
            } else {//登录成功处理

                if (userLogin.getJumpUrl() != null && !"".equals(userLogin.getJumpUrl().trim())) {
                    //Base64解码后参数进行URL编码
                    String parameter = WebUtil.parameterEncoded(Base64.decodeBase64URL(userLogin.getJumpUrl()));

                    String encodedRedirectURL = response.encodeRedirectURL(parameter);
                    response.sendRedirect((Configuration.getPath() != null && !"".equals(Configuration.getPath()) ? Configuration.getPath() + "/" : "") + encodedRedirectURL);
                    return null;
                }
            }
        }
        return "redirect:/index";
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void createUser(UserLogin userLogin, TopDonLoginResult topDonLoginResult1) {
        User user = new User();
        user.setEmailRemind(Boolean.TRUE);
        do {
            user.setNickname(String.format("%s_%s", User.DEFAULT_NICKNAME_TITLE, UUIDUtil.getUUID22().substring(0, 12)));
        } while (null != userService.findUserByNickname(user.getNickname()));

        //读取允许注册账号类型
        AllowRegisterAccount allowRegisterAccount = settingManage.readAllowRegisterAccount();
        Optional.ofNullable(allowRegisterAccount)
                .filter(o -> o.isLocal() || o.isMobile())
                .orElseThrow(() -> new CustomException(ErrorCode.C_2_0004_0001, "register"));
        Integer type = Optional.ofNullable(userLogin.getType())
                .orElseThrow(() -> new CustomException(ErrorCode.C_2_0004_0018, "type"));
        if (type.equals(10) && allowRegisterAccount.isLocal()) {//10:本地账号密码用户
            Optional.ofNullable(userService.findAllDisableUserName_cache()).filter(CollectionUtil::isNotEmpty)
                    .map(l -> l.stream().map(DisableUserName::getName).map(o -> matcher.match(o, userLogin.getAccount())).anyMatch(o -> o))
                    .filter(o -> !o).orElseThrow(() -> new CustomException(ErrorCode.C_2_0004_0019, "account"));

            user.setAccount(userLogin.getAccount().trim());
            Optional.ofNullable(userService.findUserByAccount(user.getAccount()) == null).filter(o -> o).orElseThrow(() -> new CustomException(ErrorCode.C_1_0004_0001, "account"));

            user.setIssue("Lenkor");
            user.setAnswer(SHA.sha256Hex("Lenkor"));
            Optional.of(user.getAnswer()).filter(o -> o.length() == 64).orElseThrow(() -> new CustomException(ErrorCode.C_2_0004_0016, "answer"));

            user.setEmail(userLogin.getAccount().trim());
            user.setUserName(UUIDUtil.getUUID22());
            user.setPlatformUserId(user.getUserName());
        }

        //盐值
        user.setSalt(UUIDUtil.getUUID32());
        user.setPassword(UUID.fastUUID().toString());
        user.setRegistrationDate(new Date());
        //用户类型
        user.setType(type);

        user.setSecurityDigest(new Date().getTime());
        try {
            userService.saveUser(user, null, null);
        } catch (Exception e) {
            log.error("saveUser error!", e);
            throw new CustomException(ErrorCode.C_2_0004_0020, "register");
        }
    }


    /**
     * 会员退出
     *
     * @param model
     * @param token
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/logout", method = RequestMethod.POST)
    public String logout(ModelMap model, String token,
                         HttpServletRequest request, HttpServletResponse response)
            throws Exception {

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据

        Map<String, String> error = new HashMap<String, String>();

        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token, error);

        String _refreshToken = WebUtil.getCookieByName(request, "cms_refreshToken");
        String _accessToken = WebUtil.getCookieByName(request, "cms_accessToken");

        //从Header获取
        UserAuthorization headerUserAuthorization = WebUtil.getAuthorization(request);
        if (headerUserAuthorization != null) {
            _accessToken = headerUserAuthorization.getAccessToken();
            _refreshToken = headerUserAuthorization.getRefreshToken();
        }

        if (error.size() == 0) {
            //获取登录用户
            AccessUser accessUser = AccessUserThreadLocal.get();
            if (accessUser != null) {
                userManage.delete_userState(accessUser.getUserName());
                oAuthManage.clearToken(accessUser.getUserId(), Optional.ofNullable(_refreshToken));
            }
            WebUtil.deleteCookie(response, "cms_refreshToken");
            WebUtil.deleteCookie(response, "cms_accessToken");
        }


        if (isAjax) {
            Map<String, Object> returnValue = new HashMap<String, Object>();

            if (error != null && error.size() > 0) {
                Map<String, String> returnError = new HashMap<String, String>();//错误
                if (error.size() > 0) {
                    //将枚举数据转为错误提示字符
                    for (Map.Entry<String, String> entry : error.entrySet()) {
                        if (ErrorView.get(entry.getValue()) != null) {
                            returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                        } else {
                            returnError.put(entry.getKey(), entry.getValue());
                        }

                    }
                }
                returnValue.put("success", "false");
                returnValue.put("error", returnError);
            } else {
                String jumpUrl = "login";
                returnValue.put("success", "true");
                returnValue.put("jumpUrl", jumpUrl);
            }
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {
            if (error.size() == 0) {
                //跳转到登录页
                return "redirect:/login";
            }
            return "redirect:/index";

        }
    }


    /**
     * 找回密码 第一步界面
     *
     * @param model
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/findPassWord/step1", method = RequestMethod.GET)
    public String findPassWord_step1_UI(ModelMap model,
                                        HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据

        Map<String, Object> returnValue = new HashMap<String, Object>();//返回值
        String captchaKey = UUIDUtil.getUUID32();
        model.addAttribute("captchaKey", captchaKey);
        returnValue.put("captchaKey", captchaKey);
        if (isAjax) {
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {
            String dirName = templateService.findTemplateDir_cache();
            return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/findPassWord_step1";
        }
    }


    /**
     * 找回密码 第一步
     *
     * @param model
     * @param account       账号
     * @param type          用户类型
     * @param mobile        手机号
     * @param captchaKey
     * @param captchaValue
     * @param token
     * @param redirectAttrs
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/findPassWord/step1", method = RequestMethod.POST)
    public String findPassWord_step1(ModelMap model, String account, Integer type, String mobile,
                                     String captchaKey, String captchaValue,
                                     String token, RedirectAttributes redirectAttrs,
                                     HttpServletRequest request, HttpServletResponse response)
            throws Exception {

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据

        Map<String, String> error = new HashMap<String, String>();

        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token, error);

        List<Integer> numbers = Arrays.asList(10, 20);
        if (type != null && numbers.contains(type)) {
            if (type.equals(10)) {//10:本地账号密码用户
                if (account == null || "".equals(account.trim())) {
                    //用户名不能为空
                    error.put("account", ErrorView._815.name());//账号不能为空
                } else {
                    account = account.trim();

                }
            } else if (type.equals(20)) {//20: 手机用户
                if (mobile == null || "".equals(mobile.trim())) {
                    //手机号不能为空
                    error.put("mobile", ErrorView._866.name());//手机号不能为空
                } else {
                    mobile = mobile.trim();


                }
            }
        } else {
            error.put("type", ErrorView._865.name());//用户类型不能为空
        }


        //验证验证码
        if (captchaKey != null && !"".equals(captchaKey.trim())) {
            //增加验证码重试次数
            //统计每分钟原来提交次数
            Integer original = settingManage.getSubmitQuantity("captcha", captchaKey.trim());
            if (original != null) {
                settingManage.addSubmitQuantity("captcha", captchaKey.trim(), original + 1);//刷新每分钟原来提交次数
            } else {
                settingManage.addSubmitQuantity("captcha", captchaKey.trim(), 1);//刷新每分钟原来提交次数
            }

            String _captcha = captchaManage.captcha_generate(captchaKey.trim(), "");
            if (captchaValue != null && !"".equals(captchaValue.trim())) {
                if (_captcha != null && !"".equals(_captcha.trim())) {
                    if (!_captcha.equalsIgnoreCase(captchaValue)) {
                        error.put("captchaValue", ErrorView._15.name());//验证码错误
                    }
                } else {
                    error.put("captchaValue", ErrorView._17.name());//验证码过期
                }
            } else {
                error.put("captchaValue", ErrorView._16.name());//请输入验证码
            }
            //删除验证码
            captchaManage.captcha_delete(captchaKey.trim());
        } else {
            error.put("captchaValue", ErrorView._14.name());//验证码参数错误
        }

        String userName = "";

        if (error.size() == 0) {
            if (type.equals(10)) {//10:本地账号密码用户
                User user = userService.findUserByAccount(account.trim());
                if (user == null) {
                    error.put("account", ErrorView._910.name());//用户不存在
                } else {
                    if (user.getCancelAccountTime() != -1L) {
                        error.put("account", ErrorView._859.name());//用户不存在
                    }

                    if (user.getType() != 10) {
                        error.put("account", ErrorView._920.name());//用户不是本地密码账户
                    } else {
                        userName = user.getUserName();
                    }

                }
            } else if (type.equals(20)) {//20: 手机用户
                String platformUserId = userManage.thirdPartyUserIdToPlatformUserId(mobile.trim(), 20);
                User mobile_user = userService.findUserByPlatformUserId(platformUserId);
                if (mobile_user == null) {
                    error.put("mobile", ErrorView._869.name());//手机用户不存在
                } else {
                    if (mobile_user.getCancelAccountTime() != -1L) {
                        error.put("mobile", ErrorView._859.name());//用户不存在
                    }
                    if (mobile_user.getType() != 20) {
                        error.put("mobile", ErrorView._870.name());//手机号不是手机账户
                    } else {
                        userName = mobile_user.getUserName();
                    }

                }

            }
        }


        Map<String, String> returnError = new HashMap<String, String>();//错误
        if (error.size() > 0) {
            //将枚举数据转为错误提示字符
            for (Map.Entry<String, String> entry : error.entrySet()) {
                if (ErrorView.get(entry.getValue()) != null) {
                    returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                } else {
                    returnError.put(entry.getKey(), entry.getValue());
                }

            }
        }

        if (isAjax == true) {

            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

            if (error != null && error.size() > 0) {
                returnValue.put("success", "false");
                returnValue.put("error", returnError);
                returnValue.put("captchaKey", UUIDUtil.getUUID32());
            } else {
                returnValue.put("success", "true");
                returnValue.put("jumpUrl", "findPassWord/step2" + "?userName=" + userName + "&mobile=" + mobile);
            }

            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {

            if (error != null && error.size() > 0) {//如果有错误

                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addFlashAttribute("userName", userName);
                redirectAttrs.addFlashAttribute("mobile", mobile);
                String referer = request.getHeader("referer");

                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";
                return "redirect:/" + referer + queryString;

            }

            return "redirect:/findPassWord/step2?userName=" + userName + "&mobile=" + mobile;

        }
    }


    /**
     * 找回密码 第二步界面
     *
     * @param model
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/findPassWord/step2", method = RequestMethod.GET)
    public String findPassWord_step2_UI(ModelMap model, String userName,
                                        HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据

        Map<String, Object> returnValue = new HashMap<String, Object>();//返回值


        User newUser = new User();

        Map<String, String> error = new HashMap<String, String>();
        if (userName != null && !"".equals(userName.trim())) {
            User user = userService.findUserByUserName(userName.trim());
            if (user != null) {
                if (user.getCancelAccountTime() != -1L) {
                    error.put("account", ErrorView._859.name());//用户不存在
                } else {
                    newUser.setId(user.getId());
                    newUser.setUserName(user.getUserName());
                    newUser.setAccount(user.getAccount());
                    newUser.setIssue(user.getIssue());
                    newUser.setType(user.getType());

                    model.addAttribute("user", newUser);
                    returnValue.put("user", newUser);
                }
            } else {
                error.put("account", ErrorView._910.name());//用户不存在
            }
        } else {
            error.put("account", ErrorView._815.name());//用户名称不能为空
        }

        //显示验证码
        String captchaKey = UUIDUtil.getUUID32();
        model.addAttribute("captchaKey", captchaKey);
        returnValue.put("captchaKey", captchaKey);

        if (isAjax == true) {
            Map<String, String> returnError = new HashMap<String, String>();//错误
            if (error.size() > 0) {
                //将枚举数据转为错误提示字符
                for (Map.Entry<String, String> entry : error.entrySet()) {
                    if (ErrorView.get(entry.getValue()) != null) {
                        returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                    } else {
                        returnError.put(entry.getKey(), entry.getValue());
                    }

                }
            }

            if (error != null && error.size() > 0) {
                returnValue.put("success", "false");
                returnValue.put("error", returnError);
            } else {
                returnValue.put("success", "true");
            }

            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {
            if (error.size() > 0) {
                return "redirect:/findPassWord/step1";
            }
            String dirName = templateService.findTemplateDir_cache();
            return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/findPassWord_step2";
        }
    }


    /**
     * 找回密码 第二步
     *
     * @param model
     * @param formbean
     * @param smsCode       短信验证码
     * @param captchaKey    验证Key
     * @param captchaValue  验证码
     * @param jumpUrl       跳转URL
     * @param token         令牌
     * @param redirectAttrs
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/findPassWord/step2", method = RequestMethod.POST)
    public String findPassWord_step2(ModelMap model, @Validated(ForgetPassword.class) User formbean, String smsCode,
                                     String captchaKey, String captchaValue,
                                     String jumpUrl, String token, RedirectAttributes redirectAttrs,
                                     HttpServletRequest request, HttpServletResponse response)
            throws Exception {

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据
        try {
            return findPassWord_step2(model, formbean, smsCode, captchaKey, captchaValue, token, isAjax, jumpUrl, request, response);
        } catch (CustomException e) {
            Map<String, String> returnError = new HashMap<String, String>();//错误
            returnError.put(e.getFiledName(), errorMessageService.customExceptionMsg(e));
            if (isAjax == true) {
                Map<String, Object> returnValue = new HashMap<String, Object>();//返回值
                returnValue.put("success", "false");
                returnValue.put("error", returnError);

                returnValue.put("captchaKey", UUIDUtil.getUUID32());
                WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
                return null;
            } else {
                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addFlashAttribute("user", formbean);

                String referer = request.getHeader("referer");

                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";

                return "redirect:/" + referer + queryString;
            }
        }
    }

    private String findPassWord_step2(ModelMap model, User formbean,
                                      String smsCode, String captchaKey, String captchaValue,
                                      String token, boolean isAjax, String jumpUrl,
                                      HttpServletRequest request, HttpServletResponse response) throws IOException {
        String dirName = templateService.findTemplateDir_cache();

        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        Optional.ofNullable(systemSetting).map(SystemSetting::getCloseSite).filter(o -> !o.equals(2)).orElseThrow(() -> new CustomException(ErrorCode.C_2_0001_0001, "user"));

        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);

        String account = Optional.ofNullable(formbean.getAccount()).map(String::trim).filter(Strings::isNotEmpty).orElseThrow(() -> new CustomException(ErrorCode.C_2_0004_0002, "account"));
        Optional<User> user = Optional.ofNullable(userService.findUserByAccount(account));

        captchaKey = Optional.ofNullable(captchaKey).map(String::trim).filter(Strings::isNotEmpty).orElseThrow(() -> new CustomException(ErrorCode.C_2_0004_0003, "captchaValue"));
        //验证验证码
        //增加验证码重试次数
        //统计每分钟原来提交次数
        Integer original = settingManage.getSubmitQuantity("captcha", captchaKey);
        if (original != null) {
            settingManage.addSubmitQuantity("captcha", captchaKey, original + 1);//刷新每分钟原来提交次数
        } else {
            settingManage.addSubmitQuantity("captcha", captchaKey, 1);//刷新每分钟原来提交次数
        }

        captchaValue = Optional.ofNullable(captchaValue).map(String::trim).filter(Strings::isNotEmpty).orElseThrow(() -> new CustomException(ErrorCode.C_1_0004_0008, "captchaValue"));

        //删除验证码
        captchaManage.captcha_delete(captchaKey);

        user.ifPresent(x -> Optional.of(x).map(User::getType).filter(o -> o.equals(10)).orElseThrow(() -> new CustomException(ErrorCode.C_2_0004_0008, "account")));

        //新密码
        String newPassword = formbean.getPassword();

        //修改密码
        topDonUserService.forgetPassword(user.map(User::getEmail).orElse(account), newPassword, captchaValue);
        Optional.ofNullable(formbean.getUserName()).map(Optional::of).orElseGet(() -> user.map(User::getUserName)).ifPresent(userManage::delete_userState);

        if (isAjax == true) {
            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值
            returnValue.put("success", "true");
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {
            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());
                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", errorMessageService.getMessage(C_2_0004_0021));
                String referer = Configuration.getUrl(request) + "login";//默认跳转到登录页
                model.addAttribute("urlAddress", referer);
                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }

    /**
     * 会话续期
     *
     * @param model
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/refreshToken", method = RequestMethod.POST)
    @ResponseBody
    public String refreshToken(ModelMap model,
                               HttpServletRequest request, HttpServletResponse response)
            throws Exception {

        Map<String, Object> returnValue = new HashMap<String, Object>();

        //从Header获取
        UserAuthorization headerUserAuthorization = WebUtil.getAuthorization(request);
        if (headerUserAuthorization != null) {
            String accessToken = headerUserAuthorization.getAccessToken();
            String refreshToken = headerUserAuthorization.getRefreshToken();

            if (accessToken != null && !"".equals(accessToken.trim()) && refreshToken != null && !"".equals(refreshToken.trim())) {

                RefreshUser refreshUser = oAuthManage.getRefreshUserByRefreshToken(refreshToken);
                if (refreshUser != null) {
                    String newAccessToken = UUIDUtil.getUUID32();
                    String newRefreshToken = UUIDUtil.getUUID32();
                    //令牌续期
                    boolean flag = oAuthManage.tokenRenewal(refreshToken, refreshUser, newAccessToken, newRefreshToken, request, response);

                    if (flag) {
                        returnValue.put("accessToken", newAccessToken);
                        returnValue.put("refreshToken", newRefreshToken);

                        AccessUser accessUser = AccessUserThreadLocal.get();
                        returnValue.put("systemUser", accessUser);//登录用户
                        return JsonUtils.toJSONString(returnValue);
                    }
                }
            }
        }
        throw new CustomException(ErrorCode.C_2_0001_0008, "refreshToken");
    }

    /**
     * 恢复微信浏览器会话
     * 微信浏览器被清理缓存后公众号自动登录
     *
     * @param model
     * @param code
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/recoverWeChatBrowserSession", method = RequestMethod.POST)
    @ResponseBody
    public String recoverWeChatBrowserSession(ModelMap model, String code,
                                              HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

        if (code != null && !"".equals(code.trim()) && WebUtil.isWeChatBrowser(request)) {//如果是微信客户端
            WeiXinOpenId weiXinOpenId = thirdPartyManage.queryWeiXinOpenId(code.trim());
            if (weiXinOpenId != null && weiXinOpenId.getOpenId() != null && !"".equals(weiXinOpenId.getOpenId())) {
                //添加到缓存
                thirdPartyManage.addWeiXinOpenId(code.trim(), weiXinOpenId);


                //刷新令牌号
                String refreshToken = oAuthManage.getRefreshTokenByOpenId(weiXinOpenId.getOpenId());
                if (refreshToken != null && !"".equals(refreshToken.trim())) {


                    RefreshUser refreshUser = oAuthManage.getRefreshUserByRefreshToken(refreshToken.trim());
                    if (refreshUser != null) {

                        //存放时间 单位/秒
                        int maxAge = 0;
                        if (refreshUser.isRememberMe()) {
                            maxAge = WebUtil.cookieMaxAge;//默认Cookie有效期
                        }
                        //将令牌写入Cookie

                        //将访问令牌添加到Cookie
                        WebUtil.addCookie(response, "cms_accessToken", refreshUser.getAccessToken(), maxAge);
                        //将刷新令牌添加到Cookie
                        WebUtil.addCookie(response, "cms_refreshToken", refreshToken, maxAge);

                        AccessUser accessUser = oAuthManage.getAccessUserByAccessToken(refreshUser.getAccessToken());

                        returnValue.put("systemUser", accessUser);//登录用户
                        returnValue.put("accessToken", refreshUser.getAccessToken());
                        returnValue.put("refreshToken", refreshToken);


                    }

                }
                returnValue.put("openId", weiXinOpenId.getOpenId());
            }
        }

        WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
        return null;

    }
}
