package cms.web.filter.wrapper;

import cms.utils.JsonUtils;
import cn.hutool.json.JSONObject;
import org.apache.commons.io.IOUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.util.StringUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/4/21 16:05
 */
public class RequestTrimHttpServletRequestWrapper extends HttpServletRequestWrapper {

    private static final String CHARSET = "utf-8";

    private Map<String, String[]> pareams = new HashMap<>();

    /**
     * Constructs a request object wrapping the given request.
     *
     * @param request The request to wrap
     * @throws IllegalArgumentException if the request is null
     */
    public RequestTrimHttpServletRequestWrapper(HttpServletRequest request) {
        super(request);

        this.pareams.putAll(request.getParameterMap());
        this.modifyParameterValues();
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        if (!APPLICATION_JSON_VALUE.equals(super.getHeader(HttpHeaders.CONTENT_TYPE))) {
            return super.getInputStream();
        }
        String json = IOUtils.toString(super.getInputStream(), CHARSET);
        if (!StringUtils.hasLength(json)) {
            return super.getInputStream();
        }
        JSONObject jsonObject = JsonUtils.jsonTrim(json);
        byte[] bytes = jsonObject.toString().getBytes(CHARSET);
        ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
        return new MyServletInputStream(bis);
    }

    public void modifyParameterValues() {
        pareams.entrySet().stream().forEach(e -> {
            String[] values = e.getValue();
            for (int i = 0; i < values.length; i++) {
                values[i] = values[i].trim();
            }
            e.setValue(values);
        });
    }

    @Override
    public String getParameter(String name) {
        return Optional.ofNullable(pareams.get(name)).filter(a -> a.length > 0).map(a -> a[0]).orElse(null);
    }

    @Override
    public String[] getParameterValues(String name) {
        return pareams.get(name);
    }

    class MyServletInputStream extends ServletInputStream {
        private ByteArrayInputStream bis;

        public MyServletInputStream(ByteArrayInputStream bis) {
            this.bis = bis;
        }

        @Override
        public boolean isFinished() {
            return true;
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setReadListener(ReadListener listener) {

        }

        @Override
        public int read() throws IOException {
            return bis.read();
        }
    }
}
