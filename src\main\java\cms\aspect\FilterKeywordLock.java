package cms.aspect;

import cms.service.keywordDict.TopicFilterKeyWordService;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/28 16:30
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface FilterKeywordLock {

    LockMapEnum lockMap();

    int lockKeyIndex();

    boolean isResponseWriter() default false;

    public enum LockMapEnum {
        TOPIC("topic", TopicFilterKeyWordService.topic_update_lock_map),
        COMMENT("comment", TopicFilterKeyWordService.comment_update_lock_map),
        REPLY("reply", TopicFilterKeyWordService.reply_update_lock_map),
        ;

        private String name;
        private Map<Long, Object> lockMap;
        private static Map<String, LockMapEnum> map_name;

        static {
            map_name = Arrays.stream(LockMapEnum.values()).collect(Collectors.toMap(LockMapEnum::getName, o -> o));
        }

        LockMapEnum(String name, Map<Long, Object> lockMap) {
            this.name = name;
            this.lockMap = lockMap;
        }

        public String getName() {
            return name;
        }

        public Map<Long, Object> getLockMap() {
            return lockMap;
        }

        public static Optional<LockMapEnum> getEnum(String name) {
            return Optional.ofNullable(name).map(map_name::get);
        }
    }
}
