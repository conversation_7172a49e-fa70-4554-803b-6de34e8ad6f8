package cms.service.question.impl;

import cms.bean.RequestResult;
import cms.bean.ResultCode;
import cms.bean.question.*;
import cms.bean.staff.SysUsers;
import cms.bean.topic.ImageInfo;
import cms.bean.user.PointLog;
import cms.bean.user.User;
import cms.service.aliyun.OssFileChangeService;
import cms.service.question.QuestionIndexService;
import cms.service.question.QuestionService;
import cms.service.question.QuestionTagService;
import cms.service.user.UserService;
import cms.utils.*;
import cms.web.action.SystemException;
import cms.web.action.TextFilterManage;
import cms.web.action.question.QuestionManage;
import cms.web.action.question.QuestionTagManage;
import cms.web.action.user.PointManage;
import cms.web.action.user.UserManage;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

import static cms.constant.Constant.QUESTION_TAG_GRADE_ONE;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/5 11:06
 */
@Service
@Slf4j
public class QuestionManageService {

    public static ConcurrentMap<Long, Boolean> questionUpdateLock = Maps.newConcurrentMap();

    @Resource
    QuestionService questionService;
    @Resource
    TextFilterManage textFilterManage;
    @Resource
    QuestionManage questionManage;
    @Resource
    QuestionTagService questionTagService;
    @Resource
    UserManage userManage;
    @Resource
    UserService userService;
    @Resource
    QuestionIndexService questionIndexService;
    @Resource
    PointManage pointManage;
    @Autowired
    private OssFileChangeService ossFileChangeService;

    @Transactional(propagation = Propagation.REQUIRED)
    public String add(ModelMap model, Long[] tagId, String tagName, String title, Boolean allow, Integer status, String point,
                      String content, String sort,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {
        tagId = ArrayUtil.distinct(tagId);
        Question question = new Question();
        List<String> imageNameList = null;
        boolean isImage = false;//是否含有图片
        List<String> flashNameList = null;
        boolean isFlash = false;//是否含有Flash
        List<String> mediaNameList = null;
        boolean isMedia = false;//是否含有音视频
        List<String> fileNameList = null;
        boolean isFile = false;//是否含有文件
        boolean isMap = false;//是否含有地图
        Map<String, String> error = new HashMap<String, String>();

        String username = "";//用户名称
        String userId = "";//用户Id
        Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (obj instanceof SysUsers) {
            userId = ((SysUsers) obj).getUserId();
            username = ((SysUsers) obj).getUserAccount();
        }

        question.setAllow(allow);
        question.setStatus(status);
        Date d = new Date();
        question.setPostTime(d);
        question.setLastAnswerTime(d);
        question.setImage("");

        List<Long> oneTagId = new ArrayList<>();
        LinkedHashSet<QuestionTagAssociation> questionTagAssociationList = new LinkedHashSet<QuestionTagAssociation>();
        if (tagId != null && tagId.length > 0) {
            List<QuestionTag> questionTagList = questionTagService.findAllQuestionTag();
            for (Long id : tagId) {
                if (id != null && id > 0L) {
                    QuestionTagAssociation questionTagAssociation = new QuestionTagAssociation();
                    questionTagAssociation.setQuestionTagId(id);
                    for (QuestionTag questionTag : questionTagList) {
                        if (questionTag.getId().equals(id) && questionTag.getChildNodeNumber().equals(0)) {
                            if (questionTag.getGrade() == QUESTION_TAG_GRADE_ONE) {
                                oneTagId.add(questionTag.getId());
                            }
                            questionTagAssociation.setQuestionTagName(questionTag.getName());
                            questionTagAssociation.setUserName(username);
                            questionTagAssociationList.add(questionTagAssociation);
                            break;
                        }

                    }
                }
            }
        } else {
            error.put("tagId", "标签不能为空");
        }

        if (oneTagId.size() == 0 || oneTagId.size() > 1) {
            error.put("tagId", "一级标签不能为空，且只能有一个");
        }

        if (title != null && !"".equals(title.trim())) {
            question.setTitle(title);
            if (title.length() > 150) {
                error.put("title", "不能大于150个字符");
            }
        } else {
            error.put("title", "标题不能为空");
        }
        if (content != null && !"".equals(content.trim())) {
            //过滤标签
            content = textFilterManage.filterTag(request, content);
            Object[] object = textFilterManage.filterHtml(request, content, "question", null);
            String value = (String) object[0];
            imageNameList = (List<String>) object[1];
            isImage = (Boolean) object[2];//是否含有图片
            flashNameList = (List<String>) object[3];
            isFlash = (Boolean) object[4];//是否含有Flash
            mediaNameList = (List<String>) object[5];
            isMedia = (Boolean) object[6];//是否含有音视频
            fileNameList = (List<String>) object[7];
            isFile = (Boolean) object[8];//是否含有文件
            isMap = (Boolean) object[9];//是否含有地图


            //不含标签内容
            String text = textFilterManage.filterText(textFilterManage.specifyHtmlTagToText(value));
            //清除空格&nbsp;
            String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();
            //摘要
            if (trimSpace != null && !"".equals(trimSpace)) {
                if (trimSpace.length() > 180) {
                    question.setSummary(trimSpace.substring(0, 180) + "..");
                } else {
                    question.setSummary(trimSpace + "..");
                }
            }

            //不含标签内容
            String source_text = textFilterManage.filterText(content);
            //清除空格&nbsp;
            String source_trimSpace = cms.utils.StringUtil.replaceSpace(source_text).trim();

            if (isImage == true || isFlash == true || isMedia == true || isFile == true || isMap == true || (!"".equals(source_text.trim()) && !"".equals(source_trimSpace))) {

                question.setIp(IpAddress.getClientIpAddress(request));
                question.setUserName(username);
                question.setIsStaff(true);
                question.setContent(value);
            } else {
                error.put("content", "问题内容不能为空");
            }

            //图片
            Optional.ofNullable(textFilterManage.readImageName(value, "question")).filter(CollUtil::isNotEmpty).map(l -> l.stream().map(o -> {
                ImageInfo imageInfo = new ImageInfo();
                imageInfo.setName(FileUtil.getName(o));
                imageInfo.setPath(FileUtil.getFullPath(o));
                return imageInfo;
            }).collect(Collectors.toList())).map(JSON::toJSONString).ifPresent(question::setImage);
        } else {
            error.put("content", "问题内容不能为空");
        }

        if (point != null && !"".equals(point.trim())) {
            if (point.trim().length() > 8) {
                error.put("point", "不能超过8位数字");
            } else {
                boolean pointVerification = Verification.isPositiveIntegerZero(point.trim());//正整数+0
                if (pointVerification) {
                    Long _rewardPoint = Long.parseLong(point.trim());
                    if (_rewardPoint < 0L) {
                        error.put("point", "不能小于0");

                    }
                    if (error.size() == 0) {
                        question.setPoint(_rewardPoint);
                    }
                } else {
                    error.put("point", "请填写正整数");
                }
            }
        }

        if (sort != null) {
            if (Verification.isNumeric(sort)) {
                if (sort.length() <= 8) {
                    question.setSort(Integer.parseInt(sort));
                } else {
                    error.put("sort", "不能超过8位数字");
                }
            } else {
                error.put("sort", "请填写整数");
            }
        } else {
            error.put("sort", "排序不能为空");
        }

        if (error.size() == 0) {
            questionService.saveQuestion(question, new ArrayList<QuestionTagAssociation>(questionTagAssociationList), null, null, null, null);

            //更新索引
            questionIndexService.addQuestionIndex(new QuestionIndex(String.valueOf(question.getId()), 1));
            QuestionTagManage.incOneTagQuestionNum(oneTagId);
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String appendQuestion(ModelMap model, Long questionId, String content,
                                 HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<String> imageNameList = null;
        boolean isImage = false;//是否含有图片
        List<String> flashNameList = null;
        boolean isFlash = false;//是否含有Flash
        List<String> mediaNameList = null;
        boolean isMedia = false;//是否含有音视频
        List<String> fileNameList = null;
        boolean isFile = false;//是否含有文件
        boolean isMap = false;//是否含有地图
        Map<String, String> error = new HashMap<String, String>();

        String appendContent = "";
        Question question = null;
        if (questionId != null && questionId > 0L) {
            question = questionService.findById(questionId);
            if (question != null) {
                if (content != null && !"".equals(content.trim())) {
                    //过滤标签
                    content = textFilterManage.filterTag(request, content);
                    Object[] object = textFilterManage.filterHtml(request, content, "question", null);
                    String value = (String) object[0];
                    imageNameList = (List<String>) object[1];
                    isImage = (Boolean) object[2];//是否含有图片
                    flashNameList = (List<String>) object[3];
                    isFlash = (Boolean) object[4];//是否含有Flash
                    mediaNameList = (List<String>) object[5];
                    isMedia = (Boolean) object[6];//是否含有音视频
                    fileNameList = (List<String>) object[7];
                    isFile = (Boolean) object[8];//是否含有文件
                    isMap = (Boolean) object[9];//是否含有地图

                    //不含标签内容
                    String source_text = textFilterManage.filterText(content);
                    //清除空格&nbsp;
                    String source_trimSpace = cms.utils.StringUtil.replaceSpace(source_text).trim();

                    if (isImage == true || isFlash == true || isMedia == true || isFile == true || isMap == true || (!"".equals(source_text.trim()) && !"".equals(source_trimSpace))) {
                        appendContent = value;
                    } else {
                        error.put("content", "问题内容不能为空");
                    }
                } else {
                    error.put("content", "问题内容不能为空");
                }
            } else {
                error.put("content", "问题不存在");
            }
        } else {
            error.put("content", "问题Id不能为空");
        }

        String appendContent_json = "";
        if (appendContent != null && !"".equals(appendContent.trim())) {
            AppendQuestionItem appendQuestionItem = new AppendQuestionItem();
            appendQuestionItem.setId(UUIDUtil.getUUID32());
            appendQuestionItem.setContent(appendContent.trim());
            appendQuestionItem.setPostTime(new Date());
            appendContent_json = JsonUtils.toJSONString(appendQuestionItem);
        } else {
            error.put("content", "追加内容不能为空");
        }

        if (error != null && error.size() > 0) {
            model.addAttribute("error", error);
        } else {
            //追加问题
            questionService.saveAppendQuestion(questionId, appendContent_json + ",");
            //更新索引
            questionIndexService.addQuestionIndex(new QuestionIndex(String.valueOf(question.getId()), 2));

            //删除缓存
            questionManage.delete_cache_findById(questionId);//删除问题缓存
        }
        if (error.size() == 0) {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String editQuestion(ModelMap model, Long questionId, Long[] tagId,
                               String title, Boolean allow, Integer status, String point, String amount,
                               String content, String sort, Boolean visible,
                               HttpServletRequest request, HttpServletResponse response) throws Exception {
        tagId = ArrayUtil.distinct(tagId);
        String username = "";//用户名称
        String userId = "";//用户Id
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof SysUsers) {
            userId = ((SysUsers) principal).getUserId();
            username = ((SysUsers) principal).getUserAccount();
        }

        Question question = null;
        List<String> imageNameList = null;
        boolean isImage = false;//是否含有图片
        List<String> flashNameList = null;
        boolean isFlash = false;//是否含有Flash
        List<String> mediaNameList = null;
        boolean isMedia = false;//是否含有音视频
        List<String> fileNameList = null;
        boolean isFile = false;//是否含有文件
        boolean isMap = false;//是否含有地图
        Map<String, String> error = new HashMap<String, String>();

        //旧状态
        Integer old_status = -1;

        String old_content = "";
        List<Long> newOneTagId = Lists.newArrayList();
        if (questionId != null && questionId > 0L) {
            question = questionService.findById(questionId);
            if (question != null) {
                List<QuestionTag> questionTagList = questionTagService.findAllQuestionTag();
                Map<Long, QuestionTag> tagMap = questionTagList.stream().collect(Collectors.toMap(QuestionTag::getId, o -> o));
                old_status = question.getStatus();
                question.setAllow(allow);
                question.setStatus(status);
                question.setImage("");

                old_content = question.getContent();
                if (title != null && !"".equals(title.trim())) {
                    question.setTitle(title);
                    if (title.length() > 150) {
                        error.put("title", "不能大于150个字符");
                    }
                } else {
                    error.put("title", "标题不能为空");
                }

                LinkedHashSet<QuestionTagAssociation> questionTagAssociationList = new LinkedHashSet<QuestionTagAssociation>();
                if (tagId != null && tagId.length > 0) {
                    for (Long id : tagId) {
                        if (id != null && id > 0L) {
                            QuestionTagAssociation questionTagAssociation = new QuestionTagAssociation();
                            questionTagAssociation.setQuestionTagId(id);
                            for (QuestionTag questionTag : questionTagList) {
                                if (questionTag.getId().equals(id) && questionTag.getChildNodeNumber().equals(0)) {
                                    if (QUESTION_TAG_GRADE_ONE == questionTag.getGrade()) {
                                        newOneTagId.add(questionTag.getId());
                                    }
                                    questionTagAssociation.setQuestionTagName(questionTag.getName());
                                    questionTagAssociation.setUserName(question.getUserName());
                                    questionTagAssociationList.add(questionTagAssociation);
                                    break;
                                }
                            }
                        }
                    }
                } else {
                    error.put("tagId", "标签不能为空");
                }

                if (newOneTagId.size() == 0 || newOneTagId.size() > 1) {
                    error.put("tagId", "一级标签不能为空，且只能有一个");
                }

                //悬赏积分
                Long rewardPoint = null;
                //悬赏金额
                BigDecimal rewardAmount = null;

                User user = userService.findUserByUserName(question.getUserName());//查询用户数据
                if (question.getAdoptionAnswerId().equals(0L)) {//未采纳答案的问题才允许修改赏金
                    if (point != null && !"".equals(point.trim())) {
                        if (point.trim().length() > 8) {
                            error.put("point", "不能超过8位数字");
                        } else {
                            boolean pointVerification = Verification.isPositiveIntegerZero(point.trim());//正整数+0
                            if (pointVerification) {
                                Long _rewardPoint = Long.parseLong(point.trim());
                                if (_rewardPoint < 0L) {
                                    error.put("point", "不能小于0");

                                }

                                if (question.getIsStaff() == false && _rewardPoint > user.getPoint()) {
                                    error.put("point", "不能大于账户积分");
                                }

                                if (error.size() == 0) {
                                    rewardPoint = _rewardPoint;
                                }
                            } else {
                                error.put("point", "请填写正整数或0");
                            }
                        }
                    }
                }

                //变更积分符号
                boolean changePointSymbol = true;//true：加号  false：减号
                //变更积分
                Long changePoint = 0L;
                //变更金额符号
                boolean changeAmountSymbol = true;//true：加号  false：减号
                //变更金额
                BigDecimal changeAmount = new BigDecimal("0");

                if (rewardPoint != null && rewardPoint > question.getPoint()) {
                    changePointSymbol = true;
                    changePoint = rewardPoint - question.getPoint();
                }
                if (rewardPoint != null && rewardPoint < question.getPoint()) {
                    changePointSymbol = false;
                    changePoint = question.getPoint() - rewardPoint;
                }

                //用户悬赏积分日志
                Object pointLogObject = null;
                //用户悬赏金额日志
                Object paymentLogObject = null;

                Date time = new Date();
                if (changePoint != null && changePoint > 0L) {//如果有变更积分
                    question.setPoint(rewardPoint);

                    if (question.getIsStaff() == false) {
                        PointLog reward_pointLog = new PointLog();
                        reward_pointLog.setId(pointManage.createPointLogId(user.getId()));
                        reward_pointLog.setModule(1200);//1200.调整赏金
                        reward_pointLog.setParameterId(question.getId());//参数Id
                        reward_pointLog.setOperationUserType(1);//操作用户类型  0:系统  1: 员工  2:会员
                        reward_pointLog.setOperationUserName(username);//操作用户名称
                        reward_pointLog.setPointState(changePointSymbol == true ? 2 : 1);//2:账户支出
                        reward_pointLog.setPoint(changePoint);//积分
                        reward_pointLog.setUserName(user.getUserName());//用户名称
                        reward_pointLog.setRemark("");
                        reward_pointLog.setTimes(time);
                        pointLogObject = pointManage.createPointLogObject(reward_pointLog);
                    }
                }

                if (content != null && !"".equals(content.trim())) {
                    //过滤标签
                    content = textFilterManage.filterTag(request, content);
                    Object[] object = textFilterManage.filterHtml(request, content, "question", null);

                    String value = (String) object[0];
                    imageNameList = (List<String>) object[1];
                    isImage = (Boolean) object[2];//是否含有图片
                    flashNameList = (List<String>) object[3];
                    isFlash = (Boolean) object[4];//是否含有Flash
                    mediaNameList = (List<String>) object[5];
                    isMedia = (Boolean) object[6];//是否含有音视频
                    fileNameList = (List<String>) object[7];
                    isFile = (Boolean) object[8];//是否含有文件
                    isMap = (Boolean) object[9];//是否含有地图

                    //不含标签内容
                    String text = textFilterManage.filterText(textFilterManage.specifyHtmlTagToText(value));

                    //清除空格&nbsp;
                    String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();

                    //摘要
                    if (trimSpace != null && !"".equals(trimSpace)) {
                        if (trimSpace.length() > 180) {
                            question.setSummary(trimSpace.substring(0, 180) + "..");
                        } else {
                            question.setSummary(trimSpace + "..");
                        }
                    }

                    //不含标签内容
                    String source_text = textFilterManage.filterText(content);
                    //清除空格&nbsp;
                    String source_trimSpace = cms.utils.StringUtil.replaceSpace(source_text).trim();
                    if (isImage == true || isFlash == true || isMedia == true || isFile == true || isMap == true || (!"".equals(source_text.trim()) && !"".equals(source_trimSpace))) {
                        question.setContent(value);
                    } else {
                        error.put("content", "问题内容不能为空");
                    }

                    //图片
                    Optional.ofNullable(textFilterManage.readImageName(value, "question")).filter(CollUtil::isNotEmpty).map(l -> l.stream().map(o -> {
                        ImageInfo imageInfo = new ImageInfo();
                        imageInfo.setName(FileUtil.getName(o));
                        imageInfo.setPath(FileUtil.getFullPath(o));
                        return imageInfo;
                    }).collect(Collectors.toList())).map(JSON::toJSONString).ifPresent(question::setImage);
                } else {
                    error.put("content", "问题内容不能为空");
                }

                if (sort != null) {
                    if (Verification.isNumeric(sort)) {
                        if (sort.length() <= 8) {
                            question.setSort(Integer.parseInt(sort));
                        } else {
                            error.put("sort", "不能超过8位数字");
                        }
                    } else {
                        error.put("sort", "请填写整数");
                    }
                } else {
                    error.put("sort", "排序不能为空");
                }


                if (error.size() == 0) {
                    if (null == questionUpdateLock.putIfAbsent(question.getId(), Boolean.FALSE)) {
                        try {
                            List<Long> oldOneTagId = questionService.findQuestionTagAssociationByQuestionId(questionId).stream().map(QuestionTagAssociation::getQuestionTagId).map(tagMap::get).filter(Objects::nonNull).filter(o -> o.getGrade() == QUESTION_TAG_GRADE_ONE).map(QuestionTag::getId).collect(Collectors.toList());
                            question.setLastUpdateTime(new Date());//最后修改时间
                            int i = questionService.updateQuestion(question, new ArrayList<QuestionTagAssociation>(questionTagAssociationList),
                                    changePointSymbol, changePoint, changeAmountSymbol, changeAmount, pointLogObject, paymentLogObject);
                            //更新索引
                            questionIndexService.addQuestionIndex(new QuestionIndex(String.valueOf(question.getId()), 2));

                            if (i > 0 && !old_status.equals(status)) {
                                if (user != null) {
                                    //修改用户动态问题状态
                                    userService.updateUserDynamicQuestionStatus(user.getId(), question.getUserName(), question.getId(), question.getStatus());
                                }

                            }

                            //删除缓存
                            questionManage.delete_cache_findById(question.getId());//删除问题缓存
                            questionManage.delete_cache_findQuestionTagAssociationByQuestionId(question.getId());//删除'根据问题Id查询问题标签关联'缓存
                            questionManage.delete_cache_markUpdateStatus_question(question.getId());
                            if (user != null) {
                                userManage.delete_cache_findUserById(user.getId());
                                userManage.delete_cache_findUserByUserName(user.getUserName());
                            }
                            QuestionTagManage.incDecOneTagQuestionNum(oldOneTagId, newOneTagId);
                            ossFileChangeService.fileChange(old_content, "question", imageNameList, flashNameList, mediaNameList, fileNameList);
                        } catch (SystemException e) {
                            error.put("question", e.getMessage());//提交问题错误
                        } finally {
                            questionUpdateLock.remove(question.getId());
                        }
                    }
                } else {
                    error.put("content", "问题正在被人修改");
                }
            } else {
                error.put("question", "问题不存在");
            }
        } else {
            error.put("question", "Id不存在");
        }

        if (error.size() == 0) {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String editAppendQuestion(ModelMap model, Long questionId, String appendQuestionItemId, String content,
                                     HttpServletRequest request, HttpServletResponse response) throws Exception {
        Question question = null;
        List<String> imageNameList = null;
        boolean isImage = false;//是否含有图片
        List<String> flashNameList = null;
        boolean isFlash = false;//是否含有Flash
        List<String> mediaNameList = null;
        boolean isMedia = false;//是否含有音视频
        List<String> fileNameList = null;
        boolean isFile = false;//是否含有文件
        boolean isMap = false;//是否含有地图
        Map<String, String> error = new HashMap<String, String>();

        String old_content = "";
        String appendContent = "";
        if (questionId != null && questionId > 0L) {
            question = questionService.findById(questionId);
            if (question != null) {
                //删除最后一个逗号
                String _appendContent = StringUtils.substringBeforeLast(question.getAppendContent(), ",");//从右往左截取到相等的字符,保留左边的

                List<AppendQuestionItem> appendQuestionItemList = JsonUtils.toGenericObject(_appendContent + "]", new TypeReference<List<AppendQuestionItem>>() {
                });

                boolean flag = false;
                if (appendQuestionItemList != null && appendQuestionItemList.size() > 0) {
                    for (AppendQuestionItem appendQuestionItem : appendQuestionItemList) {
                        if (appendQuestionItem.getId().equals(appendQuestionItemId)) {
                            old_content = appendQuestionItem.getContent();
                            flag = true;
                            break;
                        }
                    }
                }
                if (flag == false) {
                    error.put("question", "追加问题不存在");
                }

                if (content != null && !"".equals(content.trim())) {
                    //过滤标签
                    content = textFilterManage.filterTag(request, content);
                    Object[] object = textFilterManage.filterHtml(request, content, "question", null);

                    String value = (String) object[0];
                    imageNameList = (List<String>) object[1];
                    isImage = (Boolean) object[2];//是否含有图片
                    flashNameList = (List<String>) object[3];
                    isFlash = (Boolean) object[4];//是否含有Flash
                    mediaNameList = (List<String>) object[5];
                    isMedia = (Boolean) object[6];//是否含有音视频
                    fileNameList = (List<String>) object[7];
                    isFile = (Boolean) object[8];//是否含有文件
                    isMap = (Boolean) object[9];//是否含有地图

                    //不含标签内容
                    String source_text = textFilterManage.filterText(content);
                    //清除空格&nbsp;
                    String source_trimSpace = cms.utils.StringUtil.replaceSpace(source_text).trim();
                    if (isImage == true || isFlash == true || isMedia == true || isFile == true || isMap == true || (!"".equals(source_text.trim()) && !"".equals(source_trimSpace))) {
                        appendContent = value;
                    } else {
                        error.put("content", "问题内容不能为空");
                    }
                } else {
                    error.put("content", "问题内容不能为空");
                }

                if (appendQuestionItemList != null && appendQuestionItemList.size() > 0) {
                    for (AppendQuestionItem appendQuestionItem : appendQuestionItemList) {
                        if (appendQuestionItem.getId().equals(appendQuestionItemId)) {
                            appendQuestionItem.setContent(appendContent);
                        }
                    }
                }

                String appendContent_json = JsonUtils.toJSONString(appendQuestionItemList);
                //删除最后一个中括号
                appendContent_json = StringUtils.substringBeforeLast(appendContent_json, "]");//从右往左截取到相等的字符,保留左边的

                if (error.size() == 0) {
                    int i = questionService.updateAppendQuestion(questionId, appendContent_json + ",");
                    //更新索引
                    questionIndexService.addQuestionIndex(new QuestionIndex(String.valueOf(question.getId()), 2));

                    //删除缓存
                    questionManage.delete_cache_findById(question.getId());//删除问题缓存

                    ossFileChangeService.fileChange(old_content, "question", imageNameList, flashNameList, mediaNameList, fileNameList);
                }
            } else {
                error.put("question", "问题不存在");
            }
        } else {
            error.put("question", "Id不存在");
        }

        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String deleteQuestion(ModelMap model, Long[] questionId,
                                 HttpServletRequest request, HttpServletResponse response) throws Exception {

        String username = "";//用户名称
        String userId = "";//用户Id
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof SysUsers) {
            userId = ((SysUsers) principal).getUserId();
            username = ((SysUsers) principal).getUserAccount();
        }
        Map<String, String> error = new HashMap<String, String>();//错误

        if (questionId != null && questionId.length > 0) {
            List<Long> questionIdList = new ArrayList<Long>();
            for (Long l : questionId) {
                if (l != null && l > 0L) {
                    questionIdList.add(l);
                }
            }
            if (questionIdList != null && questionIdList.size() > 0) {
                List<Question> questionList = questionService.findByIdList(questionIdList);
                if (questionList != null && questionList.size() > 0) {
                    for (Question question : questionList) {
                        User user = userManage.query_cache_findUserByUserName(question.getUserName());
                        //悬赏金额
                        BigDecimal rewardAmount = new BigDecimal("0.00");
                        //悬赏积分
                        Long rewardPoint = 0L;
                        //用户悬赏积分日志
                        Object pointLogObject = null;
                        //用户悬赏金额日志
                        Object paymentLogObject = null;

                        if (null == questionUpdateLock.putIfAbsent(question.getId(), Boolean.FALSE)) {
                            try {
                                question = questionService.findById(question.getId());
                                if (question.getStatus() < 100) {//标记删除
                                    Map<Long, QuestionTag> tagMap = questionTagService.findAllQuestionTag().stream().collect(Collectors.toMap(QuestionTag::getId, o -> o));
                                    List<Long> tagIds = questionService.findQuestionTagAssociationByQuestionId(question.getId()).stream().map(QuestionTagAssociation::getQuestionTagId).map(tagMap::get).filter(Objects::nonNull).filter(o -> o.getGrade() == QUESTION_TAG_GRADE_ONE).map(QuestionTag::getId).collect(Collectors.toList());
                                    int i = questionService.markDelete(question.getId());

                                    if (i > 0 && user != null) {
                                        //修改问题状态
                                        userService.softDeleteUserDynamicByQuestionId(user.getId(), question.getUserName(), question.getId());
                                    }
                                    //更新索引
                                    questionIndexService.addQuestionIndex(new QuestionIndex(String.valueOf(question.getId()), 2));
                                    questionManage.delete_cache_findById(question.getId());//删除缓存
                                    questionManage.delete_cache_findQuestionTagAssociationByQuestionId(question.getId());//删除'根据问题Id查询问题标签关联'缓存
                                    QuestionTagManage.decOneTagQuestionNum(tagIds);
                                } else {//物理删除
                                    if (question.getAdoptionAnswerId().equals(0L) && !question.getIsStaff()) {//如果悬赏未采纳答案，则将赏金退还给提问用户
                                        Date time = new Date();
                                        if (user != null && question.getPoint() != null && question.getPoint() > 0L) {
                                            rewardPoint = question.getPoint();
                                            PointLog reward_pointLog = new PointLog();
                                            reward_pointLog.setId(pointManage.createPointLogId(user.getId()));
                                            reward_pointLog.setModule(1000);//1000.悬赏积分
                                            reward_pointLog.setParameterId(question.getId());//参数Id
                                            reward_pointLog.setOperationUserType(1);//操作用户类型  0:系统  1: 员工  2:会员
                                            reward_pointLog.setOperationUserName(username);//操作用户名称
                                            reward_pointLog.setPointState(1);//2:账户支出
                                            reward_pointLog.setPoint(question.getPoint());//积分
                                            reward_pointLog.setUserName(user.getUserName());//用户名称
                                            reward_pointLog.setRemark("");
                                            reward_pointLog.setTimes(time);
                                            pointLogObject = pointManage.createPointLogObject(reward_pointLog);
                                        }
                                    }

                                    try {
                                        int i = questionService.deleteQuestion(question.getId(), question.getUserName(), rewardPoint, pointLogObject, rewardAmount, paymentLogObject);
                                        if (i > 0) {
                                            //根据问题Id删除用户动态(问题下的评论和回复也同时删除)
                                            userService.deleteUserDynamicByQuestionId(question.getId());
                                        }

                                        questionManage.delete_cache_findById(question.getId());//删除缓存
                                        questionManage.delete_cache_findQuestionTagAssociationByQuestionId(question.getId());//删除'根据问题Id查询问题标签关联'缓存
                                        if (user != null) {
                                            userManage.delete_cache_findUserById(user.getId());
                                            userManage.delete_cache_findUserByUserName(user.getUserName());
                                        }

                                        //更新索引
                                        questionIndexService.addQuestionIndex(new QuestionIndex(String.valueOf(question.getId()), 3));

                                        String questionContent = question.getContent();

                                        //删除最后一个逗号
                                        String _appendContent = StringUtils.substringBeforeLast(question.getAppendContent(), ",");//从右往左截取到相等的字符,保留左边的

                                        List<AppendQuestionItem> appendQuestionItemList = JsonUtils.toGenericObject(_appendContent + "]", new TypeReference<List<AppendQuestionItem>>() {
                                        });
                                        if (appendQuestionItemList != null && appendQuestionItemList.size() > 0) {
                                            for (AppendQuestionItem appendQuestionItem : appendQuestionItemList) {
                                                questionContent += appendQuestionItem.getContent();
                                            }
                                        }

                                        ossFileChangeService.fileChange(questionContent, "question", Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList());
                                    } catch (SystemException e) {
                                        error.put("answer", e.getMessage());
                                    }
                                }
                            } finally {
                                questionUpdateLock.remove(question.getId());
                            }
                        }
                    }
                } else {
                    error.put("questionId", "问题不存在");
                }
            } else {
                error.put("questionId", "问题Id组不能为空");
            }
        } else {
            error.put("questionId", "问题Id不能为空");
        }

        if (error.size() > 0) {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
        } else {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String deleteAppendQuestion(ModelMap model, Long questionId, String appendQuestionItemId,
                                       HttpServletRequest request, HttpServletResponse response) throws Exception {
        //错误
        Map<String, String> error = new HashMap<String, String>();
        if (questionId != null && questionId > 0 && appendQuestionItemId != null && !"".equals(appendQuestionItemId.trim())) {
            Question question = questionService.findById(questionId);
            if (question != null) {
                boolean flag = false;
                String old_content = "";
                //删除最后一个逗号
                String _appendContent = StringUtils.substringBeforeLast(question.getAppendContent(), ",");//从右往左截取到相等的字符,保留左边的

                List<AppendQuestionItem> appendQuestionItemList = JsonUtils.toGenericObject(_appendContent + "]", new TypeReference<List<AppendQuestionItem>>() {
                });

                if (appendQuestionItemList != null && appendQuestionItemList.size() > 0) {
                    Iterator<AppendQuestionItem> iter = appendQuestionItemList.iterator();
                    while (iter.hasNext()) {
                        AppendQuestionItem appendQuestionItem = iter.next();
                        if (appendQuestionItem.getId().equals(appendQuestionItemId)) {
                            old_content = appendQuestionItem.getContent();
                            flag = true;
                            iter.remove();
                            break;
                        }
                    }
                }

                String appendContent_json = JsonUtils.toJSONString(appendQuestionItemList);
                //删除最后一个中括号
                appendContent_json = StringUtils.substringBeforeLast(appendContent_json, "]");//从右往左截取到相等的字符,保留左边的

                if (appendQuestionItemList.size() > 0) {
                    appendContent_json += ",";
                }
                if (flag) {
                    String fileNumber = questionManage.generateFileNumber(question.getUserName(), question.getIsStaff());

                    int i = questionService.updateAppendQuestion(questionId, appendContent_json);

                    questionManage.delete_cache_findById(question.getId());//删除缓存
                    //更新索引
                    questionIndexService.addQuestionIndex(new QuestionIndex(String.valueOf(question.getId()), 2));
                    ossFileChangeService.fileChange(old_content, "question", Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList());
                }
            } else {
                error.put("questionId", "问题不存在");
            }
        } else {
            error.put("questionId", "问题Id或追加问题项Id不能为空");
        }

        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String reduction(ModelMap model, Long[] questionId,
                            HttpServletResponse response) throws Exception {
        //错误
        Map<String, String> error = new HashMap<String, String>();

        if (questionId != null && questionId.length > 0) {
            List<Question> questionList = questionService.findByIdList(Arrays.asList(questionId));
            if (questionList != null && questionList.size() > 0) {
                int i = questionService.reductionQuestion(questionList);

                for (Question question : questionList) {

                    User user = userManage.query_cache_findUserByUserName(question.getUserName());
                    if (i > 0 && user != null) {
                        //修改问题状态
                        userService.reductionUserDynamicByQuestionId(user.getId(), question.getUserName(), question.getId());
                    }

                    //更新索引
                    questionIndexService.addQuestionIndex(new QuestionIndex(String.valueOf(question.getId()), 2));
                    questionManage.delete_cache_findById(question.getId());//删除缓存
                }

                return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
            } else {
                error.put("question", "问题不能为空");
            }
        } else {
            error.put("question", "问题Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String auditQuestion(ModelMap model, Long questionId,
                                HttpServletResponse response) throws Exception {
        //错误
        Map<String, String> error = new HashMap<String, String>();
        if (questionId != null && questionId > 0L) {
            int i = questionService.updateQuestionStatus(questionId, 20);

            Question question = questionManage.query_cache_findById(questionId);
            if (i > 0 && question != null) {
                User user = userManage.query_cache_findUserByUserName(question.getUserName());
                if (user != null) {
                    //修改问题状态
                    userService.updateUserDynamicQuestionStatus(user.getId(), question.getUserName(), question.getId(), 20);
                }
            }

            //更新索引
            questionIndexService.addQuestionIndex(new QuestionIndex(String.valueOf(questionId), 2));
            questionManage.delete_cache_findById(questionId);//删除缓存
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        } else {
            error.put("questionId", "问题Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }
}
