package cms.bean.template;

import cms.bean.topic.TopicSize;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 站点栏目
 */
@Data
public class Column implements Serializable {
    private static final long serialVersionUID = 4117213723041628243L;

    /**
     * ID
     **/
    private Integer id;
    /**
     * 栏目名称
     **/
    private String name;
    /**
     * 所属父类ID
     **/
    private Integer parentId = 0;
    /**
     * 子栏目
     **/
    private List<Column> childColumn = new ArrayList<Column>();
    /**
     * 排序
     **/
    private Integer sort = 1;
    /**
     * 链接方式   1.无   2.外部URL  3.内部URL  4.空白页
     **/
    private Integer linkMode = 1;
    /**
     * URL
     **/
    private String url = "";
    /**
     * 简介
     **/
    private String intro = "";
    /**
     * 导航图标(默认状态)URL
     **/
    private String defaultIconUrl = "";
    /**
     * 导航图标(选中状态)URL
     **/
    private String selectedIconUrl = "";

    private Long tagId;

    private TopicSize topicSize;

    /**
     * 添加子栏目
     *
     * @param column
     */
    public void addColumn(Column column) {
        this.getChildColumn().add(column);
    }

    /**
     * 添加子栏目
     *
     * @param column
     */
    public void addColumn(List<Column> childColumn) {
        this.getChildColumn().addAll(childColumn);
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public List<Column> getChildColumn() {
        return childColumn;
    }

    public void setChildColumn(List<Column> childColumn) {
        this.childColumn = childColumn;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getLinkMode() {
        return linkMode;
    }

    public void setLinkMode(Integer linkMode) {
        this.linkMode = linkMode;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public String getDefaultIconUrl() {
        return defaultIconUrl;
    }

    public void setDefaultIconUrl(String defaultIconUrl) {
        this.defaultIconUrl = defaultIconUrl;
    }

    public String getSelectedIconUrl() {
        return selectedIconUrl;
    }

    public void setSelectedIconUrl(String selectedIconUrl) {
        this.selectedIconUrl = selectedIconUrl;
    }

    public TopicSize getTopicSize() {
        return topicSize;
    }

    public void setTopicSize(TopicSize topicSize) {
        this.topicSize = topicSize;
    }
}
