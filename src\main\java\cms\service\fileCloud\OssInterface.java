package cms.service.fileCloud;

import java.io.InputStream;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/9/21 20:27
 */
public interface OssInterface {

    public String upload(InputStream input, String objectName);

    public List<String> listFile();

    public void del(List<String> path);

    public String getUploadStartPath();

    public String getTitlePath();

    public InputStream getFile(String path);
}
