package cms.web.action.common;

import cms.bean.RequestResult;
import cms.bean.ResultCode;
import cms.bean.keywordDict.SearchKeywordDict;
import cms.bean.message.EmailRemind;
import cms.bean.setting.EditorTag;
import cms.bean.setting.SystemSetting;
import cms.bean.template.RollImg;
import cms.bean.topdon.PlatUser;
import cms.bean.topdon.TopdonResult;
import cms.bean.user.AccessUser;
import cms.constant.BbsInnerApiAuthUtil;
import cms.constant.ErrorCode;
import cms.constant.FileConstant;
import cms.handle.CustomException;
import cms.service.aliyun.OssFileChangeService;
import cms.service.fileCloud.FileCloudService;
import cms.service.keywordDict.SearchKeywordDictService;
import cms.service.message.impl.EmailRemindService;
import cms.service.operation.OperationTagService;
import cms.service.operation.OperationTopicService;
import cms.service.setting.SettingService;
import cms.service.setting.impl.EmailRemindSettingService;
import cms.service.template.impl.RollImgService;
import cms.service.user.impl.UserLoginService;
import cms.utils.EmailUtil;
import cms.utils.JsonUtils;
import cms.utils.threadLocal.AccessUserThreadLocal;
import cms.web.action.CSRFTokenManage;
import cms.web.action.setting.SettingManage;
import cms.web.action.user.UserManage;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.logging.log4j.util.Strings;
import org.jsoup.parser.ParseSettings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.security.GeneralSecurityException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

import static cms.constant.Constant.ACTIVE_PRO;
import static cms.constant.Constant.USER_TYPE_LOCAL;
import static cms.constant.ErrorCode.C_1_0001_0020;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/6/14 9:21
 */
@Controller
@RequestMapping("staticInfo/control/home")
@Slf4j
public class HomeStaticAction {

    public static List<RollImg> url = Lists.newArrayList();

    @Resource
    CSRFTokenManage csrfTokenManage;
    @Autowired
    private SearchKeywordDictService searchKeywordDictService;
    @Autowired
    private RollImgService rollImgService;
    @Autowired
    private FileCloudService fileCloudService;
    @Resource
    SettingManage settingManage;
    @Resource
    SettingService settingService;
    @Autowired
    private OssFileChangeService ossFileChangeService;
    @Autowired
    private EmailRemindSettingService emailRemindSettingService;
    @Resource
    UserManage userManage;
    @Autowired
    private OperationTagService operationTagService;
    @Autowired
    private OperationTopicService operationTopicService;
    @Value("${spring.profiles.active}")
    private String active;
    @Autowired
    private UserLoginService userLoginService;

    @ResponseBody
    @RequestMapping(value = "/getRollImg", method = RequestMethod.GET)
    public String getRollImg(String token, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, url));
    }

    @ResponseBody
    @RequestMapping(value = "/refreshRollImg", method = RequestMethod.GET)
    public String refreshRollImg(String token, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        url = rollImgService.findAllList();
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, url));
    }

    @ResponseBody
    @RequestMapping(value = "/getKeywords", method = RequestMethod.GET)
    public String getKeywords(String token, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, Optional.ofNullable(searchKeywordDictService.findAll()).map(l -> l.stream().map(SearchKeywordDict::getKeyWord).collect(Collectors.toList())).orElse(Lists.newArrayList())));
    }

    @ResponseBody
    @RequestMapping(value = "/getBuckets", method = RequestMethod.GET)
    public String getBuckets(String token, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
//        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, Optional.ofNullable(fileCloudService.getBuckets()).map(l -> l.stream().map(Bucket::getName).collect(Collectors.toList())).orElse(null)));
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, ""));
    }

    @ResponseBody
    @RequestMapping(value = "/upload", method = RequestMethod.GET)
    public String upload(MultipartFile file, String token, HttpServletRequest request, HttpServletResponse response) throws Exception {
        if (ACTIVE_PRO.equals(active)) {
            return "";
        }
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
//        String result = ossService.upload(file, "oss" + File.separator + "123." + FileUtil.getSuffix(file.getOriginalFilename()));
        String result = fileCloudService.uploadByMp4Code264(file.getInputStream(), "oss" + File.separator + "123." + FileUtil.getSuffix(file.getOriginalFilename()));
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, result));
    }

    @ResponseBody
    @RequestMapping(value = "/listFile", method = RequestMethod.GET)
    public String listFile(String token, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
//        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, ossService.listFile()));
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, fileCloudService.listFile()));
    }

    @ResponseBody
    @RequestMapping(value = "/del", method = RequestMethod.GET)
    public String del(String token, String filePath, HttpServletRequest request, HttpServletResponse response) throws Exception {
        if (ACTIVE_PRO.equals(active)) {
            return "";
        }
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        List<String> path = Lists.newArrayList();
//        path.add("bbs/oss/123.png");
        path.add(filePath);
//        ossService.del(path);
        fileCloudService.del(path);
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, ""));
    }

    @ResponseBody
    @RequestMapping(value = "/testEmailRemind", method = RequestMethod.GET)
    public String testEmailRemind(String token, String userName, HttpServletRequest request, HttpServletResponse response) throws Exception {
        if (ACTIVE_PRO.equals(active)) {
            return "";
        }
        String finalUserName = Optional.ofNullable(userName).orElse("MRkSdqO6ngkfohYpDno");
        LongStream.range(0, 1).forEach(o -> EmailRemindService.addEmailRemind(new EmailRemind(o, o, "businessUserName", 10, Optional.of(userManage.query_cache_findUserByUserName(finalUserName)))));

        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, ""));
    }

    @ResponseBody
    @RequestMapping(value = "/uploadRollImg", method = RequestMethod.POST)
    public String uploadRollImg(String token, MultipartFile file, Long id, String url, Integer type, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Optional<EditorTag> editorTagOptional = Optional.ofNullable(settingManage.readEditorTag());
        RollImg rollImg = Optional.ofNullable(id).map(o -> rollImgService.getById(o).orElse(null)).orElseGet(() -> new RollImg());
        type = Optional.ofNullable(type).orElseGet(() -> rollImg.getType());
        String srcJson = ossFileChangeService.upload(errorMessageService -> {
            Optional<SystemSetting> systemSetting = Optional.ofNullable(settingService.findSystemSetting_cache());
            // 只读模式不允许提交数据
            systemSetting.map(SystemSetting::getCloseSite).filter(o -> !o.equals(2)).orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0001));
        }, file, editorTagOptional, FileConstant.FILE_TYPE_IMG, Optional.of(String.format("b1")), "rollImg", Optional.ofNullable(type).map(String::valueOf));
        Map<String, Object> map = JSON.parseObject(srcJson, Map.class);
        if ((Integer) map.get("error") == 1) {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, map.get("error")));
        }
        String src = (String) map.get("url");
        rollImg.setSrc(src);
        Optional.ofNullable(url).filter(Strings::isNotEmpty).ifPresent(rollImg::setUrl);
        Optional.ofNullable(type).ifPresent(rollImg::setType);
        if (null == rollImg.getId()) {
            rollImgService.add(rollImg);
        } else {
            rollImgService.edit(rollImg);
        }
        HomeStaticAction.url = rollImgService.findAllList();
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, HomeStaticAction.url));
    }

    @ResponseBody
    @RequestMapping(value = "/delRollImg", method = RequestMethod.POST)
    public String uploadRollImg(String token, Long id, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Optional.ofNullable(id).ifPresent(rollImgService::delById);
        HomeStaticAction.url = rollImgService.findAllList();
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, HomeStaticAction.url));
    }

    @ResponseBody
    @RequestMapping(value = "/refreshEmailRemindSetting", method = RequestMethod.POST)
    public String refreshEmailRemindSetting(String token, Long id, HttpServletRequest request, HttpServletResponse response) throws Exception {
        emailRemindSettingService.updateSystemSetting();
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, emailRemindSettingService.test()));
    }

    @ResponseBody
    @RequestMapping(value = "/findEmailRemindSetting", method = RequestMethod.GET)
    public String findEmailRemindSetting(String token, Long id, HttpServletRequest request, HttpServletResponse response) throws Exception {
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, emailRemindSettingService.findEmailRemindSetting_cache()));
//        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, emailRemindService.findEmailByFollow(16L, "5lRXXNOL7hOXnlWEuS0", 0, 30)));
    }

    @ResponseBody
    @RequestMapping(value = "/reParseSettings", method = RequestMethod.GET)
    public String reParseSettings(String token, @RequestParam(required = true) Boolean attribute, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Field field = ParseSettings.class.getDeclaredField("htmlDefault");
        field.setAccessible(true);
        Field modifiersField = Field.class.getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.set(field, field.getModifiers() & ~Modifier.FINAL);
        field.set(null, new ParseSettings(false, attribute));
        modifiersField.set(field, field.getModifiers() & Modifier.FINAL);
        field.setAccessible(false);
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, ParseSettings.htmlDefault.preserveAttributeCase()));
    }

    @ResponseBody
    @RequestMapping(value = "/getSign", method = RequestMethod.GET)
    public Map<String, String> getSign(@RequestParam("clientSecret") String clientSecret, HttpServletRequest request, HttpServletResponse response) throws Exception {
        long timestamp = System.currentTimeMillis() / 1000;
        String a = timestamp + "-" + BbsInnerApiAuthUtil.randStr(5);
        String rightSign = BbsInnerApiAuthUtil.genHeaderSign(clientSecret, a);

        Map<String, String> info = Maps.newHashMap();
        info.put("sign", rightSign);
        info.put("stamp", a);
        return info;
    }

    @ResponseBody
    @RequestMapping(value = "/reOperation/{operationId}", method = RequestMethod.GET)
    public RequestResult reOperation(@PathVariable long operationId) {
        operationTagService.reByOperationId(operationId);
        return RequestResult.ok("");
    }

    @ResponseBody
    @RequestMapping(value = "/reInnerApi", method = RequestMethod.GET)
    public RequestResult reInnerApi() {
        operationTagService.reInnerApi();
        return RequestResult.ok("");
    }

    @ResponseBody
    @RequestMapping(value = "/initOperationInfo", method = RequestMethod.POST)
    @Transactional(propagation = Propagation.REQUIRED)
    public RequestResult initOperationInfo(@RequestParam("operationId") Long operationId, @RequestParam(name = "tagIds", required = false) Long[] tagIds, @RequestParam(name = "topicId", required = false) Long topicId) {
        Optional.ofNullable(tagIds).filter(ar -> ar.length > 0).ifPresent(ar -> {
            operationTagService.delByOperationId(operationId);
            operationTagService.add(operationId, ListUtil.of(ar));
        });
        Optional.ofNullable(topicId).ifPresent(o -> {
            operationTopicService.delByOperationId(operationId, Boolean.TRUE);
            operationTopicService.add(operationId.longValue(), o.longValue(), Boolean.TRUE.booleanValue());
        });
        return RequestResult.ok("");
    }

    @ResponseBody
    @RequestMapping(value = "/testEmail", method = RequestMethod.GET)
    public RequestResult reOperation() throws UnsupportedEncodingException, MessagingException, GeneralSecurityException {
        EmailUtil.send("<EMAIL>", "测试", "测试");
        return RequestResult.ok("");
    }

    @ResponseBody
    @RequestMapping(value = "/redirect", method = RequestMethod.GET)
    public Map<String,Object> addTopicComment(HttpServletRequest request, HttpServletResponse response) throws IOException {
        return userLoginService.loginByAccessUser(request);
    }
}
