package cms.service.topic;

import cms.bean.QueryResult;
import cms.bean.platformShare.TopicUnhidePlatformShare;
import cms.bean.redEnvelope.GiveRedEnvelope;
import cms.bean.topic.Topic;
import cms.bean.topic.TopicSize;
import cms.bean.topic.TopicTagAssociation;
import cms.bean.topic.TopicUnhide;
import cms.service.besa.DAO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 话题
 */
public interface TopicService extends DAO<Topic> {
    /**
     * 根据Id查询话题
     *
     * @param topicId 话题Id
     * @return
     */
    Topic findById(Long topicId);

    /**
     * 根据Id集合查询话题
     *
     * @param topicIdList 话题Id集合
     * @return
     */
    List<Topic> findByIdList(List<Long> topicIdList);

    /**
     * 根据Id集合查询话题标题
     *
     * @param topicIdList 话题Id集合
     * @return
     */
    List<Topic> findTitleByIdList(List<Long> topicIdList);

    /**
     * 根据话题Id集合查询话题
     *
     * @param topicIdList 话题Id集合
     * @return
     */
    List<Topic> findTopicByTopicIdList(List<Long> topicIdList);

    /**
     * 分页查询话题内容
     *
     * @param firstIndex
     * @param maxResult
     * @param userName   用户名称
     * @param isStaff    是否为员工
     * @return
     */
    Map<Long, String> findTopicContentByPage(int firstIndex, int maxResult, String userName, boolean isStaff);

    /**
     * 分页查询话题
     *
     * @param firstIndex 开始索引
     * @param maxResult  需要获取的记录数
     * @return
     */
    List<Topic> findTopicByPage(int firstIndex, int maxResult);

    /**
     * 分页查询话题
     *
     * @param userName用户名称
     * @param postTime     话题发表时间
     * @param firstIndex   开始索引
     * @param maxResult    需要获取的记录数
     * @return
     */
    List<Topic> findTopicByPage(String userName, Date postTime, int firstIndex, int maxResult);

    Optional<List<Long>> findIdByPageAndLtLastUpdateTime(Date ltLastUpdateTime, int firstIndex, int maxResult);

    List<Topic> findEssenceTop10();

    Long findTotalPostNum(String username);

    /**
     * 保存话题
     *
     * @param topic
     * @param userName 用户名称
     */
    void saveTopic(Topic topic, List<TopicTagAssociation> topicTagAssociations, String userName);

    /**
     * 修改话题
     *
     * @param topic
     * @return
     */
    Integer updateTopic(Topic topic, List<TopicTagAssociation> topicTagAssociations);

    /**
     * 前台用户修改话题
     *
     * @param topic
     * @return
     */
    Integer updateTopic2(Topic topic, List<TopicTagAssociation> topicTagAssociations);

    Integer updateTopicByFilterKeyWord(Long id, Date eqLastUpdateTime, String title, String summary, String contentFilter);

    /**
     * 还原话题
     *
     * @param topicList 话题集合
     * @return
     */
    Integer reductionTopic(List<Topic> topicList);

    /**
     * 标记删除话题
     *
     * @param topicId 话题Id
     * @return
     */
    Integer markDelete(Long topicId);

    /**
     * 修改话题最后回复时间
     *
     * @param topicId       话题Id
     * @param lastReplyTime 最后回复时间
     * @return
     */
    Integer updateTopicReplyTime(Long topicId, Date lastReplyTime);

    /**
     * 删除话题
     *
     * @param topicId          话题Id
     * @param giveRedEnvelope  发红包
     * @param userName         用户名称
     * @param amount           返还用户金额
     * @param paymentLogObject 支付日志
     * @return
     */
    Integer deleteTopic(Long topicId, GiveRedEnvelope giveRedEnvelope, String userName, BigDecimal amount, Object paymentLogObject);

    /**
     * 查询待审核话题数量
     *
     * @return
     */
    Long auditTopicCount();

    /**
     * 增加展示次数
     *
     * @param countMap key: 话题Id value:展示次数
     * @return
     */
    int addViewCount(Map<Long, Long> countMap);

    /**
     * 修改话题状态
     *
     * @param topicId 话题Id
     * @param status  状态
     * @return
     */
    int updateTopicStatus(Long topicId, Integer status);

    /**
     * 增加总评论数
     *
     * @param topicId  话题Id
     * @param quantity 数量
     * @return
     */
    int addCommentTotal(Long topicId, Long quantity);

    /**
     * 减少总评论数
     *
     * @param topicId  话题Id
     * @param quantity 数量
     * @return
     */
    int subtractCommentTotal(Long topicId, Long quantity);

    /**
     * 保存'话题取消隐藏'
     *
     * @param topicUnhide
     * @param hideTagType                 隐藏标签类型
     * @param point                       积分
     * @param consumption_userName        消费用户名称
     * @param consumption_pointLogObject  消费积分日志
     * @param income_userName             收入用户名称
     * @param income_pointLogObject       收入积分日志
     * @param consumption_amount          消费金额
     * @param consumption_paymentLogObjec 用户消费金额日志
     * @param income_paymentLogObject     用户收入金额日志
     * @param userShare_amount            用户分成金额
     * @param topicUnhidePlatformShare    平台分成
     */
    void saveTopicUnhide(Object topicUnhide, Integer hideTagType, Long point, String consumption_userName, Object consumption_pointLogObject, String income_userName, Object income_pointLogObject,
                         BigDecimal consumption_amount, BigDecimal userShare_amount, Object consumption_paymentLogObject, Object income_paymentLogObject, TopicUnhidePlatformShare topicUnhidePlatformShare);

    /**
     * 根据Id查询'话题取消隐藏'
     *
     * @param topicUnhideId 话题取消隐藏Id
     * @return
     */
    TopicUnhide findTopicUnhideById(String topicUnhideId);

    /**
     * 根据话题Id查询话题取消隐藏用户列表
     *
     * @param firstIndex 索引开始,即从哪条记录开始
     * @param maxResult  获取多少条数据
     * @param topicId    话题Id
     * @return
     */
    QueryResult<TopicUnhide> findTopicUnhidePageByTopicId(int firstIndex, int maxResult, Long topicId);

    int updateEssence(Long topicId, boolean essence);

    TopicSize findTopicSizeByTag(Long tagId);
}
