package cms.bean.topic;

import cms.utils.serialize.TopicTagAssociationSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/4/19 18:12
 */
@Entity
@Table(indexes = {@Index(name = "index_topicId", columnList = "topicId"), @Index(name = "index_tagId", columnList = "tagId")})
@Data
@JsonSerialize(using = TopicTagAssociationSerialize.class)
public class TopicTagAssociation implements Serializable {

    private static final long serialVersionUID = -684257451052921860L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long topicId;

    private Long tagId;

    @Transient
    private String tagName;

    @Transient
    private Integer grade;

    @Column(length = 30)
    private String userName;

    public TopicTagAssociation() {

    }

    public TopicTagAssociation(Long tagId, String userName) {
        this.tagId = tagId;
        this.userName = userName;
    }
}
