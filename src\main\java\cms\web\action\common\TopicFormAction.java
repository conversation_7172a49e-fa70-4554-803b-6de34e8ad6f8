package cms.web.action.common;


import cms.bean.ErrorView;
import cms.bean.RequestResult;
import cms.bean.ResultCode;
import cms.bean.message.EmailRemind;
import cms.bean.message.Remind;
import cms.bean.payment.PaymentLog;
import cms.bean.platformShare.TopicUnhidePlatformShare;
import cms.bean.setting.EditorTag;
import cms.bean.setting.SystemSetting;
import cms.bean.topic.HideTagType;
import cms.bean.topic.Topic;
import cms.bean.topic.TopicUnhide;
import cms.bean.user.AccessUser;
import cms.bean.user.PointLog;
import cms.bean.user.User;
import cms.constant.ErrorCode;
import cms.constant.enums.TopicTypeEnums;
import cms.handle.CustomException;
import cms.handle.MsgException;
import cms.service.aliyun.OssFileChangeService;
import cms.service.aliyun.OssService;
import cms.service.message.RemindService;
import cms.service.message.impl.EmailRemindService;
import cms.service.messageSource.ErrorMessageService;
import cms.service.setting.SettingService;
import cms.service.template.TemplateService;
import cms.service.topic.TopicService;
import cms.service.topic.impl.TopicFormService;
import cms.service.topic.impl.TopicOpService;
import cms.service.user.UserService;
import cms.utils.Base64;
import cms.utils.JsonUtils;
import cms.utils.RefererCompare;
import cms.utils.WebUtil;
import cms.utils.threadLocal.AccessUserThreadLocal;
import cms.web.action.AccessSourceDeviceManage;
import cms.web.action.CSRFTokenManage;
import cms.web.action.TextFilterManage;
import cms.web.action.message.RemindManage;
import cms.web.action.payment.PaymentManage;
import cms.web.action.setting.SettingManage;
import cms.web.action.topic.TopicManage;
import cms.web.action.user.PointManage;
import cms.web.action.user.UserManage;
import cms.web.taglib.Configuration;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cms.constant.enums.RemindTypeEnums.UNLOCK_MY_TOPIC;

/**
 * 话题接收表单
 */
@Controller
@RequestMapping("user/control/topic")
@Slf4j
@Validated()
public class TopicFormAction {
    @Resource
    TemplateService templateService;

    @Resource
    AccessSourceDeviceManage accessSourceDeviceManage;

    @Resource
    TextFilterManage textFilterManage;
    @Resource
    SettingManage settingManage;
    @Resource
    SettingService settingService;
    @Resource
    UserService userService;
    @Resource
    TopicService topicService;
    @Autowired
    private TopicOpService topicOpService;
    @Resource
    PointManage pointManage;

    @Resource
    CSRFTokenManage csrfTokenManage;
    @Resource
    TopicManage topicManage;
    @Resource
    UserManage userManage;

    @Resource
    RemindService remindService;
    @Resource
    RemindManage remindManage;
    @Resource
    PaymentManage paymentManage;
    @Autowired
    private TopicFormService topicFormService;
    @Autowired
    private OssFileChangeService ossFileChangeService;
    @Autowired
    private ErrorMessageService errorMessageService;

    /**
     * 话题  添加
     *
     * @param model
     * @param tagId    标签Id
     * @param title    标题
     * @param content  内容
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public String add(ModelMap model, Long tagId, @RequestParam("twoTagId[]") Long[] twoTagId, String title, String content,
                      String token, String captchaKey, String captchaValue, String jumpUrl,
                      Integer sort,
                      RedirectAttributes redirectAttrs,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {
        return topicFormService.add(model, tagId, twoTagId, title, content, token, captchaKey, captchaValue, jumpUrl, sort, redirectAttrs, request, response);
    }


    /**
     * 话题  修改
     *
     * @param model
     * @param topicId  话题Id
     * @param title    标题
     * @param content  内容
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    public String edit(ModelMap model, Long topicId, Long tagId, @RequestParam("twoTagId[]") Long[] twoTagId, String title, String content,
                       String token, String captchaKey, String captchaValue, String jumpUrl,
                       RedirectAttributes redirectAttrs,
                       HttpServletRequest request, HttpServletResponse response) throws Exception {
        return topicFormService.edit(model, topicId, tagId, twoTagId, title, content, token, captchaKey, captchaValue, jumpUrl, redirectAttrs, request, response);
    }


    /**
     * 文件上传
     *
     * @param dir:     上传类型，分别为image、file、media
     * @param fileName 文件名称 预签名时有值
     *                 员工发话题 上传文件名为UUID + a + 员工Id
     *                 用户发话题 上传文件名为UUID + b + 用户Id
     */
    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    @ResponseBody//方式来做ajax,直接返回字符串
    public String upload(ModelMap model, String dir, String fileName,
                         MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {

        Optional<EditorTag> editorTagOptional = Optional.ofNullable(settingManage.readTopicEditorTag());
        Optional<AccessUser> accessUser = Optional.ofNullable(AccessUserThreadLocal.get());
        String userName = accessUser.map(AccessUser::getUserName).orElse("");
        Optional<User> user = Optional.ofNullable(userManage.query_cache_findUserByUserName(userName));
        String date = new DateTime().toString("yyyy-MM-dd");
        return ossFileChangeService.upload(errorMessageService -> {
            Optional<SystemSetting> systemSetting = Optional.ofNullable(settingService.findSystemSetting_cache());
            // 只读模式不允许提交数据
            systemSetting.map(SystemSetting::getCloseSite).filter(o -> !o.equals(2)).orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0001));
            //如果全局不允许提交,如果实名用户才允许提交提问
            systemSetting.filter(o -> o.isAllowTopic()).filter(o -> o.isRealNameUserAllowTopic() == Boolean.FALSE || user.map(User::isRealNameAuthentication).orElse(Boolean.TRUE)).orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0017));
        }, file, editorTagOptional, dir, accessUser.map(o -> String.format("b%s", o.getUserId())), "topic", Optional.ofNullable(dir).filter(Strings::isNotEmpty).map(o -> new StringBuffer(date).append(OssService.separator).append(o).toString()));
    }


    /**
     * 话题  取消隐藏
     *
     * @param model
     * @param topicId       话题Id
     * @param hideType      隐藏类型
     * @param password      密码
     * @param token
     * @param jumpUrl
     * @param redirectAttrs
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/unhide", method = RequestMethod.POST)
    public String topicUnhide(ModelMap model, Long topicId, Integer hideType, String password,
                              String token, String jumpUrl,
                              RedirectAttributes redirectAttrs,
                              HttpServletRequest request, HttpServletResponse response) throws Exception {
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据


        Map<String, String> error = new HashMap<String, String>();
        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getCloseSite().equals(2)) {
            error.put("topicUnhide", ErrorView._21.name());//只读模式不允许提交数据
        }


        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token, error);

        //统计每分钟原来提交次数
        Integer quantity = settingManage.getSubmitQuantity("topicUnhide", accessUser.getUserName());
        if (quantity != null && quantity > 30) {//如果每分钟提交超过30次，则一分钟内不允许'取消隐藏'
            error.put("topicUnhide", ErrorView._1640.name());//提交过于频繁，请稍后再提交
        }

        Topic topic = null;
        if (error.size() == 0) {
            if (topicId != null && topicId > 0L) {

                topic = topicManage.queryTopicCache(topicId);//查询缓存


            } else {
                error.put("topicUnhide", ErrorView._103.name());//话题Id不能为空
            }

            if (topic.getUserName().equals(accessUser.getUserName())) {
                error.put("topicUnhide", ErrorView._1690.name());//不允许解锁自已发表的话题
            }

            if (topic != null) {
                //话题取消隐藏Id
                String topicUnhideId = topicManage.createTopicUnhideId(accessUser.getUserId(), hideType, topicId);

                TopicUnhide topicUnhide = topicManage.query_cache_findTopicUnhideById(topicUnhideId);

                if (topicUnhide != null) {
                    error.put("topicUnhide", ErrorView._1610.name());//当前话题已经取消隐藏
                }


            }
        }

        //消费积分
        Long point = null;
        //消费金额
        BigDecimal amount = null;

        List<Integer> hideTypeList = new ArrayList<Integer>();
        hideTypeList.add(HideTagType.PASSWORD.getName());
        hideTypeList.add(HideTagType.POINT.getName());
        hideTypeList.add(HideTagType.AMOUNT.getName());

        if (!hideTypeList.contains(hideType)) {
            error.put("topicUnhide", ErrorView._1620.name());//隐藏标签不存在
        }


        if (error.size() == 0) {
            //解析隐藏标签
            Map<Integer, Object> analysisHiddenTagMap = textFilterManage.analysisHiddenTag(topic.getContent());
            if (!analysisHiddenTagMap.containsKey(hideType)) {
                error.put("topicUnhide", ErrorView._1660.name());//话题内容不含当前标签
            }

            for (Map.Entry<Integer, Object> entry : analysisHiddenTagMap.entrySet()) {
                if (entry.getKey().equals(HideTagType.PASSWORD.getName()) && HideTagType.PASSWORD.getName().equals(hideType)) {//输入密码可见
                    if (password == null || "".equals(password.trim())) {
                        error.put("password", ErrorView._1650.name());//密码不能为空
                        break;
                    }


                    if (!entry.getValue().equals(password)) {
                        error.put("topicUnhide", ErrorView._1630.name());//密码错误
                    }
                    break;
                }

                if (entry.getKey().equals(HideTagType.POINT.getName()) && HideTagType.POINT.getName().equals(hideType)) {//积分购买可见
                    //获取登录用户
                    User _user = userService.findUserByUserName(accessUser.getUserName());
                    if (_user != null) {
                        if (_user.getPoint() < (Long) entry.getValue()) {
                            error.put("topicUnhide", ErrorView._1680.name());//用户积分不足
                        } else {
                            point = (Long) entry.getValue();
                        }
                    } else {
                        error.put("topicUnhide", ErrorView._1670.name());//用户不存在
                    }

                }

                if (entry.getKey().equals(HideTagType.AMOUNT.getName()) && HideTagType.AMOUNT.getName().equals(hideType)) {//余额购买可见
                    //获取登录用户
                    User _user = userService.findUserByUserName(accessUser.getUserName());
                    if (_user != null) {
                        if (_user.getDeposit().compareTo((BigDecimal) entry.getValue()) < 0) {
                            error.put("topicUnhide", ErrorView._1685.name());//用户余额不足
                        } else {
                            amount = (BigDecimal) entry.getValue();
                        }
                    } else {
                        error.put("topicUnhide", ErrorView._1670.name());//用户不存在
                    }


                }

            }
        }

        //统计每分钟原来提交次数
        Integer original = settingManage.getSubmitQuantity("topicUnhide", accessUser.getUserName());
        if (original != null) {
            settingManage.addSubmitQuantity("topicUnhide", accessUser.getUserName(), original + 1);//刷新每分钟原来提交次数
        } else {
            settingManage.addSubmitQuantity("topicUnhide", accessUser.getUserName(), 1);//刷新每分钟原来提交次数
        }


        if (error.size() == 0) {
            Date time = new Date();
            TopicUnhide topicUnhide = new TopicUnhide();
            String topicUnhideId = topicManage.createTopicUnhideId(accessUser.getUserId(), hideType, topicId);
            topicUnhide.setId(topicUnhideId);
            topicUnhide.setUserName(accessUser.getUserName());
            topicUnhide.setCancelTime(new Date());
            topicUnhide.setHideTagType(hideType);
            topicUnhide.setPostUserName(topic.getUserName());//发布话题的用户名称
            topicUnhide.setTopicId(topicId);

            //用户消费积分日志
            Object consumption_pointLogObject = null;
            //用户收入积分日志
            Object income_pointLogObject = null;
            if (point != null) {
                topicUnhide.setPoint(point);

                PointLog pointLog = new PointLog();
                pointLog.setId(pointManage.createPointLogId(accessUser.getUserId()));
                pointLog.setModule(400);//400.积分购买隐藏话题
                pointLog.setParameterId(topic.getId());//参数Id
                pointLog.setOperationUserType(2);//操作用户类型  0:系统  1: 员工  2:会员
                pointLog.setOperationUserName(accessUser.getUserName());//操作用户名称
                pointLog.setPointState(2);//2:账户支出
                pointLog.setPoint(point);//积分
                pointLog.setUserName(accessUser.getUserName());//用户名称
                pointLog.setRemark("");
                pointLog.setTimes(time);
                consumption_pointLogObject = pointManage.createPointLogObject(pointLog);

                if (!topic.getIsStaff()) {//如果是用户
                    User _user = userManage.query_cache_findUserByUserName(topic.getUserName());
                    PointLog income_pointLog = new PointLog();
                    income_pointLog.setId(pointManage.createPointLogId(_user.getId()));
                    income_pointLog.setModule(400);//400.积分购买隐藏话题
                    income_pointLog.setParameterId(topic.getId());//参数Id
                    income_pointLog.setOperationUserType(2);//操作用户类型  0:系统  1: 员工  2:会员
                    income_pointLog.setOperationUserName(accessUser.getUserName());//操作用户名称
                    income_pointLog.setPointState(1);//1:账户存入
                    income_pointLog.setPoint(point);//积分
                    income_pointLog.setUserName(topic.getUserName());//用户名称
                    income_pointLog.setRemark("");
                    income_pointLog.setTimes(time);
                    income_pointLogObject = pointManage.createPointLogObject(income_pointLog);

                    //删除用户缓存
                    userManage.delete_cache_findUserById(_user.getId());
                    userManage.delete_cache_findUserByUserName(topic.getUserName());

                }

            }

            //用户消费金额日志
            Object consumption_paymentLogObject = null;
            //用户收入金额日志
            Object income_paymentLogObject = null;
            //平台分成
            TopicUnhidePlatformShare topicUnhidePlatformShare = null;

            //发布话题用户分成金额
            BigDecimal postUserNameShareAmount = new BigDecimal("0");
            //平台分成金额
            BigDecimal platformShareAmount = new BigDecimal("0");
            if (amount != null) {
                topicUnhide.setAmount(amount);


                Integer topicUnhidePlatformShareProportion = settingService.findSystemSetting().getTopicUnhidePlatformShareProportion();//平台分成比例

                if (topicUnhidePlatformShareProportion > 0) {
                    //平台分成金额 = 总金额 * (平台分成比例 /100)
                    platformShareAmount = amount.multiply(new BigDecimal(String.valueOf(topicUnhidePlatformShareProportion)).divide(new BigDecimal("100")));

                    postUserNameShareAmount = amount.subtract(platformShareAmount);
                } else {
                    postUserNameShareAmount = amount;
                }


                PaymentLog paymentLog = new PaymentLog();
                paymentLog.setPaymentRunningNumber(paymentManage.createRunningNumber(accessUser.getUserId()));//支付流水号
                paymentLog.setPaymentModule(70);//支付模块 7.余额购买话题隐藏内容
                paymentLog.setSourceParameterId(String.valueOf(topic.getId()));//参数Id
                paymentLog.setOperationUserType(2);//操作用户类型  0:系统  1: 员工  2:会员
                paymentLog.setOperationUserName(accessUser.getUserName());//操作用户名称  0:系统  1: 员工  2:会员
                paymentLog.setAmountState(2);//金额状态  1:账户存入  2:账户支出
                paymentLog.setAmount(amount);//金额
                paymentLog.setInterfaceProduct(0);//接口产品
                paymentLog.setUserName(accessUser.getUserName());//用户名称
                paymentLog.setTimes(time);
                consumption_paymentLogObject = paymentManage.createPaymentLogObject(paymentLog);

                if (!topic.getIsStaff()) {//如果是用户
                    User _user = userManage.query_cache_findUserByUserName(topic.getUserName());
                    String paymentRunningNumber = paymentManage.createRunningNumber(_user.getId());//支付流水号
                    PaymentLog income_paymentLog = new PaymentLog();
                    income_paymentLog.setPaymentRunningNumber(paymentRunningNumber);//支付流水号
                    income_paymentLog.setPaymentModule(80);//支付模块 80.余额购买话题隐藏内容分成收入
                    income_paymentLog.setSourceParameterId(String.valueOf(topic.getId()));//参数Id
                    income_paymentLog.setOperationUserType(2);//操作用户类型  0:系统  1: 员工  2:会员
                    income_paymentLog.setOperationUserName(accessUser.getUserName());//操作用户名称  0:系统  1: 员工  2:会员
                    income_paymentLog.setAmountState(1);//金额状态  1:账户存入  2:账户支出
                    income_paymentLog.setAmount(postUserNameShareAmount);//金额
                    income_paymentLog.setInterfaceProduct(0);//接口产品
                    income_paymentLog.setUserName(topic.getUserName());//用户名称
                    income_paymentLog.setTimes(time);
                    income_paymentLogObject = paymentManage.createPaymentLogObject(income_paymentLog);

                    if (topicUnhidePlatformShareProportion > 0) {
                        //平台分成
                        topicUnhidePlatformShare = new TopicUnhidePlatformShare();
                        topicUnhidePlatformShare.setTopicId(topic.getId());//话题Id
                        topicUnhidePlatformShare.setStaff(topic.getIsStaff());//分成用户是否为员工
                        topicUnhidePlatformShare.setPostUserName(topic.getUserName());//发布话题的用户名称
                        topicUnhidePlatformShare.setUnlockUserName(accessUser.getUserName());//解锁话题的用户名称
                        topicUnhidePlatformShare.setPlatformShareProportion(topicUnhidePlatformShareProportion);//平台分成比例
                        topicUnhidePlatformShare.setPostUserShareRunningNumber(paymentRunningNumber);//发布话题的用户分成流水号
                        topicUnhidePlatformShare.setTotalAmount(amount);//总金额
                        topicUnhidePlatformShare.setShareAmount(platformShareAmount);//平台分成金额
                        topicUnhidePlatformShare.setUnlockTime(time);//解锁时间
                    }


                    //删除用户缓存
                    userManage.delete_cache_findUserById(_user.getId());
                    userManage.delete_cache_findUserByUserName(topic.getUserName());
                }
            }

            try {
                //保存'话题取消隐藏'
                topicService.saveTopicUnhide(topicManage.createTopicUnhideObject(topicUnhide), hideType,
                        point, accessUser.getUserName(), consumption_pointLogObject, topic.getUserName(), income_pointLogObject,
                        amount, postUserNameShareAmount, consumption_paymentLogObject, income_paymentLogObject, topicUnhidePlatformShare);

                //删除'话题取消隐藏'缓存;
                topicManage.delete_cache_findTopicUnhideById(topicUnhideId);

                //删除用户缓存
                userManage.delete_cache_findUserById(accessUser.getUserId());
                userManage.delete_cache_findUserByUserName(accessUser.getUserName());


                User _user = userManage.query_cache_findUserByUserName(topic.getUserName());

                //别人解锁了我的话题  只对(隐藏标签类型 10:输入密码可见  40:积分购买可见  50:余额购买可见)发提醒
                if (!topic.getIsStaff() && _user != null && !topic.getUserName().equals(accessUser.getUserName())) {//不发提醒给自己

                    //提醒楼主
                    Remind remind = new Remind();
                    remind.setId(remindManage.createRemindId(_user.getId()));
                    remind.setReceiverUserId(_user.getId());//接收提醒用户Id
                    remind.setSenderUserId(accessUser.getUserId());//发送用户Id
                    remind.setTypeCode(UNLOCK_MY_TOPIC.getCode());//60:别人解锁了我的话题
                    remind.setSendTimeFormat(new Date().getTime());//发送时间格式化
                    remind.setTopicId(topic.getId());//话题Id

                    Object remind_object = remindManage.createRemindObject(remind);
                    remindService.saveRemind(remind_object);
                    Optional.ofNullable(_user).filter(User::getEmailRemind).ifPresent(o -> EmailRemindService.addEmailRemind(new EmailRemind(topicId, accessUser.getUserId(), accessUser.getUserName(), UNLOCK_MY_TOPIC.getCode(), Optional.of(o))));

                    //删除提醒缓存
                    remindManage.delete_cache_findUnreadRemindByUserId(_user.getId());


                }
            } catch (org.springframework.orm.jpa.JpaSystemException e) {
                error.put("topicUnhide", ErrorView._1600.name());//话题重复取消隐藏

            }

        }


        Map<String, String> returnError = new HashMap<String, String>();//错误
        if (error.size() > 0) {
            //将枚举数据转为错误提示字符
            for (Map.Entry<String, String> entry : error.entrySet()) {
                if (ErrorView.get(entry.getValue()) != null) {
                    returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                } else {
                    returnError.put(entry.getKey(), entry.getValue());
                }
            }
        }
        if (isAjax == true) {

            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

            if (error != null && error.size() > 0) {
                returnValue.put("success", "false");
                returnValue.put("error", returnError);

            } else {
                returnValue.put("success", "true");
            }
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {


            if (error != null && error.size() > 0) {//如果有错误
                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addFlashAttribute("hideType", hideType);


                String referer = request.getHeader("referer");


                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";

                return "redirect:/" + referer + queryString;

            }


            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());

                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "话题取消隐藏成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);

                String dirName = templateService.findTemplateDir_cache();


                return "templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }


    /**
     * 删除话题
     *
     * @param model
     * @param topicIds
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/delByIds", method = RequestMethod.POST)
    public String delByIds(ModelMap model, @RequestParam("topicIds[]") Long[] topicIds,
                           String token, String jumpUrl,
                           HttpServletRequest request, HttpServletResponse response) throws IOException {
        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据
        Map<String, String> error = new HashMap<String, String>();
        SystemSetting systemSetting = settingService.findSystemSetting_cache();


        try {
            if (systemSetting.getCloseSite().equals(2)) {

                error.put("topic", ErrorView._21.name());//只读模式不允许提交数据
            }

            //处理CSRF令牌
            csrfTokenManage.processCsrfToken(request, token);

            Optional.ofNullable(topicIds).filter(o -> o.length > 0).map(Arrays::asList).ifPresent(topicOpService::delete);
        } catch (CustomException e) {
            Map<String, String> returnError = new HashMap<String, String>();//错误
            returnError.put(e.getFiledName(), errorMessageService.getMessage(e.getCode()));
            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值
            returnValue.put("success", "false");
            returnValue.put("error", returnError);
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } catch (MsgException e) {
            Map<String, String> returnError = new HashMap<String, String>();//错误
            returnError.put("topic", e.getMsg());
            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值
            returnValue.put("success", "false");
            returnValue.put("error", returnError);
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        }

        if (isAjax == true) {
            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值
            returnValue.put("success", "true");
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {
            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());

                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "修改话题成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);

                String dirName = templateService.findTemplateDir_cache();


                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }


    /**
     * 删除话题恢复
     *
     * @param model
     * @param topicIds
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/unDelByIds", method = RequestMethod.POST)
    public String unDelByIds(ModelMap model, Long[] topicIds,
                             String token, String jumpUrl,
                             HttpServletRequest request, HttpServletResponse response) throws IOException {
        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据
        Map<String, String> error = new HashMap<String, String>();
        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getCloseSite().equals(2)) {
            error.put("topic", ErrorView._21.name());//只读模式不允许提交数据
        }

        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);

        Optional.ofNullable(topicIds).filter(o -> o.length > 0).map(Arrays::asList).ifPresent(topicOpService::unDelete);

        if (isAjax == true) {
            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值
            returnValue.put("success", "true");
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {
            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());

                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "修改话题成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);

                String dirName = templateService.findTemplateDir_cache();


                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }


    @ResponseBody
    @RequestMapping(value = "/getTopicTypes", method = RequestMethod.GET)
    public String getTopicTypes(String token, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, TopicTypeEnums.getMap().entrySet().stream().map(m -> {
            Map<String, Object> r = Maps.newHashMap();
            r.put("value", m.getKey());
            r.put("label", m.getValue());
            return r;
        }).collect(Collectors.toList())));
    }
}
