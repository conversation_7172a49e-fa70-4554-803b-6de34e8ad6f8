package cms.service.user;

import cms.bean.user.User;
import cms.bean.user.UserGrade;
import cms.service.besa.DAO;

import java.util.List;

/**
 * 会员等级接口
 */
public interface UserGradeService extends DAO<UserGrade> {
    /**
     * 根据Id查询等级
     *
     * @param userGradeId 等级Id
     * @return
     */
    UserGrade findGradeById(Integer userGradeId);

    /**
     * 根据需要积分查询等级
     *
     * @param needPoint 需要积分
     * @return
     */
    UserGrade findGradeByNeedPoint(Long needPoint);

    /**
     * 查询所有设置的等级
     */
    List<UserGrade> findAllGrade();

    /**
     * 查询所有设置的等级 - 缓存
     */
    List<UserGrade> findAllGrade_cache();

    /**
     *
     * @param user
     * @param userGradeList 根据积分排序好的
     * @return
     */
    Integer getUserGrade(User user, List<UserGrade> userGradeList);

    /**
     * 保存用户等级
     *
     * @param userGrade 用户等级
     */
    void saveUserGrade(UserGrade userGrade);

    /**
     * 修改用户等级
     *
     * @param userGrade 用户等级
     */
    void updateUserGrade(UserGrade userGrade);

    /**
     * 删除用户等级
     *
     * @param userGradeId 用户等级Id
     */
    int deleteUserGrade(Integer userGradeId);
}
