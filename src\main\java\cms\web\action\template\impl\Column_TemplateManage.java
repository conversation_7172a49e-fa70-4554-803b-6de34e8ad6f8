package cms.web.action.template.impl;

import cms.bean.template.Column;
import cms.bean.template.Forum;
import cms.bean.topic.Tag;
import cms.bean.topic.TopicSize;
import cms.service.topic.TagService;
import cms.service.topic.TopicService;
import cms.web.action.template.ColumnManage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 站点栏目 -- 模板方法实现
 */
@Component("column_TemplateManage")
public class Column_TemplateManage {

    @Resource
    ColumnManage columnManage;
    @Resource
    TagService tagService;
    @Resource
    private TopicService topicService;

    /**
     * 站点栏目列表 -- 集合
     *
     * @param forum
     */
    public List<Column> column_collection(Forum forum, Map<String, Object> parameter, Map<String, Object> runtimeParameter) {
        List<Column> columnList = columnManage.columnList_cache(forum.getDirName());
        Map<String, Long> tagNameMap = tagService.findAllTag_cache().stream().filter(o -> o.getGrade() == 1).collect(Collectors.toMap(Tag::getName, Tag::getId));
        columnList.forEach(o -> {
            o.setTagId(tagNameMap.get(o.getName()));
            TopicSize topicSize = topicService.findTopicSizeByTag(o.getTagId());
            o.setTopicSize(topicSize);
        });
        return columnList;
    }
}
