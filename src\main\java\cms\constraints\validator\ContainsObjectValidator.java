package cms.constraints.validator;

import cms.constraints.ContainsObject;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16 11:32
 */
public class ContainsObjectValidator implements ConstraintValidator<ContainsObject, Integer> {

    private String[] contains;

    @Override
    public void initialize(ContainsObject constraintAnnotation) {
        this.contains = constraintAnnotation.contains();
    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        return Optional.ofNullable(contains).filter(o -> o.length > 1).filter(o -> null != value).map(Arrays::asList).map(o -> o.contains(value.toString())).orElse(true);
    }
}
