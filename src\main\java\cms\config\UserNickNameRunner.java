package cms.config;

import cms.bean.keywordDict.KeywordDict;
import cms.bean.question.QuestionTag;
import cms.service.keywordDict.KeywordDictService;
import cms.service.question.QuestionService;
import cms.service.question.QuestionTagService;
import cms.service.template.impl.RollImgService;
import cms.service.topic.TagService;
import cms.utils.SpringUtils;
import cms.web.action.common.HomeStaticAction;
import cms.web.action.filterWord.SensitiveWordFilterManage;
import cms.web.action.question.QuestionTagManage;
import cms.web.action.topic.TopicManage;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.parser.ParseSettings;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static cms.constant.Constant.QUESTION_TAG_GRADE_ONE;
import static cms.constant.Constant.TOPIC_TAG_GRADE_TWO;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/3 17:31
 */
@Component
@Slf4j
@Order(1)
public class UserNickNameRunner implements ApplicationRunner {

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info(">>>>>>>> start init keyword <<<<<<<<");
        KeywordDictService keywordDictService = SpringUtils.getBean(KeywordDictService.class);
        SensitiveWordFilterManage sensitiveWordFilterManage = SpringUtils.getBean(SensitiveWordFilterManage.class);
        List<String> keyWords = keywordDictService.findAllKeyword_cache().stream().map(KeywordDict::getKeyWord).collect(Collectors.toList());
        sensitiveWordFilterManage.addWords(keyWords);
        log.info("keyword: {}", sensitiveWordFilterManage.getAllKeywords().stream().collect(Collectors.joining(",")));
        log.info(">>>>>>>> end init keyword {} <<<<<<<<", keyWords.size());
        log.info(">>>>>>>> start init rollImg <<<<<<<<");
        RollImgService rollImgService = SpringUtils.getBean(RollImgService.class);
        HomeStaticAction.url = rollImgService.findAllList();
        log.info(">>>>>>>> end init rollImg <<<<<<<<");
        log.info(">>>>>>>> start init two tag topic num <<<<<<<<");
        TagService tagService = SpringUtils.getBean(TagService.class);
        Map<Long, Integer> twoTagTopicCount = tagService.findTwoTagCountTopic();
        TopicManage.initTwoTagNumAdd(tagService.findAllTag_cache().stream().filter(o -> o.getGrade() == TOPIC_TAG_GRADE_TWO).collect(Collectors.toConcurrentMap(o -> o.getName(),
                o -> {
                    ConcurrentMap map = Maps.newConcurrentMap();
                    map.put(o.getId(), Optional.ofNullable(twoTagTopicCount.get(o.getId())).map(AtomicInteger::new).orElseGet(() -> new AtomicInteger(0)));
                    return map;
                }, (a, b) -> {
                    a.putAll(b);
                    return a;
                })));
        log.info(">>>>>>>> end init two tag topic num <<<<<<<<");
        log.info(">>>>>>>> start init one tag Q&A num <<<<<<<<");
        QuestionService questionService = SpringUtils.getBean(QuestionService.class);
        QuestionTagService questionTagService = SpringUtils.getBean(QuestionTagService.class);
        Map<Long, Integer> oneTagQaCount = questionService.findOneTagCount();
        QuestionTagManage.initOneTagNumAdd(questionTagService.findAllQuestionTag_cache().stream().filter(o -> o.getGrade() == QUESTION_TAG_GRADE_ONE)
                .collect(Collectors.toConcurrentMap(QuestionTag::getId, o -> Optional.ofNullable(oneTagQaCount.get(o.getId())).map(AtomicInteger::new).orElseGet(() -> new AtomicInteger(0)))));
        log.info(">>>>>>>> end init one tag Q&A num <<<<<<<<");
        log.info(">>>>>>>> start update htmlDefault <<<<<<<<");
        Field field = ParseSettings.class.getDeclaredField("htmlDefault");
        field.setAccessible(true);
        Field modifiersField = Field.class.getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.set(field, field.getModifiers() & ~Modifier.FINAL);
        field.set(null, new ParseSettings(false, true));
        modifiersField.set(field, field.getModifiers() & Modifier.FINAL);
        field.setAccessible(false);
        log.info(">>>>>>>> end update htmlDefault <<<<<<<<");

        RequestMappingHandlerMapping mapping = SpringUtils.getBean(RequestMappingHandlerMapping.class);
        // 获取url与类和方法的对应信息
        Map<RequestMappingInfo, HandlerMethod> map = mapping.getHandlerMethods();
        for (RequestMappingInfo info : map.keySet()) {
        }
    }
}
