package cms.web.filter;


import cms.web.filter.wrapper.RequestTrimHttpServletRequestWrapper;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/4/21 16:02
 */
public class RequestTrimFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        chain.doFilter(new RequestTrimHttpServletRequestWrapper((HttpServletRequest) request), response);
    }
}
