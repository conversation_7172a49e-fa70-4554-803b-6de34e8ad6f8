package cms.service.keywordDict;

import cms.aspect.FilterKeywordLock;
import cms.bean.setting.EditorTag;
import cms.bean.topic.Comment;
import cms.bean.topic.Reply;
import cms.bean.topic.Topic;
import cms.bean.user.UserGrade;
import cms.handle.MsgException;
import cms.service.topic.CommentService;
import cms.service.topic.TopicService;
import cms.service.user.UserGradeService;
import cms.web.action.TextFilterManage;
import cms.web.action.filterWord.SensitiveWordFilterManage;
import cms.web.action.setting.SettingManage;
import cms.web.action.topic.CommentManage;
import cms.web.action.topic.TopicManage;
import com.google.common.collect.Maps;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static cms.aspect.FilterKeywordLock.LockMapEnum.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/27 17:28
 */
@Service
public class TopicFilterKeyWordService {

    public static Map<Long, Object> topic_update_lock_map = Maps.newConcurrentMap();
    public static Map<Long, Object> comment_update_lock_map = Maps.newConcurrentMap();
    public static Map<Long, Object> reply_update_lock_map = Maps.newConcurrentMap();

    @Autowired
    private TopicService topicService;
    @Resource
    UserGradeService userGradeService;
    @Autowired
    private CommentService commentService;
    @Autowired
    private CommentManage commentManage;
    @Autowired
    private SensitiveWordFilterManage sensitiveWordFilterManage;
    @Resource
    TextFilterManage textFilterManage;
    @Resource
    SettingManage settingManage;
    @Resource
    TopicManage topicManage;

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @FilterKeywordLock(lockKeyIndex = 0, lockMap = TOPIC)
    public void filterKeyWord(Long id, String wordReplace, Date date) {
        Topic topic = Optional.ofNullable(topicService.findById(id)).orElseThrow(() -> new MsgException(String.format("topicId %s not find topic", id)));
        if (Optional.ofNullable(topic.getLastUpdateTime()).orElse(topic.getPostTime()).compareTo(date) > 0) {
            return;
        }

        String title = Optional.of(topic).map(Topic::getTitle).map(o -> sensitiveWordFilterManage.filterSensitiveWord(o, wordReplace)).orElse("");
        //过滤标签
        EditorTag editorTag = settingManage.readTopicEditorTag();
        String content = textFilterManage.filterTag(topic.getContent(), editorTag);
        Object[] object = textFilterManage.filterHtml(null, content, "topic", editorTag);
        String value = (String) object[0];

        List<UserGrade> userGradeList = userGradeService.findAllGrade_cache();
        //校正隐藏标签
        String validValue = textFilterManage.correctionHiddenTag(value, userGradeList);

        //删除隐藏标签
        String new_content = textFilterManage.deleteHiddenTag(value);
        //不含标签内容
        String text = textFilterManage.filterText(textFilterManage.specifyHtmlTagToText(new_content));
        //清除空格&nbsp;
        String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();
        String summary = Optional.ofNullable(trimSpace).filter(Strings::isNotEmpty).map(o -> sensitiveWordFilterManage.filterSensitiveWord(o, wordReplace)).orElse("");

        String contentFilter = Optional.ofNullable(validValue).filter(Strings::isNotEmpty).map(o -> sensitiveWordFilterManage.filterSensitiveWord(o, wordReplace)).orElse("");

        int i = topicService.updateTopicByFilterKeyWord(id, topic.getLastUpdateTime(), title, summary, contentFilter);
        if (i > 0) {
            //删除话题缓存
            topicManage.deleteTopicCache(topic.getId());
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @FilterKeywordLock(lockKeyIndex = 0, lockMap = COMMENT)
    public void filterKeyWordComment(Long id, String wordReplace, Date date) {
        Comment comment = Optional.ofNullable(commentService.findByCommentId(id)).orElseThrow(() -> new MsgException(String.format("commentId %s not find comment", id)));
        if (Optional.ofNullable(comment.getLastUpdateTime()).orElse(comment.getPostTime()).compareTo(date) > 0) {
            return;
        }

        //过滤标签
        String content = textFilterManage.filterTag(comment.getContent(), settingManage.readEditorTag());
        Object[] object = textFilterManage.filterHtml(null, content, "comment", settingManage.readEditorTag());
        String value = (String) object[0];

        value = sensitiveWordFilterManage.filterSensitiveWord(value, wordReplace);

        int i = commentService.updateComment(id, comment.getLastUpdateTime(), value);
        if (i > 0) {
            //删除缓存
            commentManage.delete_cache_findByCommentId(comment.getId());
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @FilterKeywordLock(lockKeyIndex = 0, lockMap = REPLY)
    public void filterKeyWordReply(Long replyId, String wordReplace, Date date) {
        Reply reply = Optional.ofNullable(commentService.findReplyByReplyId(replyId)).orElseThrow(() -> new MsgException(String.format("replyId %s not find reply", replyId)));
        if (Optional.ofNullable(reply.getLastUpdateTime()).orElse(reply.getPostTime()).compareTo(date) > 0) {
            return;
        }

        String text = textFilterManage.filterText(reply.getContent());
        text = sensitiveWordFilterManage.filterSensitiveWord(text, wordReplace);

        int i = commentService.updateReply(replyId, reply.getLastUpdateTime(), text);
        if (i > 0) {
            //删除缓存
            commentManage.delete_cache_findReplyByReplyId(reply.getId());
        }
    }
}
