package cms.bean.operation;

import cms.bean.BaseUserDel;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/25 12:05
 */
@Entity
@Table(name = "operation_tag", indexes = {@Index(name = "operationId_idx", columnList = "operationId")})
@Data
public class OperationTag extends BaseUserDel implements Serializable {

    private static final long serialVersionUID = -2L;

    @Column()
    private Long operationId;

    @Column()
    private Long tagId;
}
