package cms.bean.keywordDict;

import cms.bean.BaseUserDel;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/9 10:40
 */
@Entity
@Table(name = "keywordDict")
@Data
public class KeywordDict extends BaseUserDel implements Serializable {

    private static final long serialVersionUID = -1L;


    /**
     * 关键字
     */
    @Column(length = 100, nullable = false)
    private String keyWord;

}
