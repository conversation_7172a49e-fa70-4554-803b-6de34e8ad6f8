package cms.config;

import com.hy.corecode.idgen.YitIdGenerator;
import com.hy.properties.IdGeneratorOptions;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/9 15:36
 */
@Configuration
public class IdGeneratorConfig {

    @Bean
    public YitIdGenerator yitIdGenerator() {
        //准备基础配置类，在此可以配置基础信息
        IdGeneratorOptions idGeneratorOptions = new IdGeneratorOptions();
        idGeneratorOptions.setWorkerId((short) 6); //设置机器码为6
        idGeneratorOptions.setWorkerIdBitLength((byte) 3); //设置机器码位长为3
        //装载id生成器的配置文件
        return new YitIdGenerator(idGeneratorOptions);
    }
}
