@font-face {
  font-family: 'themify';
  src:  url('fonts/themify.eot?r6qzhc');
  src:  url('fonts/themify.eot?r6qzhc#iefix') format('embedded-opentype'),
    url('fonts/themify.ttf?r6qzhc') format('truetype'),
    url('fonts/themify.woff?r6qzhc') format('woff'),
    url('fonts/themify.svg?r6qzhc#themify') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="cms-"], [class*=" cms-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'themify' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.cms-chat-line:before {
  content: "\e93e";
}
.cms-menu-fold-fill:before {
  content: "\e93c";
}
.cms-menu-unfold-fill:before {
  content: "\e93d";
}
.cms-compass-line:before {
  content: "\e909";
}
.cms-angle-down:before {
  content: "\e900";
}
.cms-announcement:before {
  content: "\e901";
}
.cms-append:before {
  content: "\e902";
}
.cms-bell:before {
  content: "\e903";
}
.cms-check-circle-solid:before {
  content: "\e904";
}
.cms-chess-queen-solid:before {
  content: "\e905";
}
.cms-clipboard:before {
  content: "\e906";
}
.cms-comment:before {
  content: "\e907";
}
.cms-commentCount:before {
  content: "\e908";
}
.cms-control-play:before {
  content: "\e90a";
}
.cms-deposit:before {
  content: "\e90b";
}
.cms-ellipsis-v-solid:before {
  content: "\e90c";
}
.cms-email:before {
  content: "\e90d";
}
.cms-favorites:before {
  content: "\e90e";
}
.cms-follower:before {
  content: "\e90f";
}
.cms-heart:before {
  content: "\e910";
}
.cms-heart-broken:before {
  content: "\e911";
}
.cms-help-alt:before {
  content: "\e912";
}
.cms-home:before {
  content: "\e913";
}
.cms-info-alt:before {
  content: "\e914";
}
.cms-lock-2:before {
  content: "\e915";
}
.cms-lock-solid:before {
  content: "\e916";
}
.cms-lock-solid-2:before {
  content: "\e917";
}
.cms-log:before {
  content: "\e918";
}
.cms-login:before {
  content: "\e919";
}
.cms-membershipCard:before {
  content: "\e91a";
}
.cms-menu:before {
  content: "\e91b";
}
.cms-menu-alt:before {
  content: "\e91c";
}
.cms-order:before {
  content: "\e91d";
}
.cms-pencil:before {
  content: "\e91e";
}
.cms-pencil-alt:before {
  content: "\e91f";
}
.cms-point:before {
  content: "\e920";
}
.cms-pulse:before {
  content: "\e921";
}
.cms-question-answer-line:before {
  content: "\e922";
}
.cms-quote:before {
  content: "\e923";
}
.cms-quote-left-solid:before {
  content: "\e924";
}
.cms-quote-right-solid:before {
  content: "\e925";
}
.cms-realNameAuthentication:before {
  content: "\e926";
}
.cms-recharge:before {
  content: "\e927";
}
.cms-redEnvelope-1:before {
  content: "\e928";
}
.cms-redEnvelope-2:before {
  content: "\e929";
}
.cms-reply:before {
  content: "\e92a";
}
.cms-reply2:before {
  content: "\e92b";
}
.cms-search:before {
  content: "\e92c";
}
.cms-server:before {
  content: "\e92d";
}
.cms-settings:before {
  content: "\e92e";
}
.cms-tag:before {
  content: "\e92f";
}
.cms-themify-check:before {
  content: "\e930";
}
.cms-themify-close:before {
  content: "\e931";
}
.cms-thumb-down:before {
  content: "\e932";
}
.cms-thumb-up:before {
  content: "\e933";
}
.cms-topic:before {
  content: "\e934";
}
.cms-trash:before {
  content: "\e935";
}
.cms-trophy-solid:before {
  content: "\e936";
}
.cms-unlock-solid:before {
  content: "\e937";
}
.cms-unlock-solid-2:before {
  content: "\e938";
}
.cms-user:before {
  content: "\e939";
}
.cms-user2:before {
  content: "\e93a";
}
.cms-view:before {
  content: "\e93b";
}
.cms-caret-right:before {
  content: "\e791";
}
