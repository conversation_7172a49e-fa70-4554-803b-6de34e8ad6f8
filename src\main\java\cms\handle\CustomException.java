package cms.handle;

import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/4/20 14:06
 */
@Data
public class CustomException extends RuntimeException {
    static final long serialVersionUID = -703489719074576693L;

    private int code;
    private Integer defaultCode;
    private String filedName;
    private String msg;
    private Boolean resultCode;

    public CustomException(int code, String filedName) {
        this(code, null, filedName, null);
    }

    public CustomException(int code, Integer defaultCode, String filedName) {
        this(code, defaultCode, filedName, String.format("error code: ", code));
    }

    public CustomException(int code, Integer defaultCode, String filedName, String msg) {
        super(code + "");
        this.code = code;
        this.defaultCode = defaultCode;
        this.filedName = filedName;
        this.msg = msg;
    }
}
