package cms.web.action.keywordDict;

import cms.bean.*;
import cms.bean.keywordDict.KeywordDict;
import cms.bean.topic.Topic;
import cms.service.keywordDict.KeywordDictService;
import cms.service.setting.SettingService;
import cms.utils.JsonUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/10 9:36
 */
@Controller
public class KeywordDictAction {

    @Resource
    private SettingService settingService;
    @Autowired
    private KeywordDictService keywordDictService;

    @ResponseBody
    @RequestMapping("/control/keywordDictAction/list")
    public String execute(PageForm pageForm, ModelMap model, String keyWord,
                          HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<String> parent = Lists.newArrayList();
        parent.add("o.deleted = " + Boolean.FALSE);
        Optional.ofNullable(keyWord).filter(Strings::isNotEmpty).map(o -> String.format("o.keyWord like '%%%s%%'", o)).ifPresent(parent::add);

        PageView<Topic> pageView = new PageView<Topic>(settingService.findSystemSetting_cache().getBackstagePageNumber(), pageForm.getPage(), 10);
        //当前页
        int firstindex = (pageForm.getPage() - 1) * pageView.getMaxresult();
        //排序
        LinkedHashMap<String, String> orderby = new LinkedHashMap<String, String>();

        orderby.put("updateTime", "desc");//根据id字段降序排序
        //调用分页算法类
        QueryResult<KeywordDict> qr = keywordDictService.getScrollData(KeywordDict.class, firstindex, pageView.getMaxresult(), Optional.of(parent).filter(o -> o.size() > 0).map(l -> l.stream().collect(Collectors.joining(" and "))).orElse(""), new String[0], orderby);
        pageView.setQueryResult(qr);

        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, pageView));
    }
}
