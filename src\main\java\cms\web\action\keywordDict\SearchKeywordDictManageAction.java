package cms.web.action.keywordDict;

import cms.bean.RequestResult;
import cms.bean.ResultCode;
import cms.bean.keywordDict.SearchKeywordDict;
import cms.constant.ErrorCode;
import cms.handle.CustomException;
import cms.service.keywordDict.SearchKeywordDictService;
import cms.utils.JsonUtils;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cms.constant.Constant.WAIT_SECONDS;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/24 13:58
 */
@Controller
@RequestMapping("/control/searchKeywordDictAction/manage")
@Slf4j
public class SearchKeywordDictManageAction {

    private static final String UPLOAD_SUFFIX = "txt";
    public static Semaphore semaphore = new Semaphore(1);

    @Autowired
    private SearchKeywordDictService searchKeywordDictService;

    /**
     * 上传词库
     */
    @ResponseBody
    @RequestMapping(params = "method=upload", method = RequestMethod.POST)
    public String uploadFilterWord(ModelMap model, MultipartFile file) throws Exception {
        Optional.ofNullable(file).map(MultipartFile::getOriginalFilename).map(o -> o.split("\\.")).filter(o -> o.length > 1).map(o -> o[o.length - 1]).filter(o -> UPLOAD_SUFFIX.equals(o.toLowerCase())).orElseThrow(() -> new CustomException(ErrorCode.C_1_0006_0001, "file"));
        InputStreamReader isr = new InputStreamReader(file.getInputStream());
        BufferedReader reader = new BufferedReader(isr);
        List<String> keyWords = Optional.ofNullable(reader.lines().map(String::trim).filter(Strings::isNotEmpty).distinct().collect(Collectors.toList())).filter(CollectionUtil::isNotEmpty).orElseThrow(() -> new CustomException(ErrorCode.C_1_0006_0002, "file"));
        if (!semaphore.tryAcquire(WAIT_SECONDS, TimeUnit.SECONDS)) {
            throw new CustomException(ErrorCode.C_1_0001_0012, "file");
        }
        try {
            searchKeywordDictService.addAndDel(keyWords.stream().map(o -> {
                SearchKeywordDict keywordDict = new SearchKeywordDict();
                keywordDict.setKeyWord(o);
                return keywordDict;
            }).collect(Collectors.toList()));
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        } finally {
            semaphore.release();
        }
    }
}
