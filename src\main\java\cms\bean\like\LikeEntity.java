package cms.bean.like;

import cms.constant.enums.LikeType;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 点赞 实体类的抽象基类,定义基本属性
 */
@MappedSuperclass
@Data
public class LikeEntity implements Serializable {
    private static final long serialVersionUID = -1496184788206840632L;

    /**
     * ID
     **/
    @Id
    @Column(length = 36)
    protected String id;
    /**
     * 点赞的用户名称
     **/
    @Column(length = 30)
    protected String userName;
    /**
     * 账号
     **/
    @Transient
    protected String account;
    /**
     * 呢称
     **/
    @Transient
    protected String nickname;
    /**
     * 头像路径
     **/
    @Transient
    protected String avatarPath;
    /**
     * 头像名称
     **/
    @Transient
    protected String avatarName;

    /**
     * 发布话题的用户名称
     **/
    @Column(length = 30)
    protected String postUserName;

    /**
     * 加入时间
     **/
    @Temporal(TemporalType.TIMESTAMP)
    protected Date addtime = new Date();

    @Column(nullable = false, updatable = false)
    private Integer likeType;

    /**
     * 话题Id
     **/
    protected Long topicId;

    /**
     * 话题标题
     **/
    @Transient
    protected String topicTitle;

    @Transient
    private String oneTagName;

    @Transient
    private String content;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getAddtime() {
        return addtime;
    }

    public void setAddtime(Date addtime) {
        this.addtime = addtime;
    }

    public Long getTopicId() {
        return topicId;
    }

    public void setTopicId(Long topicId) {
        this.topicId = topicId;
    }

    public String getTopicTitle() {
        return topicTitle;
    }

    public void setTopicTitle(String topicTitle) {
        this.topicTitle = topicTitle;
    }

    public String getPostUserName() {
        return postUserName;
    }

    public void setPostUserName(String postUserName) {
        this.postUserName = postUserName;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatarPath() {
        return avatarPath;
    }

    public void setAvatarPath(String avatarPath) {
        this.avatarPath = avatarPath;
    }

    public String getAvatarName() {
        return avatarName;
    }

    public void setAvatarName(String avatarName) {
        this.avatarName = avatarName;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }


}
