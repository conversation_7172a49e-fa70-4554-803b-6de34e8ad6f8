package cms.service.question.impl;

import cms.bean.ErrorView;
import cms.bean.message.EmailRemind;
import cms.bean.payment.PaymentLog;
import cms.bean.platformShare.QuestionRewardPlatformShare;
import cms.bean.question.*;
import cms.bean.setting.EditorTag;
import cms.bean.setting.SystemSetting;
import cms.bean.topic.ImageInfo;
import cms.bean.user.*;
import cms.constant.ErrorCode;
import cms.handle.CustomException;
import cms.service.message.RemindService;
import cms.service.message.impl.EmailRemindService;
import cms.service.messageSource.ErrorMessageService;
import cms.service.question.AnswerService;
import cms.service.question.QuestionIndexService;
import cms.service.question.QuestionService;
import cms.service.question.QuestionTagService;
import cms.service.setting.SettingService;
import cms.service.template.TemplateService;
import cms.service.user.UserService;
import cms.utils.Base64;
import cms.utils.*;
import cms.utils.threadLocal.AccessUserThreadLocal;
import cms.web.action.AccessSourceDeviceManage;
import cms.web.action.CSRFTokenManage;
import cms.web.action.SystemException;
import cms.web.action.TextFilterManage;
import cms.web.action.filterWord.SensitiveWordFilterManage;
import cms.web.action.message.RemindManage;
import cms.web.action.question.AnswerManage;
import cms.web.action.question.QuestionManage;
import cms.web.action.question.QuestionTagManage;
import cms.web.action.setting.SettingManage;
import cms.web.action.user.PointManage;
import cms.web.action.user.UserDynamicManage;
import cms.web.action.user.UserManage;
import cms.web.action.user.UserRoleManage;
import cms.web.taglib.Configuration;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cms.constant.ErrorCode.*;
import static cms.constant.enums.RemindTypeEnums.IDOL_APPEND_QA;
import static cms.constant.enums.RemindTypeEnums.IDOL_QA;
import static cms.web.action.message.RemindManage.TYPE_CODE_ADOPTION_ANSWER;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/4 10:57
 */
@Service
@Slf4j
public class QuestionFormService {

    @Resource
    TemplateService templateService;
    @Resource
    QuestionTagService questionTagService;
    @Resource
    AccessSourceDeviceManage accessSourceDeviceManage;
    @Resource
    TextFilterManage textFilterManage;
    @Resource
    SettingManage settingManage;
    @Resource
    SettingService settingService;
    @Resource
    UserService userService;
    @Resource
    CSRFTokenManage csrfTokenManage;
    @Resource
    SensitiveWordFilterManage sensitiveWordFilterManage;
    @Resource
    UserManage userManage;
    @Resource
    UserDynamicManage userDynamicManage;
    @Resource
    UserRoleManage userRoleManage;
    @Resource
    QuestionManage questionManage;
    @Resource
    QuestionService questionService;
    @Resource
    QuestionIndexService questionIndexService;
    @Resource
    PointManage pointManage;
    @Resource
    AnswerManage answerManage;
    @Resource
    AnswerService answerService;
    @Resource
    RemindService remindService;
    @Resource
    RemindManage remindManage;
    @Autowired
    private ErrorMessageService errorMessageService;

    @Transactional(propagation = Propagation.REQUIRED)
    public String add(ModelMap model, final Long oneTagId, Long[] tagId, String title, String content, String amount, String point,
                      String token, String captchaKey, String captchaValue, String jumpUrl,
                      RedirectAttributes redirectAttrs,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();
        tagId = ArrayUtil.distinct(tagId);

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据

        //是否有当前功能操作权限
        boolean permission = userRoleManage.isPermission(ResourceEnum._10002000, null);
        if (permission == false) {
            if (isAjax == true) {
                response.setStatus(403);//设置状态码

                WebUtil.writeToWeb("", "json", response);
                return null;
            } else {
                String dirName = templateService.findTemplateDir_cache();

                String accessPath = accessSourceDeviceManage.accessDevices(request);
                request.setAttribute("message", "权限不足");
                return "/templates/" + dirName + "/" + accessPath + "/message";
            }
        }


        Map<String, String> error = new HashMap<String, String>();
        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getCloseSite().equals(2)) {
            error.put("question", ErrorView._21.name());//只读模式不允许提交数据
        }


        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token, error);

        //验证码
        //悬赏金额
        BigDecimal rewardAmount = new BigDecimal("0.00");
        //悬赏积分
        Long rewardPoint = 0L;

        User user = userService.findUserByUserName(accessUser.getUserName());//查询用户数据
        if (user != null) {
            if (point != null && !"".equals(point.trim())) {
                if (point.trim().length() > 8) {
                    error.put("point", ErrorView._222.name());//不能超过8位数字
                } else {
                    boolean pointVerification = Verification.isPositiveIntegerZero(point.trim());//正整数+0
                    if (pointVerification) {
                        Long _rewardPoint = Long.parseLong(point.trim());
                        if (_rewardPoint > user.getPoint()) {
                            error.put("point", ErrorView._226.name());//不能大于账户积分

                        }
                        if (_rewardPoint < 0L) {
                            error.put("point", ErrorView._225.name());//不能小于0

                        }
                        if (systemSetting.getQuestionRewardPointMax() != null) {
                            if (systemSetting.getQuestionRewardPointMax().equals(0L)) {
                                error.put("point", ErrorView._232.name());//不允许悬赏积分

                            } else if (systemSetting.getQuestionRewardPointMax() > 0L) {
                                if (_rewardPoint < systemSetting.getQuestionRewardPointMin()) {
                                    error.put("point", ErrorView._233.name());//不能小于悬赏积分下限
                                }
                                if (_rewardPoint > systemSetting.getQuestionRewardPointMax()) {
                                    error.put("point", ErrorView._234.name());//不能大于悬赏积分上限
                                }
                            }
                        } else {
                            if (_rewardPoint < systemSetting.getQuestionRewardPointMin()) {
                                error.put("point", ErrorView._233.name());//不能小于悬赏积分下限
                            }
                        }

                        if (error.size() == 0) {
                            rewardPoint = _rewardPoint;
                        }
                    } else {
                        error.put("point", ErrorView._223.name());//请填写正整数或0
                    }
                }
            }
        }

        //如果全局不允许提交问题
        if (systemSetting.isAllowQuestion() == false) {
            error.put("question", ErrorView._210.name());//不允许提交问题
        }

        //如果实名用户才允许提交问题
        if (systemSetting.isRealNameUserAllowQuestion() == true) {
            User _user = userManage.query_cache_findUserByUserName(accessUser.getUserName());
            if (_user.isRealNameAuthentication() == false) {
                error.put("question", ErrorView._209.name());//实名用户才允许提交问题
            }
        }

        Question question = new Question();
        Date d = new Date();
        question.setPostTime(d);
        question.setLastAnswerTime(d);
        question.setAmount(rewardAmount);
        question.setPoint(rewardPoint);
        question.setImage("");

        List<String> imageNameList = null;
        boolean isImage = false;//是否含有图片
        List<String> flashNameList = null;
        boolean isFlash = false;//是否含有Flash
        List<String> mediaNameList = null;
        boolean isMedia = false;//是否含有音视频
        List<String> fileNameList = null;
        boolean isFile = false;//是否含有文件
        boolean isMap = false;//是否含有地图


        //前台发表话题审核状态
        if (systemSetting.getQuestion_review().equals(10)) {//10.全部审核 20.特权会员未触发敏感词免审核(未实现) 30.特权会员免审核 40.触发敏感词需审核(未实现) 50.无需审核
            question.setStatus(10);//10.待审核
        } else if (systemSetting.getQuestion_review().equals(30)) {
            //是否有当前功能操作权限
            boolean flag_permission = userRoleManage.isPermission(ResourceEnum._10006000, null);
            if (flag_permission) {
                question.setStatus(20);//20.已发布
            } else {
                question.setStatus(10);//10.待审核
            }
        } else {
            question.setStatus(20);//20.已发布
        }

        LinkedHashSet<QuestionTagAssociation> questionTagAssociationList = new LinkedHashSet<QuestionTagAssociation>();
        if (null != oneTagId) {
            if (null != tagId && tagId.length > systemSetting.getMaxQuestionTagQuantity()) {
                error.put("tagId", errorMessageService.getMessage(C_2_0008_0044, String.valueOf(systemSetting.getMaxQuestionTagQuantity())));
            }

            List<QuestionTag> questionTagList = questionTagService.findAllQuestionTag_cache();
            questionTagList.stream().filter(x -> x.getId().equals(oneTagId)).findAny().ifPresent(o -> {
                QuestionTagAssociation questionTagAssociation = new QuestionTagAssociation();
                questionTagAssociation.setQuestionTagId(o.getId());
                questionTagAssociation.setQuestionTagName(o.getName());
                questionTagAssociation.setUserName(accessUser.getUserName());
                questionTagAssociationList.add(questionTagAssociation);
            });
            if (null != tagId) {
                for (Long id : tagId) {
                    if (id != null && id > 0L) {
                        QuestionTagAssociation questionTagAssociation = new QuestionTagAssociation();
                        questionTagAssociation.setQuestionTagId(id);
                        for (QuestionTag questionTag : questionTagList) {
                            if (questionTag.getId().equals(id) && questionTag.getChildNodeNumber().equals(0)) {
                                questionTagAssociation.setQuestionTagName(questionTag.getName());
                                questionTagAssociation.setUserName(accessUser.getUserName());
                                questionTagAssociationList.add(questionTagAssociation);
                                break;
                            }
                        }
                    }
                }
            }
            question.setQuestionTagAssociationList(new ArrayList<QuestionTagAssociation>(questionTagAssociationList));
        } else {
            error.put("tagId", errorMessageService.getMessage(C_2_0007_0043));
        }

        if (title != null && !"".equals(title.trim())) {
            if (systemSetting.isAllowFilterWord()) {
                String wordReplace = "";
                if (systemSetting.getFilterWordReplace() != null) {
                    wordReplace = systemSetting.getFilterWordReplace();
                }
                title = sensitiveWordFilterManage.filterSensitiveWord(title, wordReplace);
            }

            question.setTitle(title);
            if (title.length() > 150) {
                error.put("title", errorMessageService.getMessage(C_2_0007_0044));
            }
        } else {
            error.put("title", errorMessageService.getMessage(C_2_0007_0045));
        }

        if (content != null && !"".equals(content.trim())) {
            EditorTag editorTag = settingManage.readQuestionEditorTag();
            //过滤标签
            content = textFilterManage.filterTag(request, content, editorTag);
            Object[] object = textFilterManage.filterHtml(request, content, "question", editorTag);
            String value = (String) object[0];
            imageNameList = (List<String>) object[1];
            isImage = (Boolean) object[2];//是否含有图片
            flashNameList = (List<String>) object[3];
            isFlash = (Boolean) object[4];//是否含有Flash
            mediaNameList = (List<String>) object[5];
            isMedia = (Boolean) object[6];//是否含有音视频
            fileNameList = (List<String>) object[7];
            isFile = (Boolean) object[8];//是否含有文件
            isMap = (Boolean) object[9];//是否含有地图

            //不含标签内容
            String text = textFilterManage.filterText(textFilterManage.specifyHtmlTagToText(value));
            //清除空格&nbsp;
            String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();
            //摘要
            if (trimSpace != null && !"".equals(trimSpace)) {
                if (systemSetting.isAllowFilterWord()) {
                    String wordReplace = "";
                    if (systemSetting.getFilterWordReplace() != null) {
                        wordReplace = systemSetting.getFilterWordReplace();
                    }
                    trimSpace = sensitiveWordFilterManage.filterSensitiveWord(trimSpace, wordReplace);
                }
                if (trimSpace.length() > 180) {
                    question.setSummary(trimSpace.substring(0, 180) + "..");
                } else {
                    question.setSummary(trimSpace + "..");
                }
            }

            //不含标签内容
            String source_text = textFilterManage.filterText(content);
            //清除空格&nbsp;
            String source_trimSpace = cms.utils.StringUtil.replaceSpace(source_text).trim();

            if (isImage == true || isFlash == true || isMedia == true || isFile == true || isMap == true || (!"".equals(source_text.trim()) && !"".equals(source_trimSpace))) {
                if (systemSetting.isAllowFilterWord()) {
                    String wordReplace = "";
                    if (systemSetting.getFilterWordReplace() != null) {
                        wordReplace = systemSetting.getFilterWordReplace();
                    }
                    value = sensitiveWordFilterManage.filterSensitiveWord(value, wordReplace);
                }

                question.setIp(IpAddress.getClientIpAddress(request));
                question.setUserName(accessUser.getUserName());
                question.setIsStaff(false);
                question.setContent(value);
            } else {
                error.put("content", errorMessageService.getMessage(C_2_0008_0001));
            }

            //图片
            Optional.ofNullable(textFilterManage.readImageName(value, "question")).filter(CollUtil::isNotEmpty).map(l -> l.stream().map(o -> {
                ImageInfo imageInfo = new ImageInfo();
                imageInfo.setName(FileUtil.getName(o));
                imageInfo.setPath(FileUtil.getFullPath(o));
                return imageInfo;
            }).collect(Collectors.toList())).map(JSON::toJSONString).ifPresent(question::setImage);
        } else {
            error.put("content", errorMessageService.getMessage(C_2_0008_0001));
        }

        if (error.size() == 0) {
            Date time = new Date();
            question.setPostTime(time);

            //用户悬赏积分日志
            PointLog reward_pointLog = null;
            if (rewardPoint != null && rewardPoint > 0L) {
                reward_pointLog = new PointLog();
                reward_pointLog.setId(pointManage.createPointLogId(accessUser.getUserId()));
                reward_pointLog.setModule(1000);//1000.悬赏积分
                reward_pointLog.setOperationUserType(2);//操作用户类型  0:系统  1: 员工  2:会员
                reward_pointLog.setOperationUserName(accessUser.getUserName());//操作用户名称
                reward_pointLog.setPointState(2);//2:账户支出
                reward_pointLog.setPoint(rewardPoint);//积分
                reward_pointLog.setUserName(accessUser.getUserName());//用户名称
                reward_pointLog.setRemark("");
                reward_pointLog.setTimes(time);
            }

            //用户悬赏金额日志
            PaymentLog reward_paymentLog = null;
            try {
                //保存问题
                questionService.saveQuestion(question, new ArrayList<QuestionTagAssociation>(questionTagAssociationList), rewardPoint, reward_pointLog, rewardAmount, reward_paymentLog);
            } catch (SystemException e) {
                error.put("question", ErrorView._227.name());//提交问题错误
            }
            if (error.size() == 0) {
                //更新索引
                questionIndexService.addQuestionIndex(new QuestionIndex(String.valueOf(question.getId()), 1));

                PointLog pointLog = new PointLog();
                pointLog.setId(pointManage.createPointLogId(accessUser.getUserId()));
                pointLog.setModule(700);//700.问题
                pointLog.setParameterId(question.getId());//参数Id
                pointLog.setOperationUserType(2);//操作用户类型  0:系统  1: 员工  2:会员
                pointLog.setOperationUserName(accessUser.getUserName());//操作用户名称

                pointLog.setPoint(systemSetting.getQuestion_rewardPoint());//积分
                pointLog.setUserName(accessUser.getUserName());//用户名称
                pointLog.setRemark("");

                //增加用户积分
                userService.addUserPoint(accessUser.getUserName(), systemSetting.getQuestion_rewardPoint(), pointManage.createPointLogObject(pointLog));

                //用户动态
                UserDynamic userDynamic = new UserDynamic();
                userDynamic.setId(userDynamicManage.createUserDynamicId(accessUser.getUserId()));
                userDynamic.setUserName(accessUser.getUserName());
                userDynamic.setModule(500);//模块 500.问题
                userDynamic.setQuestionId(question.getId());
                userDynamic.setPostTime(question.getPostTime());
                userDynamic.setStatus(question.getStatus());
                userDynamic.setFunctionIdGroup("," + question.getId() + ",");
                Object new_userDynamic = userDynamicManage.createUserDynamicObject(userDynamic);
                userService.saveUserDynamic(new_userDynamic);

                //删除缓存
                userManage.delete_cache_findUserById(accessUser.getUserId());
                userManage.delete_cache_findUserByUserName(accessUser.getUserName());

                QuestionTagManage.incOneTagQuestionNum(Arrays.asList(oneTagId));

                //统计每分钟原来提交次数
                Integer original = settingManage.getSubmitQuantity("question", accessUser.getUserName());
                if (original != null) {
                    settingManage.addSubmitQuantity("question", accessUser.getUserName(), original + 1);//刷新每分钟原来提交次数
                } else {
                    settingManage.addSubmitQuantity("question", accessUser.getUserName(), 1);//刷新每分钟原来提交次数
                }
                EmailRemindService.addEmailRemind(new EmailRemind(question.getId(), accessUser.getUserId(), accessUser.getUserName(), IDOL_QA.getCode(), Optional.empty()));
            }
        }


        Map<String, String> returnError = new HashMap<String, String>();//错误
        if (error.size() > 0) {
            //将枚举数据转为错误提示字符
            for (Map.Entry<String, String> entry : error.entrySet()) {
                if (ErrorView.get(entry.getValue()) != null) {
                    returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                } else {
                    returnError.put(entry.getKey(), entry.getValue());
                }
            }
        }
        if (isAjax == true) {

            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

            if (error != null && error.size() > 0) {
                returnValue.put("success", "false");
                returnValue.put("error", returnError);
            } else {
                returnValue.put("success", "true");
                returnValue.put("questionId", question.getId());
            }
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {


            if (error != null && error.size() > 0) {//如果有错误

                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addFlashAttribute("question", question);


                String referer = request.getHeader("referer");


                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";

                return "redirect:/" + referer + queryString;

            }


            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());

                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "保存问题成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);

                String dirName = templateService.findTemplateDir_cache();


                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }


    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String appendQuestion(ModelMap model, Long questionId, String content,
                                 String token, String captchaKey, String captchaValue, String jumpUrl,
                                 RedirectAttributes redirectAttrs,
                                 HttpServletRequest request, HttpServletResponse response) throws Exception {
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据

        //是否有当前功能操作权限
        boolean permission = userRoleManage.isPermission(ResourceEnum._10003000, null);
        if (permission == false) {
            if (isAjax == true) {
                response.setStatus(403);//设置状态码

                WebUtil.writeToWeb("", "json", response);
                return null;
            } else {
                String dirName = templateService.findTemplateDir_cache();

                String accessPath = accessSourceDeviceManage.accessDevices(request);
                request.setAttribute("message", "权限不足");
                return "/templates/" + dirName + "/" + accessPath + "/message";
            }
        }


        Map<String, String> error = new HashMap<String, String>();
        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getCloseSite().equals(2)) {
            error.put("question", ErrorView._21.name());//只读模式不允许提交数据
        }

        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token, error);

        //验证码
        //如果全局不允许提交问题
        if (systemSetting.isAllowQuestion() == false) {
            error.put("question", ErrorView._210.name());//不允许提交问题
        }

        //如果实名用户才允许提交问题
        if (systemSetting.isRealNameUserAllowQuestion() == true) {
            User _user = userManage.query_cache_findUserByUserName(accessUser.getUserName());
            if (_user.isRealNameAuthentication() == false) {
                error.put("question", ErrorView._209.name());//实名用户才允许提交问题
            }

        }


        List<String> imageNameList = null;
        boolean isImage = false;//是否含有图片
        List<String> flashNameList = null;
        boolean isFlash = false;//是否含有Flash
        List<String> mediaNameList = null;
        boolean isMedia = false;//是否含有音视频
        List<String> fileNameList = null;
        boolean isFile = false;//是否含有文件
        boolean isMap = false;//是否含有地图

        String appendContent = "";
        Question question = null;
        if (questionId != null && questionId > 0L) {
            question = questionService.findById(questionId);
            List<AppendQuestionItem> appendQuestionItemList = JSON.parseArray(question.getAppendContent() + "]", AppendQuestionItem.class);
            if (appendQuestionItemList.size() >= 3) {
                throw new CustomException(ErrorCode.C_2_0008_0041, "question");
            }
            if (question != null) {
                if (!question.getUserName().equals(accessUser.getUserName())) {
                    error.put("question", ErrorView._219.name());//不是提交该问题的用户不允许追加提问
                }

                if (content != null && !"".equals(content.trim())) {
                    EditorTag editorTag = settingManage.readQuestionEditorTag();
                    //过滤标签
                    content = textFilterManage.filterTag(request, content, editorTag);
                    Object[] object = textFilterManage.filterHtml(request, content, "question", editorTag);
                    String value = (String) object[0];
                    imageNameList = (List<String>) object[1];
                    isImage = (Boolean) object[2];//是否含有图片
                    flashNameList = (List<String>) object[3];
                    isFlash = (Boolean) object[4];//是否含有Flash
                    mediaNameList = (List<String>) object[5];
                    isMedia = (Boolean) object[6];//是否含有音视频
                    fileNameList = (List<String>) object[7];
                    isFile = (Boolean) object[8];//是否含有文件
                    isMap = (Boolean) object[9];//是否含有地图


                    //不含标签内容
                    String source_text = textFilterManage.filterText(content);
                    //清除空格&nbsp;
                    String source_trimSpace = cms.utils.StringUtil.replaceSpace(source_text).trim();

                    if (isImage == true || isFlash == true || isMedia == true || isFile == true || isMap == true || (!"".equals(source_text.trim()) && !"".equals(source_trimSpace))) {
                        if (systemSetting.isAllowFilterWord()) {
                            String wordReplace = "";
                            if (systemSetting.getFilterWordReplace() != null) {
                                wordReplace = systemSetting.getFilterWordReplace();
                            }
                            value = sensitiveWordFilterManage.filterSensitiveWord(value, wordReplace);
                        }

                        appendContent = value;
                    } else {
                        error.put("content", ErrorView._218.name());//追加内容不能为空
                    }

                } else {
                    error.put("content", ErrorView._218.name());//追加内容不能为空
                }
            } else {
                error.put("question", ErrorView._207.name());//问题不存在
            }
        } else {
            error.put("question", ErrorView._203.name());//问题Id不能为空
        }

        String appendContent_json = "";
        Optional<String> appendQuestionItemId = Optional.empty();
        if (appendContent != null && !"".equals(appendContent.trim())) {
            AppendQuestionItem appendQuestionItem = new AppendQuestionItem();
            appendQuestionItem.setId(UUIDUtil.getUUID32());
            appendQuestionItem.setContent(appendContent.trim());
            appendQuestionItem.setPostTime(new Date());
            appendContent_json = JsonUtils.toJSONString(appendQuestionItem);
            appendQuestionItemId = Optional.of(appendQuestionItem.getId());
        } else {
            error.put("content", ErrorView._218.name());//追加内容不能为空
        }

        if (error.size() == 0) {

            //追加问题
            questionService.saveAppendQuestion(questionId, appendContent_json + ",");
            //更新索引
            questionIndexService.addQuestionIndex(new QuestionIndex(String.valueOf(question.getId()), 2));

            //删除缓存
            questionManage.delete_cache_findById(questionId);//删除问题缓存

            //统计每分钟原来提交次数
            Integer original = settingManage.getSubmitQuantity("question", accessUser.getUserName());
            if (original != null) {
                settingManage.addSubmitQuantity("question", accessUser.getUserName(), original + 1);//刷新每分钟原来提交次数
            } else {
                settingManage.addSubmitQuantity("question", accessUser.getUserName(), 1);//刷新每分钟原来提交次数
            }
            appendQuestionItemId.ifPresent(o -> EmailRemindService.addEmailRemind(new EmailRemind(o, accessUser.getUserId(), accessUser.getUserName(), IDOL_APPEND_QA.getCode(), Optional.empty())));
        }

        Map<String, String> returnError = new HashMap<String, String>();//错误
        if (error.size() > 0) {
            //将枚举数据转为错误提示字符
            for (Map.Entry<String, String> entry : error.entrySet()) {
                if (ErrorView.get(entry.getValue()) != null) {
                    returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                } else {
                    returnError.put(entry.getKey(), entry.getValue());
                }
            }
        }
        if (isAjax == true) {

            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

            if (error != null && error.size() > 0) {
                returnValue.put("success", "false");
                returnValue.put("error", returnError);
            } else {
                returnValue.put("success", "true");
            }
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {
            if (error != null && error.size() > 0) {//如果有错误

                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参
                redirectAttrs.addFlashAttribute("content", content);


                String referer = request.getHeader("referer");


                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";

                return "redirect:/" + referer + queryString;
            }

            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());

                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "追加问题成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);

                String dirName = templateService.findTemplateDir_cache();

                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String adoptionAnswer(ModelMap model, Long answerId, String token, String jumpUrl,
                                 RedirectAttributes redirectAttrs,
                                 HttpServletRequest request, HttpServletResponse response) throws Exception {
        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据

        Map<String, String> error = new HashMap<String, String>();
        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getCloseSite().equals(2)) {
            error.put("adoptionAnswer", ErrorView._21.name());//只读模式不允许提交数据
        }


        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token, error);

        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();
        Answer answer = null;
        Question question = null;
        if (answerId != null && answerId > 0L) {
            answer = answerManage.query_cache_findByAnswerId(answerId);
            if (answer != null) {
                if (answer.getStatus() != 20) {
                    error.put("adoptionAnswer", ErrorView._241.name());//该问题不允许采纳答案
                }
                question = questionManage.query_cache_findById(answer.getQuestionId());
                if (question != null) {
                    if (!question.getStatus().equals(20)) {
                        error.put("adoptionAnswer", ErrorView._228.name());//该问题不允许采纳答案
                    }

                    if (question.getAdoptionAnswerId() > 0L) {
                        error.put("adoptionAnswer", ErrorView._216.name());//该问题已经采纳答案
                    }

                    if (!accessUser.getUserName().equals(question.getUserName())) {
                        error.put("adoptionAnswer", ErrorView._217.name());//不是提交该问题的用户不允许采纳答案
                    }

                } else {
                    error.put("adoptionAnswer", ErrorView._207.name());//问题不存在
                }
            } else {
                error.put("adoptionAnswer", ErrorView._205.name());//答案不存在
            }


        } else {
            error.put("adoptionAnswer", ErrorView._215.name());//答案Id不能为空
        }


        if (error.size() == 0) {
            Date time = new Date();

            User user = userManage.query_cache_findUserByUserName(answer.getUserName());

            //是否更改采纳答案
            boolean changeAdoption = false;
            if (question.getAdoptionAnswerId() > 0L) {
                changeAdoption = true;

            }


            //回答用户获得积分
            Long point = 0L;


            //用户悬赏积分日志
            Object pointLogObject = null;
            if (user != null && answer.getIsStaff() == false && question.getPoint() != null && question.getPoint() > 0L) {
                point = question.getPoint();

                PointLog reward_pointLog = new PointLog();
                reward_pointLog.setId(pointManage.createPointLogId(user.getId()));
                reward_pointLog.setModule(1100);//1100.采纳答案
                reward_pointLog.setParameterId(answer.getId());//参数Id
                reward_pointLog.setOperationUserType(2);//操作用户类型  0:系统  1: 员工  2:会员
                reward_pointLog.setOperationUserName(accessUser.getUserName());//操作用户名称
                reward_pointLog.setPointState(1);//积分状态  1:账户存入  2:账户支出
                reward_pointLog.setPoint(question.getPoint());//积分
                reward_pointLog.setUserName(answer.getUserName());//用户名称
                reward_pointLog.setRemark("");
                reward_pointLog.setTimes(time);
                pointLogObject = pointManage.createPointLogObject(reward_pointLog);
            }

            //用户悬赏金额日志
            Object paymentLogObject = null;
            //平台分成
            QuestionRewardPlatformShare questionRewardPlatformShare = null;
            //回答用户分成金额
            BigDecimal userNameShareAmount = new BigDecimal("0");

            int i = answerService.updateAdoptionAnswer(answer.getQuestionId(), answerId, changeAdoption, null, null, null, null,
                    answer.getUserName(), point, pointLogObject, userNameShareAmount, paymentLogObject, questionRewardPlatformShare);
            //删除缓存
            answerManage.delete_cache_findByAnswerId(answerId);
            questionManage.delete_cache_findById(answer.getQuestionId());
            answerManage.delete_cache_answerCount(answer.getUserName());

            if (user != null) {
                userManage.delete_cache_findUserById(user.getId());
                userManage.delete_cache_findUserByUserName(user.getUserName());
                Question finalQuestion = question;
                Answer finalAnswer = answer;
                Optional.ofNullable(user).filter(o -> !user.getUserName().equals(accessUser.getUserName())).ifPresent(x -> {
                    remindService.saveRemind(remindManage.createRemindObject(accessUser.getUserId(), x.getId(), TYPE_CODE_ADOPTION_ANSWER, System.currentTimeMillis(), null, finalQuestion.getId(), finalAnswer.getId()));
                    Optional.ofNullable(x).filter(User::getEmailRemind).ifPresent(o -> EmailRemindService.addEmailRemind(new EmailRemind(answerId, accessUser.getUserId(), accessUser.getUserName(), TYPE_CODE_ADOPTION_ANSWER, Optional.of(o))));
                    //删除提醒缓存
                    remindManage.delete_cache_findUnreadRemindByUserId(x.getId());
                });
            }


        }
        Map<String, String> returnError = new HashMap<String, String>();//错误
        if (error.size() > 0) {
            //将枚举数据转为错误提示字符
            for (Map.Entry<String, String> entry : error.entrySet()) {
                if (ErrorView.get(entry.getValue()) != null) {
                    returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                } else {
                    returnError.put(entry.getKey(), entry.getValue());
                }
            }
        }
        if (isAjax == true) {

            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

            if (error != null && error.size() > 0) {
                returnValue.put("success", "false");
                returnValue.put("error", returnError);
            } else {
                returnValue.put("success", "true");
            }
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {


            if (error != null && error.size() > 0) {//如果有错误
                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参


                String referer = request.getHeader("referer");


                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";

                return "redirect:/" + referer + queryString;

            }


            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());

                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "采纳答案成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);

                String dirName = templateService.findTemplateDir_cache();


                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public void cancelAdoptionAnswer(Long answerId, String token,
                                     HttpServletRequest request, HttpServletResponse response) {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();
        Answer answer = Optional.ofNullable(answerId).map(answerManage::query_cache_findByAnswerId).orElseThrow(() -> new CustomException(ErrorCode.C_2_0008_0003, "adoptionAnswer"));
        Question question = Optional.ofNullable(questionManage.query_cache_findById(answer.getQuestionId())).orElseThrow(() -> new CustomException(ErrorCode.C_2_0008_0005, "adoptionAnswer"));
        Optional.ofNullable(accessUser.getUserName().equals(question.getUserName())).filter(o -> o).orElseThrow(() -> new CustomException(ErrorCode.C_2_0008_0042, "adoptionAnswer"));

        final long point = Optional.ofNullable(question.getPoint()).orElse(0L);
        Optional<User> user = Optional.ofNullable(userManage.query_cache_findUserByUserName(answer.getUserName()));
        // 取消采纳用户退还悬赏积分日志
        Optional<Object> cancelAdoptionPointLogObject = user.filter(o -> answer.getIsStaff() == false && point > 0L).map(o -> {
            PointLog reward_pointLog = new PointLog();
            reward_pointLog.setId(pointManage.createPointLogId(o.getId()));
            reward_pointLog.setModule(1100);//1100.采纳答案
            reward_pointLog.setParameterId(answer.getId());//参数Id
            reward_pointLog.setOperationUserType(2);//操作用户类型  0:系统  1: 员工  2:会员
            reward_pointLog.setOperationUserName(accessUser.getUserName());//操作用户名称
            reward_pointLog.setPointState(2);//积分状态  1:账户存入  2:账户支出
            reward_pointLog.setPoint(point);//积分
            reward_pointLog.setUserName(answer.getUserName());//用户名称
            reward_pointLog.setRemark("");
            reward_pointLog.setTimes(new Date());
            return pointManage.createPointLogObject(reward_pointLog);
        });

        int i = answerService.updateCancelAdoptionAnswer(answer.getQuestionId(), answer.getUserName(), cancelAdoptionPointLogObject.orElse(null), null, null, point);
        //删除缓存
        answerManage.delete_cache_findByAnswerId(answerId);
        questionManage.delete_cache_findById(answer.getQuestionId());
        answerManage.delete_cache_answerCount(answer.getUserName());

        user.ifPresent(o -> {
            userManage.delete_cache_findUserById(o.getId());
            userManage.delete_cache_findUserByUserName(o.getUserName());
        });
    }
}
