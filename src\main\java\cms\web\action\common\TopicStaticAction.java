package cms.web.action.common;

import cms.bean.RequestResult;
import cms.bean.ResultCode;
import cms.bean.topic.Tag;
import cms.bean.topic.Topic;
import cms.bean.user.ResourceEnum;
import cms.service.topic.TagService;
import cms.utils.IpAddress;
import cms.utils.JsonUtils;
import cms.web.action.CSRFTokenManage;
import cms.web.action.topic.TopicManage;
import cms.web.action.user.UserRoleManage;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/6/9 11:23
 */
@Controller
@RequestMapping("staticInfo/control/topic")
@Slf4j
public class TopicStaticAction {

    @Resource
    CSRFTokenManage csrfTokenManage;
    @Resource
    TagService tagService;
    @Autowired
    private TopicManage topicManage;
    @Resource
    UserRoleManage userRoleManage;

    @ResponseBody
    @RequestMapping(value = "/getDistinctTwoTag", method = RequestMethod.GET)
    public String getDistinctTwoTag(String token, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);

        List<String> tagList = tagService.findAllTag_cache().stream().filter(o -> o.getGrade() == 2).map(Tag::getName).distinct().collect(Collectors.toList());
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, tagList));
    }

    @ResponseBody
    @RequestMapping(value = "/getTwoTagNum", method = RequestMethod.GET)
    public String getTwoTagNum(String token, String twoTagName, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, Optional.ofNullable(twoTagName).map(String::trim).filter(StringUtils::isNotEmpty).map(topicManage::getTwoTagTopicNum).orElse(0)));
    }

    @ResponseBody
    @RequestMapping(value = "/getTwoTagByParent", method = RequestMethod.GET)
    public String getTwoTagByParent(String token, Long tagId, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        List<Tag> tagList = Optional.ofNullable(tagId).map(x -> tagService.findAllTag_cache().stream().filter(o -> x.equals(o.getParentId())).collect(Collectors.toList())).orElseGet(() -> Lists.newArrayList());
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, tagList));
    }

    @ResponseBody
    @RequestMapping(value = "/getTagNum", method = RequestMethod.POST)
    public String getTagNum(String token, @RequestParam("tagId[]") Long[] tagId, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, topicManage.getTagTopicNum(Arrays.asList(tagId))));
    }

    @ResponseBody
    @RequestMapping(value = "/getTagByName", method = RequestMethod.GET)
    public String getTagByName(String token, String tagName, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        Tag tag = Optional.ofNullable(tagName).map(x -> tagService.findAllTag_cache().stream().filter(o -> o.getGrade() == 1).filter(o -> x.equals(o.getName())).findAny().orElse(null)).orElse(null);
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, tag));
    }

    @ResponseBody
    @RequestMapping(value = "/getPostNum", method = RequestMethod.GET)
    public String getpostNum(String token, String userName, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, topicManage.query_cache_postNumByUserName(userName)));
    }

    @ResponseBody
    @RequestMapping(value = "/getEssenceList", method = RequestMethod.GET)
    public String getEssenceList(String token, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        List<Topic> topics = topicManage.findEssenceTop10_cache();
        Map<Long, Tag> tagMap = tagService.findAllTag_cache().stream().collect(Collectors.toMap(o -> o.getId(), o -> o));
        topics.stream().forEach(o -> o.setTagName(Optional.ofNullable(tagMap.get(o.getTagId())).map(Tag::getName).orElse("")));
        Map<String, Object> result = Maps.newHashMap();
        result.put("records", topics);
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, result));
    }

    @ResponseBody
    @RequestMapping(value = "/isQueryData", method = RequestMethod.GET)
    public String isQueryData(String token, Boolean isQueryData, HttpServletRequest request) {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        return JSON.toJSONString(new RequestResult(ResultCode.SUCCESS, topicManage.isQueryData(isQueryData)));
    }

    @ResponseBody
    @RequestMapping(value = "/upTwoTagNum", method = RequestMethod.GET)
    public String upTwoTagNum(String token, Long tagId, Integer num, HttpServletRequest request) {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        return JSON.toJSONString(new RequestResult(ResultCode.SUCCESS, topicManage.upTwoTagTopicNum(tagId, num)));
    }

    @ResponseBody
    @RequestMapping(value = "/addView", method = RequestMethod.POST)
    public RequestResult addView(String token, @RequestParam("topicId") Long topicId, HttpServletRequest request) {
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        Optional.ofNullable(topicId).map(topicManage::queryTopicCache).ifPresent(o -> {
            userRoleManage.checkPermission(ResourceEnum._1001000, o.getTagId());
            Optional.ofNullable(IpAddress.getClientIpAddress(request)).ifPresent(i -> topicManage.addView(topicId, i));
        });
        return RequestResult.ok("");
    }
}
