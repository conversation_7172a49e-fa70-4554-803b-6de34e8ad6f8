package cms.service.keywordDict;

import cms.bean.keywordDict.SearchKeywordDict;
import cms.service.besa.DaoSupport;
import cn.hutool.core.collection.CollUtil;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/24 14:02
 */
@Service
public class SearchKeywordDictService extends DaoSupport<SearchKeywordDict> {

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    @Cacheable(value = "findAllSearchKeyword_cache", key = "'findAllSearchKeyword_default'")
    public List<SearchKeywordDict> findAll_cache() {
        return this.findAll();
    }

    public List<SearchKeywordDict> findAll() {
        Query query = em.createQuery("select o from SearchKeywordDict o where o.deleted = false");
        query.setMaxResults(1000);
        return query.getResultList();
    }

    private void add(List<SearchKeywordDict> keywordDicts) {
        this.save(keywordDicts);
    }

    private void delAll() {
        Query query = em.createQuery("delete from SearchKeywordDict o");
        query.executeUpdate();
    }

    @CacheEvict(value = "findAllSearchKeyword_cache", allEntries = true)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public synchronized void addAndDel(List<SearchKeywordDict> keywordDicts) {
        this.delAll();
        Optional.ofNullable(keywordDicts).filter(CollUtil::isNotEmpty).ifPresent(this::add);
    }
}

