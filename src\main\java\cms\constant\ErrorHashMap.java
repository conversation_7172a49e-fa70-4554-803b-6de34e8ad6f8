package cms.constant;

import cms.service.messageSource.ErrorMessageService;
import cms.utils.SpringUtils;

import java.util.HashMap;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/26 15:06
 */
public class ErrorHashMap<K> extends HashMap<K, String> {

    private ErrorMessageService errorMessageService;

    public ErrorHashMap() {
        errorMessageService = SpringUtils.getBean(ErrorMessageService.class);
    }

    public String put(K key, int value) {
        return super.put(key, errorMessageService.getMessage(value));
    }
}
