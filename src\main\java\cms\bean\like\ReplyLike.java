package cms.bean.like;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/18 10:27
 */
@Entity
@Table(name = "replylike_0", indexes = {@Index(name = "idx_replyId_addtime", columnList = "replyId,addtime")})
@Data
public class ReplyLike extends ReplyLikeEntity implements Serializable {
    private static final long serialVersionUID = 3112294654438891737L;


}
