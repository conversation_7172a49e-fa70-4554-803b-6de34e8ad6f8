package cms.constraints.validator;

import cms.constraints.ContentSpString;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/4/23 15:00
 */
public class ContentSpStringConstraintValidator implements ConstraintValidator<ContentSpString, String> {
    @Override
    public void initialize(ContentSpString contentSpString) {

    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        return Optional.ofNullable(value).map(String::trim).filter(StringUtils::isNotEmpty).map(o -> {
            return !o.contains(" ");
        }).orElse(Boolean.TRUE);
    }
}
