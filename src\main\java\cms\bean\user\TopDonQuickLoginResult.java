package cms.bean.user;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16 10:39
 */
@NoArgsConstructor
@Data
public class TopDonQuickLoginResult {


    private Integer code;
    private String msg;
    private Boolean success;
    private DataDTO data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        private String token;
        private String avatar;
        private String userName;
        private Integer gender;
        private String phone;
        private String email;
        private String topdonId;
        private Integer userId;
        private String refreshToken;
        private Integer expiresIn;
    }
}
