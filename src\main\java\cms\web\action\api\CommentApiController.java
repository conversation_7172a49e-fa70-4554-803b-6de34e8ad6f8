package cms.web.action.api;

import cms.bean.RequestResult;
import cms.bean.api.CommentApi;
import cms.handle.CustomException;
import cms.service.topic.impl.CommentFormService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/3 10:33
 */
@Controller
@RequestMapping("api/control/comment")
@Validated
@Slf4j
public class CommentApiController {

    @Autowired
    private CommentFormService commentFormService;

    @ResponseBody
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public RequestResult add(@RequestBody CommentApi commentApi, HttpServletRequest httpServletRequest) {
        try {
            Long id = commentFormService.addByApi(commentApi, httpServletRequest);
            return RequestResult.ok("");
        } catch (CustomException e) {
            e.setResultCode(Boolean.TRUE);
            throw e;
        }
    }
}
