package cms.service.thirdParty;

import cms.bean.thirdParty.ThirdPartyLoginInterface;
import cms.service.besa.DAO;

import java.util.List;

/**
 * 第三方登录管理接口
 */
public interface ThirdPartyLoginService extends DAO<ThirdPartyLoginInterface> {
    /**
     * 根据Id查询第三方登录接口
     *
     * @param thirdPartyLoginInterfaceId 第三方登录接口Id
     * @return
     */
    ThirdPartyLoginInterface findThirdPartyLoginInterfaceById(Integer thirdPartyLoginInterfaceId);

    /**
     * 查询所有有效的第三方登录接口
     *
     * @return
     */
    List<ThirdPartyLoginInterface> findAllValidThirdPartyLoginInterface();

    /**
     * 查询所有有效的第三方登录接口(缓存)
     *
     * @return
     */
    List<ThirdPartyLoginInterface> findAllValidThirdPartyLoginInterface_cache();

    /**
     * 查询所有第三方登录接口
     *
     * @return
     */
    List<ThirdPartyLoginInterface> findAllThirdPartyLoginInterface();

    /**
     * 保存第三方登录接口
     *
     * @param thirdPartyLoginInterface 第三方登录接口
     */
    void saveThirdPartyLoginInterface(ThirdPartyLoginInterface thirdPartyLoginInterface);

    /**
     * 修改第三方登录接口
     *
     * @param thirdPartyLoginInterface 第三方登录接口
     */
    void updateThirdPartyLoginInterface(ThirdPartyLoginInterface thirdPartyLoginInterface);


    /**
     * 删除第三方登录接口
     *
     * @param thirdPartyLoginInterfaceId 第三方登录接口Id
     */
    Integer deleteThirdPartyLoginInterface(Integer thirdPartyLoginInterfaceId);

}
