package cms.service.follow.impl;

import cms.bean.ErrorView;
import cms.bean.follow.Follow;
import cms.bean.follow.Follower;
import cms.bean.message.EmailRemind;
import cms.bean.message.Remind;
import cms.bean.setting.SystemSetting;
import cms.bean.user.AccessUser;
import cms.bean.user.User;
import cms.constant.ErrorCode;
import cms.handle.CustomException;
import cms.service.follow.FollowService;
import cms.service.message.RemindService;
import cms.service.message.impl.EmailRemindService;
import cms.service.setting.SettingService;
import cms.service.template.TemplateService;
import cms.utils.Base64;
import cms.utils.JsonUtils;
import cms.utils.RefererCompare;
import cms.utils.WebUtil;
import cms.utils.threadLocal.AccessUserThreadLocal;
import cms.web.action.AccessSourceDeviceManage;
import cms.web.action.CSRFTokenManage;
import cms.web.action.follow.FollowManage;
import cms.web.action.follow.FollowerManage;
import cms.web.action.message.RemindManage;
import cms.web.action.user.UserManage;
import cms.web.taglib.Configuration;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static cms.constant.ErrorCode.*;
import static cms.web.action.message.RemindManage.TYPE_CODE_FOLLOW;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/18 17:50
 */
@Service
@Slf4j
public class FollowFormService {

    @Resource
    TemplateService templateService;
    @Resource
    AccessSourceDeviceManage accessSourceDeviceManage;
    @Resource
    RemindService remindService;
    @Resource
    RemindManage remindManage;
    @Resource
    UserManage userManage;
    @Autowired
    private FollowService followService;
    @Resource
    FollowManage followManage;
    @Resource
    FollowerManage followerManage;
    @Resource
    CSRFTokenManage csrfTokenManage;
    @Resource
    SettingService settingService;

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public String add(ModelMap model, String userName, String token, String jumpUrl,
                      RedirectAttributes redirectAttrs,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {

        boolean isAjax = WebUtil.submitDataMode(request);//是否以Ajax方式提交数据


        Map<String, String> error = new HashMap<String, String>();

        SystemSetting systemSetting = settingService.findSystemSetting_cache();
        if (systemSetting.getCloseSite().equals(2)) {
            error.put("follow", ErrorView._21.name());//只读模式不允许提交数据
        }

        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token, error);

        //获取登录用户
        AccessUser accessUser = AccessUserThreadLocal.get();

        User user = null;
        if (userName != null && !"".equals(userName.trim())) {
            user = userManage.query_cache_findUserByUserName(userName.trim());//查询缓存
        } else {
            error.put("follow", ErrorView._815.name());//用户名称不能为空
        }

        if (user != null) {
            //关注Id
            String followId = followManage.createFollowId(accessUser.getUserId(), user.getId());

            Follow follow = followManage.query_cache_findById(followId);

            if (follow != null) {
                error.put("follow", ErrorView._1820.name());//当前用户已关注对方
            }

            if (user.getId().equals(accessUser.getUserId())) {
                error.put("follow", ErrorView._1870.name());//不能关注自身
            }

        } else {
            error.put("follow", ErrorView._859.name());//用户不存在
        }

        if (error.size() == 0) {
            Date time = new Date();
            Follow follow = new Follow();
            follow.setId(followManage.createFollowId(accessUser.getUserId(), user.getId()));
            follow.setAddtime(time);
            follow.setUserName(accessUser.getUserName());
            follow.setFriendUserName(user.getUserName());

            Follower follower = new Follower();
            follower.setId(followerManage.createFollowerId(user.getId(), accessUser.getUserId()));
            follower.setAddtime(time);
            follower.setUserName(user.getUserName());
            follower.setFriendUserName(accessUser.getUserName());
            try {
                //保存关注
                followService.saveFollow(followManage.createFollowObject(follow), followerManage.createFollowerObject(follower));

                //删除关注缓存
                followManage.delete_cache_findById(follow.getId());
                followerManage.delete_cache_followerCount(user.getUserName());
                followManage.delete_cache_findAllFollow(accessUser.getUserName());
                followManage.delete_cache_followCount(accessUser.getUserName());

                if (user != null && !user.getId().equals(accessUser.getUserId())) {//会员关注自已不发提醒给自己

                    //提醒对方
                    Remind remind = new Remind();
                    remind.setId(remindManage.createRemindId(user.getId()));
                    remind.setReceiverUserId(user.getId());//接收提醒用户Id
                    remind.setSenderUserId(accessUser.getUserId());//发送用户Id
                    remind.setTypeCode(TYPE_CODE_FOLLOW);//80.别人关注了我
                    remind.setSendTimeFormat(time.getTime());//发送时间格式化


                    Object remind_object = remindManage.createRemindObject(remind);
                    remindService.saveRemind(remind_object);
                    Optional.ofNullable(user).filter(User::getEmailRemind).ifPresent(o -> EmailRemindService.addEmailRemind(new EmailRemind(follower.getId(), accessUser.getUserId(), accessUser.getUserName(), TYPE_CODE_FOLLOW, Optional.of(o))));

                    //删除提醒缓存
                    remindManage.delete_cache_findUnreadRemindByUserId(user.getId());
                }

            } catch (org.springframework.orm.jpa.JpaSystemException e) {
                error.put("follow", ErrorView._1800.name());//重复关注

            }

        }

        Map<String, String> returnError = new HashMap<String, String>();//错误
        if (error.size() > 0) {
            //将枚举数据转为错误提示字符
            for (Map.Entry<String, String> entry : error.entrySet()) {
                if (ErrorView.get(entry.getValue()) != null) {
                    returnError.put(entry.getKey(), ErrorView.get(entry.getValue()));
                } else {
                    returnError.put(entry.getKey(), entry.getValue());
                }
            }
        }
        if (isAjax == true) {

            Map<String, Object> returnValue = new HashMap<String, Object>();//返回值

            if (error != null && error.size() > 0) {
                returnValue.put("success", "false");
                returnValue.put("error", returnError);
            } else {
                returnValue.put("success", "true");
            }
            WebUtil.writeToWeb(JsonUtils.toJSONString(returnValue), "json", response);
            return null;
        } else {


            if (error != null && error.size() > 0) {//如果有错误
                redirectAttrs.addFlashAttribute("error", returnError);//重定向传参


                String referer = request.getHeader("referer");


                referer = StringUtils.removeStartIgnoreCase(referer, Configuration.getUrl(request));//移除开始部分的相同的字符,不区分大小写
                referer = StringUtils.substringBefore(referer, ".");//截取到等于第二个参数的字符串为止
                referer = StringUtils.substringBefore(referer, "?");//截取到等于第二个参数的字符串为止

                String queryString = request.getQueryString() != null && !"".equals(request.getQueryString().trim()) ? "?" + request.getQueryString() : "";

                return "redirect:/" + referer + queryString;

            }


            if (jumpUrl != null && !"".equals(jumpUrl.trim())) {
                String url = Base64.decodeBase64URL(jumpUrl.trim());

                return "redirect:" + url;
            } else {//默认跳转
                model.addAttribute("message", "关注该用户成功");
                String referer = request.getHeader("referer");
                if (RefererCompare.compare(request, "login")) {//如果是登录页面则跳转到首页
                    referer = Configuration.getUrl(request);
                }
                model.addAttribute("urlAddress", referer);

                String dirName = templateService.findTemplateDir_cache();


                return "/templates/" + dirName + "/" + accessSourceDeviceManage.accessDevices(request) + "/jump";
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteFollower(HttpServletRequest request, String token, String followerId) {
        Optional.ofNullable(settingService.findSystemSetting_cache()).map(SystemSetting::getCloseSite).filter(o -> !o.equals(2)).orElseThrow(() -> new CustomException(ErrorCode.C_2_0001_0001, "follow"));
        AccessUser accessUser = AccessUserThreadLocal.get();
        //处理CSRF令牌
        csrfTokenManage.processCsrfToken(request, token);
        Follower follower = followService.findFollowerById(followerId).orElseThrow(() -> new CustomException(C_2_0010_0001, "follow"));
        Optional.ofNullable(follower.getUserName()).filter(accessUser.getUserName()::equals).orElseThrow(() -> new CustomException(C_2_0010_0002, "follow"));
        String[] idGroup = followerId.trim().split("-");
        String followId = idGroup[1] + "-" + idGroup[0];
        int i = followService.deleteFollow(followId.trim(), followerId);
        if (i == 0) {
            throw new CustomException(C_2_0010_0003, "follow");
        }
        //删除关注缓存
        followManage.delete_cache_findById(followId.trim());
        followerManage.delete_cache_followerCount(follower.getUserName());

        followManage.delete_cache_findAllFollow(follower.getFriendUserName());
        followManage.delete_cache_followCount(follower.getFriendUserName());
    }
}
