package cms.web.action.question;

import cms.bean.*;
import cms.bean.question.*;
import cms.bean.user.User;
import cms.service.aliyun.OssFileChangeService;
import cms.service.question.AnswerService;
import cms.service.question.QuestionService;
import cms.service.question.QuestionTagService;
import cms.service.question.impl.QuestionManageService;
import cms.service.setting.SettingService;
import cms.utils.IpAddress;
import cms.utils.JsonUtils;
import cms.web.action.fileSystem.FileManage;
import cms.web.action.user.UserManage;
import cms.web.action.user.UserRoleManage;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;


/**
 * 问题管理
 */
@Controller
@RequestMapping("/control/question/manage")
public class QuestionManageAction {

    @Resource
    QuestionService questionService;
    @Resource
    SettingService settingService;
    @Resource
    AnswerManage answerManage;
    @Resource
    AnswerService answerService;
    @Resource
    QuestionTagService questionTagService;
    @Resource
    UserManage userManage;
    @Resource
    FileManage fileManage;
    @Resource
    UserRoleManage userRoleManage;
    @Autowired
    private OssFileChangeService ossFileChangeService;
    @Autowired
    private QuestionManageService questionManageService;

    /**
     * 问题   查看
     *
     * @param model
     * @param questionId
     * @param answerId
     * @param page
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(params = "method=view", method = RequestMethod.GET)
    public String view(ModelMap model, Long questionId, Long answerId, Integer page,
                       HttpServletRequest request, HttpServletResponse response) throws Exception {

        Map<String, String> error = new HashMap<String, String>();
        Map<String, Object> returnValue = new LinkedHashMap<String, Object>();

        if (questionId != null && questionId > 0L) {
            Question question = questionService.findById(questionId);
            if (question != null) {
                if (question.getIp() != null && !"".equals(question.getIp().trim())) {
                    question.setIpAddress(IpAddress.queryAddress(question.getIp().trim()));
                }
                if (question.getContent() != null && !"".equals(question.getContent().trim())) {
                    //处理富文本路径
                    question.setContent(fileManage.processRichTextFilePath(question.getContent(), "question"));
                }

                //删除最后一个逗号
                String _appendContent = StringUtils.substringBeforeLast(question.getAppendContent(), ",");//从右往左截取到相等的字符,保留左边的

                List<AppendQuestionItem> appendQuestionItemList = JsonUtils.toGenericObject(_appendContent + "]", new TypeReference<List<AppendQuestionItem>>() {
                });
                if (appendQuestionItemList != null && appendQuestionItemList.size() > 0) {
                    for (AppendQuestionItem appendQuestionItem : appendQuestionItemList) {
                        if (appendQuestionItem.getContent() != null && !"".equals(appendQuestionItem.getContent().trim())) {
                            //处理富文本路径
                            appendQuestionItem.setContent(fileManage.processRichTextFilePath(appendQuestionItem.getContent(), "question"));
                        }
                    }

                }

                question.setAppendQuestionItemList(appendQuestionItemList);


                if (question.getIsStaff() == false) {//会员
                    User user = userManage.query_cache_findUserByUserName(question.getUserName());
                    if (user != null) {
                        question.setAccount(user.getAccount());
                        question.setNickname(user.getNickname());
                        question.setAvatarPath(user.getAvatarPath(fileManage.fileOssAddress()));
                        question.setAvatarName(user.getAvatarName());

                        List<String> userRoleNameList = userRoleManage.queryUserRoleName(user.getUserName());
                        if (userRoleNameList != null && userRoleNameList.size() > 0) {
                            question.setUserRoleNameList(userRoleNameList);//用户角色名称集合
                        }
                    }

                } else {
                    question.setAccount(question.getUserName());//员工用户名和账号是同一个

                }
                List<QuestionTag> questionTagList = questionTagService.findAllQuestionTag();

                if (questionTagList != null && questionTagList.size() > 0) {
                    List<QuestionTagAssociation> questionTagAssociationList = questionService.findQuestionTagAssociationByQuestionId(question.getId());
                    if (questionTagAssociationList != null && questionTagAssociationList.size() > 0) {
                        for (QuestionTag questionTag : questionTagList) {
                            for (QuestionTagAssociation questionTagAssociation : questionTagAssociationList) {
                                if (questionTagAssociation.getQuestionTagId().equals(questionTag.getId())) {
                                    questionTagAssociation.setQuestionTagName(questionTag.getName());
                                    question.addQuestionTagAssociation(questionTagAssociation);
                                    break;
                                }
                            }
                        }
                    }
                    question.setQuestionTagAssociationList(questionTagAssociationList);
                }

                returnValue.put("question", question);
                returnValue.put("availableTag", answerManage.availableTag());

                PageForm pageForm = new PageForm();
                pageForm.setPage(page);

                if (answerId != null && answerId > 0L && page == null) {
                    Long row = answerService.findRowByAnswerId(answerId, questionId);
                    if (row != null && row > 0L) {

                        Integer _page = Integer.parseInt(String.valueOf(row)) / settingService.findSystemSetting_cache().getBackstagePageNumber();
                        if (Integer.parseInt(String.valueOf(row)) % settingService.findSystemSetting_cache().getBackstagePageNumber() > 0) {//余数大于0要加1

                            _page = _page + 1;
                        }
                        pageForm.setPage(_page);

                    }
                }


                //答案
                StringBuffer jpql = new StringBuffer();
                //存放参数值
                List<Object> params = new ArrayList<Object>();
                PageView<Answer> pageView = new PageView<Answer>(settingService.findSystemSetting_cache().getBackstagePageNumber(), pageForm.getPage(), 10);


                //当前页
                int firstindex = (pageForm.getPage() - 1) * pageView.getMaxresult();
                //排序
                LinkedHashMap<String, String> orderby = new LinkedHashMap<String, String>();

                if (questionId != null && questionId > 0L) {
                    jpql.append(" o.questionId=?" + (params.size() + 1));//所属父类的ID;(params.size()+1)是为了和下面的条件参数兼容
                    params.add(questionId);//设置o.parentId=?2参数
                }

                orderby.put("postTime", "asc");//根据sort字段降序排序
                QueryResult<Answer> qr = answerService.getScrollData(Answer.class, firstindex, pageView.getMaxresult(), jpql.toString(), params.toArray(), orderby);


                List<Long> answerIdList = new ArrayList<Long>();
                List<Answer> answerList = qr.getResultlist();


                if (answerList != null && answerList.size() > 0) {
                    for (Answer answer : answerList) {
                        answerIdList.add(answer.getId());
                        if (answer.getContent() != null && !"".equals(answer.getContent().trim())) {
                            //处理富文本路径
                            answer.setContent(fileManage.processRichTextFilePath(answer.getContent(), "answer"));
                        }

                    }
                }

                Map<String, List<String>> userRoleNameMap = new HashMap<String, List<String>>();//用户角色名称 key:用户名称Id 角色名称集合
                if (answerList != null && answerList.size() > 0) {
                    for (Answer answer : answerList) {
                        if (answer.getIsStaff() == false) {//会员
                            User user = userManage.query_cache_findUserByUserName(answer.getUserName());
                            if (user != null) {
                                answer.setAccount(user.getAccount());
                                answer.setNickname(user.getNickname());
                                answer.setAvatarPath(user.getAvatarPath(fileManage.fileOssAddress()));
                                answer.setAvatarName(user.getAvatarName());
                                userRoleNameMap.put(answer.getUserName(), null);
                            }
                        } else {
                            answer.setAccount(answer.getUserName());//员工用户名和账号是同一个

                        }

                        if (answer.getIp() != null && !"".equals(answer.getIp().trim())) {
                            answer.setIpAddress(IpAddress.queryAddress(answer.getIp()));
                        }
                    }

                }
                if (answerIdList != null && answerIdList.size() > 0) {
                    List<AnswerReply> answerReplyList = answerService.findReplyByAnswerId(answerIdList);
                    if (answerReplyList != null && answerReplyList.size() > 0) {
                        for (Answer answer : answerList) {
                            for (AnswerReply answerReply : answerReplyList) {
                                if (answerReply.getIsStaff() == false) {//会员
                                    User user = userManage.query_cache_findUserByUserName(answerReply.getUserName());
                                    if (user != null) {
                                        answerReply.setAccount(user.getAccount());
                                        answerReply.setNickname(user.getNickname());
                                        answerReply.setAvatarPath(user.getAvatarPath(fileManage.fileOssAddress()));
                                        answerReply.setAvatarName(user.getAvatarName());
                                        userRoleNameMap.put(answerReply.getUserName(), null);
                                    }

                                } else {
                                    answerReply.setAccount(answerReply.getUserName());//员工用户名和账号是同一个

                                }
                                if (answer.getId().equals(answerReply.getAnswerId())) {
                                    answer.addAnswerReply(answerReply);
                                }
                            }

                        }
                    }
                }
                if (userRoleNameMap != null && userRoleNameMap.size() > 0) {
                    for (Map.Entry<String, List<String>> entry : userRoleNameMap.entrySet()) {
                        List<String> roleNameList = userRoleManage.queryUserRoleName(entry.getKey());
                        entry.setValue(roleNameList);
                    }
                }
                if (answerList != null && answerList.size() > 0) {
                    for (Answer answer : answerList) {
                        //用户角色名称集合
                        for (Map.Entry<String, List<String>> entry : userRoleNameMap.entrySet()) {
                            if (entry.getKey().equals(answer.getUserName())) {
                                List<String> roleNameList = entry.getValue();
                                if (roleNameList != null && roleNameList.size() > 0) {
                                    answer.setUserRoleNameList(roleNameList);
                                }
                                break;
                            }
                        }

                        if (answer.getAnswerReplyList() != null && answer.getAnswerReplyList().size() > 0) {
                            for (AnswerReply reply : answer.getAnswerReplyList()) {
                                for (Map.Entry<String, List<String>> entry : userRoleNameMap.entrySet()) {
                                    if (entry.getKey().equals(reply.getUserName())) {
                                        List<String> roleNameList = entry.getValue();
                                        if (roleNameList != null && roleNameList.size() > 0) {
                                            reply.setUserRoleNameList(roleNameList);
                                        }
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }


                //将查询结果集传给分页List
                pageView.setQueryResult(qr);
                returnValue.put("pageView", pageView);

                String username = "";//用户名称

                Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
                if (obj instanceof UserDetails) {
                    username = ((UserDetails) obj).getUsername();
                }
                returnValue.put("userName", username);

                return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, returnValue));
            } else {
                error.put("questionId", "问题不存在");
            }
        } else {
            error.put("questionId", "问题Id参数不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    /**
     * 问题   添加界面显示
     */
    @ResponseBody
    @RequestMapping(params = "method=add", method = RequestMethod.GET)
    public String addUI(Question question, ModelMap model,
                        HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, Object> returnValue = new HashMap<String, Object>();

        String username = "";//用户名称

        Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (obj instanceof UserDetails) {
            username = ((UserDetails) obj).getUsername();
        }
        returnValue.put("userName", username);
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, returnValue));
    }

    /**
     * 问题  添加
     */
    @ResponseBody
    @RequestMapping(params = "method=add", method = RequestMethod.POST)
    public String add(ModelMap model, Long[] tagId, String tagName, String title, Boolean allow, Integer status, String point,
                      String content, String sort,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {
        return questionManageService.add(model, tagId, tagName, title, allow, status, point, content, sort, request, response);
    }


    /**
     * 问题   追加界面显示
     */
    @ResponseBody
    @RequestMapping(params = "method=appendQuestion", method = RequestMethod.GET)
    public String appendQuestionUI(Long questionId, ModelMap model,
                                   HttpServletRequest request, HttpServletResponse response) throws Exception {

        Map<String, String> error = new HashMap<String, String>();
        Map<String, Object> returnValue = new LinkedHashMap<String, Object>();
        if (questionId != null && questionId > 0L) {
            Question question = questionService.findById(questionId);
            if (question != null) {
                returnValue.put("question", question);
            } else {
                error.put("questionId", "问题不存在");
            }
        } else {
            error.put("questionId", "问题Id参数不能为空");
        }
        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, returnValue));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }


    /**
     * 问题  追加
     *
     * @param model
     * @param questionId 问题Id
     * @param content    追加内容
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(params = "method=appendQuestion", method = RequestMethod.POST)
    public String appendQuestion(ModelMap model, Long questionId, String content,
                                 HttpServletRequest request, HttpServletResponse response) throws Exception {
        return questionManageService.appendQuestion(model, questionId, content, request, response);
    }


    /**
     * 文件上传
     * <p>
     * 员工发问题 上传文件名为UUID + a + 员工Id
     * 用户发问题 上传文件名为UUID + b + 用户Id
     *
     * @param model
     * @param dir      上传类型，分别为image、flash、media、file
     * @param userName 用户名称
     * @param isStaff  是否是员工   true:员工   false:会员
     * @param fileName 文件名称 预签名时有值
     * @param file
     * @param response
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(params = "method=upload", method = RequestMethod.POST)
    public String upload(ModelMap model, String dir, String userName, Boolean isStaff, String fileName,
                         MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        return ossFileChangeService.upload(file, dir, userName, isStaff, "question");
    }

    /**
     * 问题   修改界面显示
     */
    @ResponseBody
    @RequestMapping(params = "method=editQuestion", method = RequestMethod.GET)
    public String editQuestionUI(ModelMap model, Long questionId,
                                 HttpServletRequest request, HttpServletResponse response) throws Exception {

        Map<String, String> error = new HashMap<String, String>();
        Map<String, Object> returnValue = new HashMap<String, Object>();

        if (questionId != null && questionId > 0L) {
            Question question = questionService.findById(questionId);
            if (question != null) {
                if (question.getContent() != null && !"".equals(question.getContent().trim())) {
                    //处理富文本路径
                    question.setContent(fileManage.processRichTextFilePath(question.getContent(), "question"));
                }

                List<QuestionTag> questionTagList = questionTagService.findAllQuestionTag();
                if (questionTagList != null && questionTagList.size() > 0) {

                    List<QuestionTagAssociation> questionTagAssociationList = questionService.findQuestionTagAssociationByQuestionId(question.getId());
                    if (questionTagAssociationList != null && questionTagAssociationList.size() > 0) {
                        for (QuestionTag questionTag : questionTagList) {
                            for (QuestionTagAssociation questionTagAssociation : questionTagAssociationList) {
                                if (questionTagAssociation.getQuestionTagId().equals(questionTag.getId())) {
                                    questionTagAssociation.setQuestionTagName(questionTag.getName());
                                    question.addQuestionTagAssociation(questionTagAssociation);
                                    break;
                                }
                            }
                        }
                    }

                }

                User user = userManage.query_cache_findUserByUserName(question.getUserName());
                if (user != null) {
                    returnValue.put("maxDeposit", user.getDeposit());//允许使用的预存款
                    returnValue.put("maxPoint", user.getPoint());//允许使用的积分
                }
                returnValue.put("question", question);
                return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, returnValue));
            } else {
                error.put("questionId", "问题不存在");
            }
        } else {
            error.put("questionId", "问题Id不能为空");
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    /**
     * 问题   修改
     *
     * @param model
     * @param pageForm
     * @param tagId
     * @param title
     * @param content
     * @param sort
     * @param visible
     */
    @ResponseBody
    @RequestMapping(params = "method=editQuestion", method = RequestMethod.POST)
    public String editQuestion(ModelMap model, Long questionId, Long[] tagId,
                               String title, Boolean allow, Integer status, String point, String amount,
                               String content, String sort, Boolean visible,
                               HttpServletRequest request, HttpServletResponse response) throws Exception {
        return questionManageService.editQuestion(model, questionId, tagId, title, allow, status, point, amount, content, sort, visible, request, response);
    }


    /**
     * 问题   修改追加界面显示
     */
    @ResponseBody
    @RequestMapping(params = "method=editAppendQuestion", method = RequestMethod.GET)
    public String editAppendQuestionUI(Long questionId, String appendQuestionItemId, ModelMap model,
                                       HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, String> error = new HashMap<String, String>();
        Map<String, Object> returnValue = new LinkedHashMap<String, Object>();

        if (questionId != null && questionId > 0L) {
            Question question = questionService.findById(questionId);
            if (question != null) {
                //删除最后一个逗号
                String _appendContent = StringUtils.substringBeforeLast(question.getAppendContent(), ",");//从右往左截取到相等的字符,保留左边的

                List<AppendQuestionItem> appendQuestionItemList = JsonUtils.toGenericObject(_appendContent + "]", new TypeReference<List<AppendQuestionItem>>() {
                });
                if (appendQuestionItemList != null && appendQuestionItemList.size() > 0) {
                    for (AppendQuestionItem appendQuestionItem : appendQuestionItemList) {
                        if (appendQuestionItem.getId().equals(appendQuestionItemId)) {

                            if (appendQuestionItem.getContent() != null && !"".equals(appendQuestionItem.getContent().trim())) {
                                //处理富文本路径
                                appendQuestionItem.setContent(fileManage.processRichTextFilePath(appendQuestionItem.getContent(), "question"));
                            }

                            returnValue.put("appendQuestionItem", appendQuestionItem);
                            break;
                        }
                    }
                }

                returnValue.put("question", question);
            } else {
                error.put("questionId", "问题不存在");
            }
        } else {
            error.put("questionId", "问题Id参数不能为空");
        }
        if (error.size() == 0) {

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, returnValue));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));

    }


    /**
     * 问题   追加修改
     *
     * @param model
     * @param questionId
     * @param appendQuestionItemId
     * @param content
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(params = "method=editAppendQuestion", method = RequestMethod.POST)
    public String editAppendQuestion(ModelMap model, Long questionId, String appendQuestionItemId, String content,
                                     HttpServletRequest request, HttpServletResponse response) throws Exception {
        return questionManageService.editAppendQuestion(model, questionId, appendQuestionItemId, content, request, response);
    }


    /**
     * 问题   删除
     *
     * @param model
     * @param questionId
     */
    @ResponseBody
    @RequestMapping(params = "method=deleteQuestion", method = RequestMethod.POST)
    public String deleteQuestion(ModelMap model, Long[] questionId,
                                 HttpServletRequest request, HttpServletResponse response) throws Exception {
        return questionManageService.deleteQuestion(model, questionId, request, response);
    }


    /**
     * 追加问题   删除
     *
     * @param model
     * @param questionId
     * @param appendQuestionItemId
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(params = "method=deleteAppendQuestion", method = RequestMethod.POST)
    public String deleteAppendQuestion(ModelMap model, Long questionId, String appendQuestionItemId,
                                       HttpServletRequest request, HttpServletResponse response) throws Exception {
        return questionManageService.deleteAppendQuestion(model, questionId, appendQuestionItemId, request, response);
    }

    /**
     * 还原
     *
     * @param model
     * @param questionId 问题Id集合
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(params = "method=reduction", method = RequestMethod.POST)
    public String reduction(ModelMap model, Long[] questionId,
                            HttpServletResponse response) throws Exception {
        return questionManageService.reduction(model, questionId, response);
    }

    /**
     * 审核问题
     *
     * @param model
     * @param questionId 问题Id
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(params = "method=auditQuestion", method = RequestMethod.POST)
    public String auditQuestion(ModelMap model, Long questionId,
                                HttpServletResponse response) throws Exception {
        return questionManageService.auditQuestion(model, questionId, response);
    }
}
