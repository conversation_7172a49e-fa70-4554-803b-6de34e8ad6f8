package cms.bean.topic;

import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 标签
 */
@Entity
@Table(indexes = {@Index(name = "tag_1_idx", columnList = "sort")})
@Data
public class Tag implements Serializable {
    private static final long serialVersionUID = -7672182879689718269L;

    /**
     * id
     **/
    @Id
    private Long id;

    /**
     * 上级Id
     */
    @Column(nullable = false)
    private Long parentId;

    @Column(nullable = false)
    private Integer grade;

    /**
     * 标签名称
     **/
    @Column(length = 190, nullable = false)
    @NotEmpty
    private String name;
    /**
     * 排序
     **/
    @Column(nullable = false)
    @NotNull
    private Integer sort = 0;

    /**
     * 标签下的话题允许查看的角色名称集合(默认角色除外)
     **/
    @Transient
    private List<String> allowRoleViewList = new ArrayList<String>();

    /**
     * 二级标签数量
     */
    @Transient
    private Integer chindrenNums;
}
