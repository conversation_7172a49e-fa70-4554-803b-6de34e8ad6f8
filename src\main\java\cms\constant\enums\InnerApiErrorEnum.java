package cms.constant.enums;

public enum InnerApiErrorEnum {
    AUTH_FAIL_INVALID_CLIENTID(401, "invalid clientId header"),
    AUTH_FAIL_STAMP_UNSET(401, "invalid stamp header: can't be empty"),
    AUTH_FAIL_STAMP_FORMAT(401, "invalid stamp header: wrong format"),
    AUTH_FAIL_STAMP_TIME(401, "invalid stamp header: invalid time"),
    AUTH_FAIL_SIGN_UNSET(401, "invalid sign header: can't be empty"),
    AUTH_FAIL_SIGN_MISMATCH(401, "invalid sign header: sign mismatch"),

    AUTH_FAIL_URL_FORBID(401, "unauthorized API URI(API接口未授权)"),

    REQUEST_GT_LIMIT(500, "current request too many,please try again later"),
    ;
    private int code;
    private String msg;

    private InnerApiErrorEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
