package cms.web.action.filterWord;

import cms.bean.BaseUserDel;
import cms.bean.FilterWord.FilterWord;
import cms.bean.RequestResult;
import cms.bean.ResultCode;
import cms.bean.keywordDict.KeywordDict;
import cms.constant.ErrorCode;
import cms.handle.CustomException;
import cms.service.keywordDict.KeywordDictService;
import cms.utils.JsonUtils;
import cms.web.action.keywordDict.KeywordDictManageAction;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cms.constant.Constant.WAIT_SECONDS;

/**
 * 过滤词管理
 */
@Controller
@RequestMapping("/control/filterWord/manage")
@Slf4j
public class FilterWordManageAction {

    @Autowired
    private KeywordDictService keywordDictService;
    @Autowired
    private SensitiveWordFilterManage sensitiveWordFilterManage;


    /**
     * 过滤词展示
     */
    @ResponseBody
    @RequestMapping(params = "method=view", method = RequestMethod.GET)
    public String view(ModelMap model,
                       HttpServletRequest request, HttpServletResponse response) throws Exception {

        Optional<List<KeywordDict>> keywordDicts = Optional.ofNullable(keywordDictService.findAllKeyword_cache()).filter(CollUtil::isNotEmpty);
        FilterWord filterWord = new FilterWord();
        filterWord.setWordNumber(keywordDicts.map(List::size).orElse(null));
        filterWord.setBeforeWordList(IntStream.range(0, 3).boxed().map(i -> keywordDicts.filter(l -> l.size() > i).map(l -> l.get(i)).map(KeywordDict::getKeyWord).orElse("")).filter(StrUtil::isNotEmpty).collect(Collectors.toList()));
        filterWord.setSize(keywordDicts.map(List::size).map(String::valueOf).orElse(null));
        filterWord.setLastModified(keywordDicts.map(l -> l.stream().map(BaseUserDel::getCreateTime).max(Comparator.comparing(o -> o)).orElse(null)).orElse(null));

        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, filterWord));
    }

    /**
     * 删除词库
     */
    @ResponseBody
    @RequestMapping(params = "method=deleteFilterWord", method = RequestMethod.POST)
    public String deleteFilterWord(ModelMap model,
                                   HttpServletRequest request, HttpServletResponse response) throws Exception {
        if (!KeywordDictManageAction.semaphore.tryAcquire(WAIT_SECONDS, TimeUnit.SECONDS)) {
            throw new CustomException(ErrorCode.C_1_0001_0012, "id");
        }
        try {
            keywordDictService.delAll();
            sensitiveWordFilterManage.clearWord();
            log.info(String.format("keyword: %s", sensitiveWordFilterManage.getAllKeywords().stream().collect(Collectors.joining(","))));
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        } finally {
            KeywordDictManageAction.semaphore.release();
        }
    }
}
