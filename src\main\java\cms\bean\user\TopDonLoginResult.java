package cms.bean.user;

import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16 10:39
 */
@Data
public class TopDonLoginResult {

    private Integer code;
    private String access_token;
    private String token_type;
    private String refresh_token;
    private Integer expires_in;
    private Integer user_type;
    private Integer user_id;

    public TopDonLoginResult() {

    }

    public TopDonLoginResult(Integer user_id) {
        this.user_id = user_id;
    }
}
