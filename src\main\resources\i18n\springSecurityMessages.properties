AuthByAdapterProvider.incorrectKey=\u5C55\u793A\u7684AuthByAdapter\u5B9E\u73B0\u4E0D\u542B\u6709\u9884\u671F\u7684key
BasicAclEntryAfterInvocationProvider.noPermission=\u7ED9\u5B9A\u7684Authentication\u5BF9\u8C61({0})\u6839\u672C\u65E0\u6743\u64CD\u63A7\u9886\u57DF\u5BF9\u8C61({1})
BasicAclEntryAfterInvocationProvider.insufficientPermission=Authentication\u5BF9\u8C61({0})\u786E\u5B9E\u542B\u6709ACL\u6743\u9650\uFF0C\u4F46\u5E76\u4E0D\u662F\u76EE\u6807\u9886\u57DF\u5BF9\u8C61\u6240\u8981\u6C42\u7684({1})
ConcurrentSessionControllerImpl.exceededAllowed=\u5F53\u524D\u8D26\u53F7\u5DF2\u7ECF\u767B\u5F55({0}),\u8D85\u8FC7\u5141\u8BB8\u767B\u9646\u6700\u5927\u4F1A\u8BDD\u6570\u91CF
ProviderManager.providerNotFound=\u672A\u67E5\u627E\u5230\u9488\u5BF9{0}\u7684AuthenticationProvider
AnonymousAuthenticationProvider.incorrectKey=\u5C55\u793A\u7684AnonymousAuthenticationToken\u4E0D\u542B\u6709\u9884\u671F\u7684key
CasAuthenticationProvider.incorrectKey=\u5C55\u793A\u7684CasAuthenticationToken\u4E0D\u542B\u6709\u9884\u671F\u7684key
CasAuthenticationProvider.noServiceTicket=\u672A\u80FD\u591F\u6B63\u786E\u63D0\u4F9B\u5F85\u9A8C\u8BC1\u7684CAS\u670D\u52A1\u7968\u6839
NamedCasProxyDecider.untrusted=\u4EE3\u7406({0})\u4E0D\u53D7\u4FE1\u4EFB
RejectProxyTickets.reject=Proxy\u7968\u6839\u88AB\u62D2\u7EDD
AbstractSecurityInterceptor.authenticationNotFound=\u672A\u5728SecurityContext\u4E2D\u67E5\u627E\u5230\u8BA4\u8BC1\u5BF9\u8C61
AbstractUserDetailsAuthenticationProvider.onlySupports=\u4EC5\u4EC5\u652F\u6301UsernamePasswordAuthenticationToken
AbstractUserDetailsAuthenticationProvider.locked=\u7528\u6237\u5E10\u53F7\u5DF2\u88AB\u9501\u5B9A
AbstractUserDetailsAuthenticationProvider.disabled=\u7528\u6237\u5DF2\u5931\u6548
AbstractUserDetailsAuthenticationProvider.expired=\u7528\u6237\u5E10\u53F7\u5DF2\u8FC7\u671F
AbstractUserDetailsAuthenticationProvider.credentialsExpired=\u7528\u6237\u51ED\u8BC1\u5DF2\u8FC7\u671F
AbstractUserDetailsAuthenticationProvider.badCredentials=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
ApplicationDetailsService\ returned\ null,\ which\ is\ an\ interface\ contract\ violation=jhgjhgj
X509AuthenticationProvider.certificateNull=\u672A\u63D0\u4F9B\u8BC1\u4E66
DaoX509AuthoritiesPopulator.noMatching=\u672A\u5728subjectDN\: {0}\u4E2D\u627E\u5230\u5339\u914D\u7684\u6A21\u5F0F
RememberMeAuthenticationProvider.incorrectKey=\u5C55\u793ARememberMeAuthenticationToken\u4E0D\u542B\u6709\u9884\u671F\u7684key
RunAsImplAuthenticationProvider.incorrectKey=\u5C55\u793A\u7684RunAsUserToken\u4E0D\u542B\u6709\u9884\u671F\u7684key
DigestProcessingFilter.missingMandatory=\u9057\u6F0F\u4E86\u5FC5\u987B\u7ED9\u5B9A\u7684\u6458\u8981\u53D6\u503C; \u63A5\u6536\u5230\u7684\u5934\u4FE1\u606F\u4E3A{0}
DigestProcessingFilter.missingAuth=\u9057\u6F0F\u4E86\u9488\u5BF9'auth' QOP\u7684\u3001\u5FC5\u987B\u7ED9\u5B9A\u7684\u6458\u8981\u53D6\u503C; \u63A5\u6536\u5230\u7684\u5934\u4FE1\u606F\u4E3A{0}
DigestProcessingFilter.incorrectRealm=\u54CD\u5E94\u7ED3\u679C\u4E2D\u7684Realm\u540D\u5B57({0})\u540C\u7CFB\u7EDF\u6307\u5B9A\u7684Realm\u540D\u5B57({1})\u4E0D\u543B\u5408
DigestProcessingFilter.nonceExpired=Nonce\u5DF2\u7ECF\u8FC7\u671F/\u8D85\u65F6
DigestProcessingFilter.nonceEncoding=Nonce\u672A\u7ECF\u8FC7Base64\u7F16\u7801; \u76F8\u5E94\u7684nonce\u53D6\u503C\u4E3A {0}
DigestProcessingFilter.nonceNotTwoTokens=Nonce\u5E94\u8BE5\u7531\u4E24\u90E8\u5206\u53D6\u503C\u6784\u6210\uFF0C\u4F46\u7ED3\u679C\u5374\u662F{0}
DigestProcessingFilter.nonceNotNumeric=Nonce\u4EE4\u724C\u7684\u7B2C1\u90E8\u5206\u5E94\u8BE5\u662F\u6570\u5B57\uFF0C\u4F46\u7ED3\u679C\u5374\u662F{0}
DigestProcessingFilter.nonceCompromised=Nonce\u4EE4\u724C\u5DF2\u7ECF\u5B58\u5728\u95EE\u9898\u4E86\uFF0C{0}
DigestProcessingFilter.usernameNotFound=\u7528\u6237\u540D{0}\u672A\u627E\u5230
DigestProcessingFilter.incorrectResponse=\u9519\u8BEF\u7684\u54CD\u5E94\u7ED3\u679C
JdbcDaoImpl.notFound=\u672A\u627E\u5230\u7528\u6237{0}
JdbcDaoImpl.noAuthority=\u6CA1\u6709\u4E3A\u7528\u6237{0}\u6307\u5B9A\u89D2\u8272
SwitchUserProcessingFilter.noCurrentUser=\u4E0D\u5B58\u5728\u5F53\u524D\u7528\u6237
SwitchUserProcessingFilter.noOriginalAuthentication=\u4E0D\u80FD\u591F\u67E5\u627E\u5230\u539F\u5148\u7684\u5DF2\u8BA4\u8BC1\u5BF9\u8C61
SwitchUserProcessingFilter.usernameNotFound=\u7528\u6237\u540D{0}\u672A\u627E\u5230
SwitchUserProcessingFilter.locked=\u7528\u6237\u5E10\u53F7\u5DF2\u88AB\u9501\u5B9A
SwitchUserProcessingFilter.disabled=\u7528\u6237\u5DF2\u5931\u6548
SwitchUserProcessingFilter.expired=\u7528\u6237\u5E10\u53F7\u5DF2\u8FC7\u671F
SwitchUserProcessingFilter.credentialsExpired=\u7528\u6237\u51ED\u8BC1\u5DF2\u8FC7\u671F
AbstractAccessDecisionManager.accessDenied=\u4E0D\u5141\u8BB8\u8BBF\u95EE
LdapAuthenticationProvider.emptyUsername=\u7528\u6237\u540D\u4E0D\u5141\u8BB8\u4E3A\u7A7A
LdapAuthenticationProvider.emptyPassword=\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A
LdapAuthenticationProvider.passwordLength=\u5BC6\u7801\u5FC5\u987B\u7528SHA256\u52A0\u5BC6\u63D0\u4EA4
RSAKeyError=\u5BC6\u94A5\u9519\u8BEF
DefaultIntitalDirContextFactory.communicationFailure=\u4E0D\u80FD\u591F\u8FDE\u63A5\u5230LDAP\u670D\u52A1\u5668
DefaultIntitalDirContextFactory.badCredentials=\u574F\u7684\u51ED\u8BC1
DefaultIntitalDirContextFactory.unexpectedException=\u7531\u4E8E\u672A\u9884\u671F\u5F02\u5E38\u800C\u4E0D\u80FD\u591F\u83B7\u5F97InitialDirContext
PasswordComparisonAuthenticator.badCredentials=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
BindAuthenticator.badCredentials=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
BindAuthenticator.failedToLoadAttributes=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
UserDetailsService.locked=\u7528\u6237\u5E10\u53F7\u5DF2\u88AB\u9501\u5B9A
UserDetailsService.disabled=\u7528\u6237\u5DF2\u5931\u6548
UserDetailsService.expired=\u7528\u6237\u5E10\u53F7\u5DF2\u8FC7\u671F
UserDetailsService.credentialsExpired=\u7528\u6237\u51ED\u8BC1\u5DF2\u8FC7\u671F
emptyCaptchaKey=\u9A8C\u8BC1\u7801\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
emptyCaptchaValue=\u9A8C\u8BC1\u7801\u4E0D\u80FD\u4E3A\u7A7A
captchaError=\u9A8C\u8BC1\u7801\u9519\u8BEF
emptyToken=\u7F3A\u5C11token\u53C2\u6570
tokenExpired=\u4EE4\u724C\u8FC7\u671F
tokenError=\u4EE4\u724C\u9519\u8BEF
SecureLoginError=\u5B89\u5168\u767B\u9646\u9519\u8BEF

