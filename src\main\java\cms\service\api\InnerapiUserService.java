package cms.service.api;

import cms.bean.api.InnerapiUser;
import cms.service.besa.DaoSupport;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.persistence.Query;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/23 18:11
 */
@Service
@Slf4j
public class InnerapiUserService extends DaoSupport<InnerapiUser> {

    @Cacheable(value = "api_innerapi_cache", key = "'innerapi_'+#clientId", unless = "#result == null ")
    public Optional<InnerapiUser> getByIdFromCache(String clientId) {
        Query query = em.createQuery("select o from InnerapiUser o where o.deleted = false and o.clientId=?1")
                .setParameter(1, clientId);
        return Optional.ofNullable(query.getResultList()).filter(CollectionUtil::isNotEmpty).map(l -> (InnerapiUser) l.get(0));
    }
}
