package cms.bean;

import cms.constant.enums.InnerApiErrorEnum;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/23 17:47
 */
@Data
public class ResponseData<T> {

    public final static int success = 200;

    private int code = success;
    private String msg;
    private T data;

    public static <T> ResponseData<T> ok() {
        return new ResponseData<T>();
    }

    public static <T> ResponseData<T> sucess(T data) {
        ResponseData<T> responseData = new ResponseData<T>();
        responseData.setData(data);
        return responseData;
    }

    public static ResponseData fail(InnerApiErrorEnum errorEnum) {
        return fail(errorEnum.getCode(), errorEnum.getMsg());
    }

    public static <T> ResponseData fail(int failCode, String msg) {
        ResponseData responseData = new ResponseData();
        responseData.setCode(failCode);
        responseData.setMsg(msg);
        return responseData;
    }

    public boolean isSuccess() {
        return success == code;
    }
}
