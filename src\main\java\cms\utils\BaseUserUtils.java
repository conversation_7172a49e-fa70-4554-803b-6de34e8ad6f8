package cms.utils;

import cms.bean.BaseUserDel;
import cms.bean.staff.SysUsers;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Date;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/9 11:32
 */
public class BaseUserUtils {

    public static SysUsers getUser() {
        return Optional.ofNullable(SecurityContextHolder.getContext()).map(SecurityContext::getAuthentication).filter(o -> o instanceof SysUsers).map(o -> (SysUsers) o).orElseGet(() -> {
            SysUsers sysUsers = new SysUsers();
            sysUsers.setUserId("d0b3d9bf27d0460f9cc650d9b6e2e170");
            sysUsers.setFullName("admin");
            return sysUsers;
        });
    }

    public static void addUser(Object obj, Long id) {
        if (!(obj instanceof BaseUserDel)) {
            return;
        }
        BaseUserDel o = (BaseUserDel) obj;
        SysUsers sysUsers = getUser();
        o.setId(id);
        o.setCreateUser(sysUsers.getUserId());
        o.setCreateUserName(sysUsers.getFullName());
        o.setCreateTime(new Date());
        o.setUpdateUser(sysUsers.getUserId());
        o.setUpdateUserName(sysUsers.getFullName());
        o.setUpdateTime(new Date());
        o.setDeleted(Optional.ofNullable(o.getDeleted()).orElse(Boolean.FALSE));
    }

    public static void updateUser(Object obj) {
        if (!(obj instanceof BaseUserDel)) {
            return;
        }
        BaseUserDel o = (BaseUserDel) obj;
        SysUsers sysUsers = getUser();
        o.setUpdateUser(sysUsers.getUserId());
        o.setUpdateUserName(sysUsers.getFullName());
        o.setUpdateTime(new Date());
    }
}
