package cms.bean.api;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/23 18:00
 */
@Entity
@Table(name = "innerapi_user")
@Data
public class InnerapiUser implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(length = 30, unique = true)
    private String clientId;
    @Column(length = 64)
    private String clientSecret;
    @Column(length = 30)
    private String contact;
    @Column(length = 100)
    private String memo;
    @Column(length = 2000)
    private String allowUrls;
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;
    @Column(length = 4)
    private Boolean deleted;
    @Temporal(TemporalType.TIMESTAMP)
    private Date deleteTime;
    @Column(length = 100)
    private String deleteReason;
}
