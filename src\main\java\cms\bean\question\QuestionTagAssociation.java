package cms.bean.question;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;


/**
 * 问题标签关联
 */
@Entity
@Table(indexes = {@Index(name = "questionTagAssociation_1_idx", columnList = "questionId"), @Index(name = "questionTagAssociation_2_idx", columnList = "questionTagId")})
@Data
public class QuestionTagAssociation implements Serializable {
    private static final long serialVersionUID = 7948414425873700267L;

    /**
     * Id
     **/
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 问题标签Id
     **/
    private Long questionTagId;
    /**
     * 问题标签名称
     **/
    @Transient
    private String questionTagName;

    @Transient
    private Integer grade;
    /**
     * 问题Id
     **/
    private Long questionId;
    /**
     * 用户名称
     **/
    @Column(length = 30)
    private String userName;

}

