package cms.web.action.api;

import cms.bean.RequestResult;
import cms.service.api.ConfigApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/23 12:10
 */
@Controller
@RequestMapping("api/config")
@Validated
@Slf4j
public class ConfigApiController {

    @Autowired
    private ConfigApiService configApiService;

    @ResponseBody
    @RequestMapping(value = "/test", method = RequestMethod.GET)
    public String test() {
        return "123123";
    }

    @ResponseBody
    @RequestMapping(name = "测试", value = "/getTopicAndSys/{operationId}", method = RequestMethod.GET)
    public RequestResult getTopicAndSys(@PathVariable long operationId) {
        return RequestResult.ok(configApiService.getTopicAndSys(operationId));
    }
}
