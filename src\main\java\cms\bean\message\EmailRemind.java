package cms.bean.message;

import cms.bean.user.User;
import lombok.Data;

import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/24 10:31
 */
//@Entity
//@Table(name = "emailRemind")
@Data
public class EmailRemind {

    private static final long serialVersionUID = -3L;

    private String businessId;

    private Long businessUserId;

    private String businessUserName;

    private Integer remindType;

    private Optional<User> receiverUser;

    private Boolean send = false;

    public EmailRemind(Long businessId, Long businessUserId, String businessUserName, Integer remindType, Optional<User> receiverUser) {
        this.businessId = String.valueOf(businessId);
        this.businessUserId = businessUserId;
        this.businessUserName = businessUserName;
        this.remindType = remindType;
        this.receiverUser = receiverUser;
    }

    public EmailRemind(String businessId, Long businessUserId, String businessUserName, Integer remindType, Optional<User> receiverUser) {
        this.businessId = businessId;
        this.businessUserId = businessUserId;
        this.businessUserName = businessUserName;
        this.remindType = remindType;
        this.receiverUser = receiverUser;
    }
}
