package cms.listeners;

import cms.bean.user.User;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import javax.persistence.PrePersist;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/3 17:16
 */
public class UserNullInitListeners implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PrePersist
    public void prePersist(Object obj) {
        if (!(obj instanceof User)) {
            return;
        }
    }
}
