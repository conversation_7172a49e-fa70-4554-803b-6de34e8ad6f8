package cms.bean.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/2/28 16:54
 */
@Data
public class UserExcl implements Serializable {
    private static final long serialVersionUID = 1L;

    @ColumnWidth(35)
    @ExcelProperty(value = "账号", order = 1)
    private String account;

    @ColumnWidth(17)
    @ExcelProperty(value = "昵称", order = 5)
    private String nickname;

    @ColumnWidth(40)
    @ExcelProperty(value = "简介", order = 5)
    private String intro;

    @ExcelProperty(value = "积分", order = 5)
    private Long point = 0L;

    @ExcelProperty(value = "会员等级", order = 5)
    private String gradeName;

    @ColumnWidth(20)
    @ExcelProperty(value = "注册日期", order = 5)
    private Date registrationDate;

    @ExcelIgnore
    private Integer state;
    @ExcelProperty(value = "状态", order = 5)
    private String stateName;

    public String getStateName() {
        switch (state) {
            case 1:
                stateName = "启用";
                break;
            case 2:
                stateName = "停用";
                break;
            case 11:
                stateName = "启用时删除";
                break;
            case 12:
                stateName = "停用时删除";
                break;
        }
        return stateName;
    }
}
