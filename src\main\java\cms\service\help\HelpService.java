package cms.service.help;

import cms.bean.help.Help;
import cms.service.besa.DAO;

import java.util.List;

/**
 * 帮助管理
 */
public interface HelpService extends DAO<Help> {
    /**
     * 根据Id查询帮助
     *
     * @param helpId 帮助Id
     * @return
     */
    Help findById(Long helpId);

    /**
     * 根据Id集合查询帮助
     *
     * @param helpIdList 帮助Id集合
     * @return
     */
    List<Help> findByIdList(List<Long> helpIdList);

    /**
     * 根据分类Id查询帮助
     *
     * @param helpTypeId 帮助分类Id
     * @return
     */
    List<Help> findByTypeId(Long helpTypeId);

    /**
     * 保存帮助
     *
     * @param help
     */
    Integer saveHelp(Help help);

    /**
     * 修改帮助
     *
     * @param help
     * @return
     */
    Integer updateHelp(Help help);

    /**
     * 修改帮助
     *
     * @param helpList       帮助集合
     * @param new_helpTypeId 新帮助分类Id
     * @return
     */
    Integer updateHelp(List<Help> helpList, Long new_helpTypeId);


    /**
     * 还原帮助
     *
     * @param helpList 帮助集合
     * @return
     */
    Integer reductionHelp(List<Help> helpList);

    /**
     * 标记删除帮助
     *
     * @param helpTypeId 帮助分类Id
     * @param helpId     帮助Id
     * @return
     */
    Integer markDelete(Long helpTypeId, Long helpId);


    /**
     * 删除帮助
     *
     * @param helpTypeId   帮助分类Id
     * @param helpId       帮助Id
     * @param helpQuantity 减少帮助数量(显示)
     * @return
     */
    Integer deleteHelp(Long helpTypeId, Long helpId, Long helpQuantity);

    /**
     * 修改帮助分类Id
     *
     * @param old_HelpTypeId 旧帮助分类Id
     * @param new_HelpTypeId 新帮助分类Id
     * @return
     */
    Integer updateHelpTypeId(Long old_HelpTypeId, Long new_HelpTypeId);
}
