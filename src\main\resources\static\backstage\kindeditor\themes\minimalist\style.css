@font-face {
  font-family: 'remixicon';
  src:  url('fonts/remixicon.eot?frjzxf');
  src:  url('fonts/remixicon.eot?frjzxf#iefix') format('embedded-opentype'),
    url('fonts/remixicon.ttf?frjzxf') format('truetype'),
    url('fonts/remixicon.woff?frjzxf') format('woff'),
    url('fonts/remixicon.svg?frjzxf#remixicon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

.fa {
    display: inline-block;
    font-size:18px;
}

[class^="fa-"], [class*=" fa-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'remixicon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.fa-code-edit:before {
  content: "\e941";
}
.fa-angle-down:before {
  content: "\e900";
}
.fa-baidumap:before {
  content: "\e901";
}
.fa-bold:before {
  content: "\e902";
}
.fa-caret-down:before {
  content: "\e903";
}
.fa-checked:before {
  content: "\e904";
}
.fa-clearhtml:before {
  content: "\e905";
}
.fa-close:before {
  content: "\e906";
}
.fa-code:before {
  content: "\e907";
}
.fa-emoticons:before {
  content: "\e908";
}
.fa-fontname:before {
  content: "\e909";
}
.fa-fontsize:before {
  content: "\e90a";
}
.fa-forecolor:before {
  content: "\e90b";
}
.fa-formatblock:before {
  content: "\e90c";
}
.fa-fullscreen:before {
  content: "\e90d";
}
.fa-hide:before {
  content: "\e90e";
}
.fa-hide-edit:before {
  content: "\e90f";
}
.fa-hilitecolor:before {
  content: "\e910";
}
.fa-hr:before {
  content: "\e911";
}
.fa-image:before {
  content: "\e912";
}
.fa-indent:before {
  content: "\e913";
}
.fa-insertfile:before {
  content: "\e914";
}
.fa-insertorderedlist:before {
  content: "\e915";
}
.fa-insertunorderedlist:before {
  content: "\e916";
}
.fa-italic:before {
  content: "\e917";
}
.fa-justifycenter:before {
  content: "\e918";
}
.fa-justifyfull:before {
  content: "\e919";
}
.fa-justifyleft:before {
  content: "\e91a";
}
.fa-justifyright:before {
  content: "\e91b";
}
.fa-lineheight:before {
  content: "\e91c";
}
.fa-link:before {
  content: "\e91d";
}
.fa-media:before {
  content: "\e91e";
}
.fa-multiimage:before {
  content: "\e91f";
}
.fa-outdent:before {
  content: "\e920";
}
.fa-pagebreak:before {
  content: "\e921";
}
.fa-preview:before {
  content: "\e922";
}
.fa-question-circle:before {
  content: "\e923";
}
.fa-quickformat:before {
  content: "\e924";
}
.fa-quote-left:before {
  content: "\e925";
}
.fa-quote-right:before {
  content: "\e926";
}
.fa-removeformat:before {
  content: "\e927";
}
.fa-selectall:before {
  content: "\e928";
}
.fa-source:before {
  content: "\e929";
}
.fa-strikethrough:before {
  content: "\e92a";
}
.fa-subscript:before {
  content: "\e92b";
}
.fa-superscript:before {
  content: "\e92c";
}
.fa-table:before {
  content: "\e92d";
}
.fa-tablecellprop:before {
  content: "\e92e";
}
.fa-tablecoldelete:before {
  content: "\e92f";
}
.fa-tablecolinsertleft:before {
  content: "\e930";
}
.fa-tablecolinsertright:before {
  content: "\e931";
}
.fa-tablecolmerge:before {
  content: "\e932";
}
.fa-tablecolsplit:before {
  content: "\e933";
}
.fa-tabledelete:before {
  content: "\e934";
}
.fa-tableinsert:before {
  content: "\e935";
}
.fa-tableprop:before {
  content: "\e936";
}
.fa-tablerowdelete:before {
  content: "\e937";
}
.fa-tablerowinsertabove:before {
  content: "\e938";
}
.fa-tablerowinsertbelow:before {
  content: "\e939";
}
.fa-tablerowmerge:before {
  content: "\e93a";
}
.fa-tablerowsplit:before {
  content: "\e93b";
}
.fa-template:before {
  content: "\e93c";
}
.fa-underline:before {
  content: "\e93d";
}
.fa-unlink:before {
  content: "\e93e";
}
.fa-rightBottomCorner:before {
  content: "\e93f";
}
.fa-sort:before {
  content: "\e940";
}
.fa-caret-right:before {
  content: "\e791";
}
