package cms.service.template.impl;

import cms.bean.template.RollImg;
import cms.service.besa.DaoSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/23 9:54
 */
@Service
@Slf4j
public class RollImgService extends DaoSupport<RollImg> {

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    public List<RollImg> findAllList() {
        Query query = em.createQuery("select o from RollImg o");
        return query.getResultList();
    }

    public Optional<RollImg> getById(Long id) {
        Query query = em.createQuery("select o from RollImg o where o.id = ?1");
        query.setParameter(1, id);
        return Optional.ofNullable((List<RollImg>) query.getResultList()).map(o -> o.get(0));
    }

    public void add(RollImg rollImg) {
        this.save(rollImg);
    }

    public void edit(RollImg rollImg) {
        this.update(rollImg);
    }

    public void delById(Long id) {
        Query delete = em.createQuery("delete from RollImg o where o.id=?1")
                .setParameter(1, id);
        delete.executeUpdate();
    }
}
