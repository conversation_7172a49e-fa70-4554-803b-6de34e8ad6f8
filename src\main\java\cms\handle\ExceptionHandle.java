package cms.handle;

import cms.bean.RequestResult;
import cms.bean.ResultCode;
import cms.service.messageSource.ErrorMessageService;
import cms.utils.JsonUtils;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.internal.engine.path.PathImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static cms.constant.ErrorCode.DEFAULT_CODE;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/4/19 10:20
 */
@RestControllerAdvice()
@Slf4j
@Order(1)
@Component
public class ExceptionHandle {

    @Resource
    private MessageSource messageSource;
    @Autowired
    private ErrorMessageService errorMessageService;

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    @ResponseBody
    public String validate(BindException e) {
        Map<String, String> error = Optional.ofNullable(e.getFieldErrors()).map(l -> l.stream().collect(Collectors.toMap(FieldError::getField, this::getMessage))).orElse(Maps.newHashMap());
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({CustomException.class})
    @ResponseBody
    public String validate(CustomException e) {
        return validate(e, Optional.of(ResultCode.FAILURE.getCode()));
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({ConstraintViolationException.class})
    @ResponseBody
    public String validate(ConstraintViolationException e) {
        Map<String, String> error = e.getConstraintViolations().stream().collect(Collectors.toMap(o -> getLastName(((ConstraintViolation) o).getPropertyPath().toString()), this::getMessage));
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @ExceptionHandler({Exception.class})
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String handleException(Exception ex) {
        log.error("not captured exception!", ex);
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, ex.getMessage()));
    }

    public String validate(CustomException e, Optional<Integer> errorCode) {
        String msg = errorMessageService.customExceptionMsg(e);
        Map<String, String> data = Optional.ofNullable(e.getFiledName()).map(String::trim).filter(StringUtils::isNotEmpty).map(o -> {
            Map<String, String> map = Maps.newHashMap();
            map.put(e.getFiledName(), msg);
            return map;
        }).orElse(null);
        int code = errorCode.map(o -> Optional.ofNullable(e.getResultCode()).filter(v -> v).map(v -> e.getCode()).orElse(o)).orElseGet(() -> Optional.ofNullable(e.getCode()).filter(o -> 0 != o).orElseGet(() -> ResultCode.FAILURE.getCode()));
        return JsonUtils.toJSONString(new RequestResult(code, msg, data));
    }

    private String getMessage(FieldError o) {
        try {
            String name = errorMessageService.getMessage(String.format("%s.%s", o.getObjectName(), o.getField()), o.getArguments());
            String msg = errorMessageService.getMessage(String.format("validateMsg.%s", o.getCode()), o.getArguments());
            return String.format("%s %s", name, msg);
        } catch (Exception e) {
            log.error("getMessage FieldError error!", e);
            return errorMessageService.getMessage(DEFAULT_CODE);
        }
    }

    private String getMessage(ConstraintViolation o) {
        try {
            String colName = Optional.ofNullable(o.getRootBeanClass().getSimpleName()).filter(o.getLeafBean().getClass().getSimpleName()::equals).map(x -> String.format("%s.%s", StrUtil.lowerFirst(x), o.getPropertyPath().toString()))
                    .orElseGet(() -> String.format("%s.%s", StrUtil.lowerFirst(o.getLeafBean().getClass().getSimpleName()), ((PathImpl) o.getPropertyPath()).getLeafNode().toString()));
            String name = errorMessageService.getMessage(colName);
            String msg = errorMessageService.getMessage(String.format("validateMsg.%s", o.getConstraintDescriptor().getAnnotation().annotationType().getSimpleName()));
            return String.format("%s %s", name, msg);
        } catch (Exception e) {
            log.error("getMessage ConstraintViolation error!", e);
            return errorMessageService.getMessage(DEFAULT_CODE);
        }
    }

    private String getLastName(String s) {
        return s.substring(s.lastIndexOf(".") + 1, s.length());
    }
}
