package cms.web.action.keywordDict;

import cms.bean.RequestResult;
import cms.bean.ResultCode;
import cms.bean.keywordDict.KeywordDict;
import cms.constant.ErrorCode;
import cms.handle.CustomException;
import cms.service.keywordDict.KeywordDictService;
import cms.service.keywordDict.TextFilterKeywordService;
import cms.utils.JsonUtils;
import cms.web.action.filterWord.SensitiveWordFilterManage;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/9 10:39
 */
@Controller
@RequestMapping("/control/keywordDictAction/manage")
@Slf4j
public class KeywordDictManageAction {

    private static final String UPLOAD_SUFFIX = "txt";
    public static Semaphore semaphore = new Semaphore(1);
    public static final int WAIT_SECONDS = 60;

    @Autowired
    private KeywordDictService keywordDictService;
    @Autowired
    private SensitiveWordFilterManage sensitiveWordFilterManage;
    @Autowired
    private TextFilterKeywordService textFilterKeywordService;

    /**
     * 上传词库
     */
    @ResponseBody
    @RequestMapping(params = "method=upload", method = RequestMethod.POST)
    public String uploadFilterWord(ModelMap model, MultipartFile file) throws Exception {
        Optional.ofNullable(file).map(MultipartFile::getOriginalFilename).map(o -> o.split("\\.")).filter(o -> o.length > 1).map(o -> o[o.length - 1]).filter(o -> UPLOAD_SUFFIX.equals(o.toLowerCase())).orElseThrow(() -> new CustomException(ErrorCode.C_1_0006_0001, "file"));
        InputStreamReader isr = new InputStreamReader(file.getInputStream());
        BufferedReader reader = new BufferedReader(isr);
        List<String> keyWords = Optional.ofNullable(reader.lines().map(String::trim).filter(Strings::isNotEmpty).distinct().collect(Collectors.toList())).filter(CollectionUtil::isNotEmpty).orElseThrow(() -> new CustomException(ErrorCode.C_1_0006_0002, "file"));
        if (!semaphore.tryAcquire(WAIT_SECONDS, TimeUnit.SECONDS)) {
            throw new CustomException(ErrorCode.C_1_0001_0012, "file");
        }
        try {
            keywordDictService.findByKeyWords(keyWords).map(l -> l.stream().map(KeywordDict::getKeyWord).collect(Collectors.toList())).ifPresent(keyWords::removeAll);
            Optional.ofNullable(keyWords.stream().map(o -> {
                KeywordDict keywordDict = new KeywordDict();
                keywordDict.setKeyWord(o);
                return keywordDict;
            }).collect(Collectors.toList())).filter(CollectionUtil::isNotEmpty).ifPresent(keywordDictService::add);
            sensitiveWordFilterManage.addWords(keyWords);

            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        } finally {
            semaphore.release();
        }
    }

    @ResponseBody
    @RequestMapping(params = "method=export", method = RequestMethod.POST)
    public void exportFilterWord(ModelMap model, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Set<String> keywords = keywordDictService.findAllKeyword_cache().stream().map(KeywordDict::getKeyWord).collect(Collectors.toSet());

        //创建文件名
        String fileName = "keyWord";
        //设置返回信息数据
        response.setContentType("text/plain;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-disposition", "attachment;filename=" + fileName + ".txt");
        //创建输出流
        BufferedOutputStream buff = null;
        ServletOutputStream outSTr = null;
        String enter = "\r\n";
        //创建拼接字段
        StringBuffer write = new StringBuffer("");
        try {
            outSTr = response.getOutputStream();
            buff = new BufferedOutputStream(outSTr);
            keywords.stream().forEach(o -> write.append(o).append(enter));
            buff.write(write.toString().getBytes("UTF-8"));
            //关闭输出流
            buff.flush();
            buff.close();
        } catch (Exception e) {
            log.error("down key word error!", e);
        } finally {
            try {
                buff.close();
                outSTr.close();
            } catch (Exception e) {
                log.error("down key word error!", e);
            }
        }
    }

    @ResponseBody
    @RequestMapping(params = "method=delete", method = RequestMethod.POST)
    public String deleteFilterWord(ModelMap model, Long id) throws Exception {
        if (!semaphore.tryAcquire(WAIT_SECONDS, TimeUnit.SECONDS)) {
            throw new CustomException(ErrorCode.C_1_0001_0012, "id");
        }
        try {
            keywordDictService.findById(id).map(KeywordDict::getKeyWord).ifPresent(sensitiveWordFilterManage::delWord);
            keywordDictService.delById(Optional.ofNullable(id).filter(Objects::nonNull).orElseThrow(() -> new CustomException(ErrorCode.C_1_0006_0003, "file")));
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        } finally {
            semaphore.release();
        }
    }

    @ResponseBody
    @RequestMapping(params = "method=initFilterWord", method = RequestMethod.POST)
    public String initFilterWord(ModelMap model) throws Exception {
        if (!semaphore.tryAcquire(WAIT_SECONDS, TimeUnit.SECONDS)) {
            throw new CustomException(ErrorCode.C_1_0001_0012, "word");
        }
        try {
            Set<String> keywords = keywordDictService.findAllKeyword_cache().stream().map(KeywordDict::getKeyWord).collect(Collectors.toSet());
            sensitiveWordFilterManage.initWord(keywords);
            log.info(String.format("keyword: %s", sensitiveWordFilterManage.getAllKeywords().stream().collect(Collectors.joining(","))));
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        } finally {
            semaphore.release();
        }
    }

    @ResponseBody
    @RequestMapping(params = "method=runFilterKeyWord", method = RequestMethod.POST)
    public String runFilterKeyWord(ModelMap model, Long id) throws Exception {
        textFilterKeywordService.runFilterKeyWord();
        return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
    }
}
