package cms.bean.like;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/18 10:27
 */
@Entity
@Table(name = "commentlike_0", indexes = {@Index(name = "idx_commentId_addtime", columnList = "commentId,addtime")})
@Data
public class CommentLike extends CommentLikeEntity implements Serializable {
    private static final long serialVersionUID = 3112294654438891737L;


}
