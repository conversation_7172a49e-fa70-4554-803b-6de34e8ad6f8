package cms.service.user.impl;

import cms.bean.user.DisableUserName;
import cms.bean.user.User;
import cms.bean.user.UserCustom;
import cms.bean.user.UserInputValue;
import cms.constant.ErrorCode;
import cms.constraints.validateGroups.LocalUserRegister;
import cms.constraints.validateGroups.PhoneUserRegister;
import cms.handle.CustomException;
import cms.service.user.UserService;
import cms.utils.JsonUtils;
import cms.utils.SHA;
import cms.utils.UUIDUtil;
import cms.utils.Verification;
import cms.web.action.sms.SmsManage;
import cms.web.action.user.UserManage;
import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.validation.annotation.Validated;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/18 18:02
 */
@Service
@Validated()
public class RegisterUserService {

    @Autowired
    private UserService userService;
    @Autowired
    private UserManage userManage;
    @Autowired
    private SmsManage smsManage;

    private PathMatcher matcher = new AntPathMatcher();

    @Validated(LocalUserRegister.class)
    public void localUserRegister(@Valid User formbean, User user) {
        Optional.ofNullable(userService.findAllDisableUserName_cache()).filter(CollectionUtil::isNotEmpty)
                .map(l -> l.stream().map(DisableUserName::getName).map(o -> matcher.match(o, formbean.getAccount())).anyMatch(o -> o))
                .filter(o -> !o).orElseThrow(() -> new CustomException(ErrorCode.C_2_0004_0019, "account"));

        user.setAccount(formbean.getAccount().trim());
        Optional.ofNullable(userService.findUserByAccount(user.getAccount()) == null).filter(o -> o).orElseThrow(() -> new CustomException(ErrorCode.C_1_0004_0001, "account"));

        user.setIssue(formbean.getIssue().trim());
        user.setAnswer(SHA.sha256Hex(formbean.getAnswer().trim()));
        Optional.of(user.getAnswer()).filter(o -> o.length() == 64).orElseThrow(() -> new CustomException(ErrorCode.C_2_0004_0016, "answer"));

        user.setEmail(formbean.getEmail().trim());
        user.setUserName(UUIDUtil.getUUID22());
        user.setPlatformUserId(user.getUserName());
    }

    @Validated(PhoneUserRegister.class)
    public void phoneUserRegister(@Valid User formbean, User user, String smsCode) {
        String platformUserId = userManage.thirdPartyUserIdToPlatformUserId(formbean.getMobile().trim(), 20);
        Optional.ofNullable(null == userService.findUserByPlatformUserId(platformUserId)).filter(o -> o).orElseThrow(() -> new CustomException(ErrorCode.C_1_0004_0002, "mobile"));

        //实名认证绑定手机
        user.setMobile(formbean.getMobile().trim());
        //是否实名认证
        user.setRealNameAuthentication(true);
        String id = UUIDUtil.getUUID22();
        user.setUserName(id);//会员用户名
        user.setAccount(userManage.queryUserIdentifier(20) + "-" + id);//用户名和账号可以用不相同的UUID
        user.setPlatformUserId(userManage.thirdPartyUserIdToPlatformUserId(formbean.getMobile().trim(), 20));

        smsCode = Optional.ofNullable(smsCode).map(String::trim).filter(Strings::isNotEmpty).orElseThrow(() -> new CustomException(ErrorCode.C_2_0004_0011, "smsCode"));
        Optional.of(smsCode).filter(o -> o.length() <= 6).orElseThrow(() -> new CustomException(ErrorCode.C_2_0004_0012, "smsCode"));

        //生成绑定手机验证码标记
        String numeric = Optional.ofNullable(smsManage.smsCode_generate(100, user.getPlatformUserId(), formbean.getMobile().trim(), null)).orElseThrow(() -> new CustomException(ErrorCode.C_2_0004_0013, "smsCode"));
        Optional.of(numeric.equals(smsCode)).filter(o -> o).orElseThrow(() -> new CustomException(ErrorCode.C_2_0004_0014, "smsCode"));
        //删除手机验证码标记
        smsManage.smsCode_delete(100, user.getPlatformUserId(), formbean.getMobile().trim());
    }

    public void userCustom(List<UserCustom> userCustomList, HttpServletRequest request) {
        if (CollectionUtil.isNotEmpty(userCustomList)) {
            return;
        }
        for (UserCustom userCustom : userCustomList) {
            //用户自定义注册功能项用户输入值集合
            List<UserInputValue> userInputValueList = new ArrayList<UserInputValue>();

            if (userCustom.isVisible() == true) {//显示
                if (userCustom.getValue() != null && !"".equals(userCustom.getValue().trim())) {
                    LinkedHashMap<String, String> itemValue = JsonUtils.toGenericObject(userCustom.getValue(), new TypeReference<LinkedHashMap<String, String>>() {
                    });
                    userCustom.setItemValue(itemValue);
                }
                if (userCustom.getChooseType().equals(1)) {//1.输入框
                    String userCustom_value = request.getParameter("userCustom_" + userCustom.getId());

                    if (userCustom_value != null && !"".equals(userCustom_value.trim())) {
                        UserInputValue userInputValue = new UserInputValue();
                        userInputValue.setUserCustomId(userCustom.getId());
                        userInputValue.setContent(userCustom_value.trim());
                        userInputValueList.add(userInputValue);


                        if (userCustom.getMaxlength() != null && userCustom_value.length() > userCustom.getMaxlength()) {
//                            error.put("userCustom_" + userCustom.getId(), "长度超过" + userCustom_value.length() + "个字符");
                        }

                        int fieldFilter = userCustom.getFieldFilter();//字段过滤方式    0.无  1.只允许输入数字  2.只允许输入字母  3.只允许输入数字和字母  4.只允许输入汉字  5.正则表达式过滤
                        switch (fieldFilter) {
                            case 1: //输入数字
                                if (Verification.isPositiveIntegerZero(userCustom_value.trim()) == false) {
//                                    error.put("userCustom_" + userCustom.getId(), ErrorView._804.name());//只允许输入数字
                                }
                                break;
                            case 2: //输入字母
                                if (Verification.isLetter(userCustom_value.trim()) == false) {
//                                    error.put("userCustom_" + userCustom.getId(), ErrorView._805.name());//只允许输入字母
                                }
                                break;
                            case 3: //只能输入数字和字母
                                if (Verification.isNumericLetters(userCustom_value.trim()) == false) {
//                                    error.put("userCustom_" + userCustom.getId(), ErrorView._806.name());//只允许输入数字和字母
                                }
                                break;
                            case 4: //只能输入汉字
                                if (Verification.isChineseCharacter(userCustom_value.trim()) == false) {
//                                    error.put("userCustom_" + userCustom.getId(), ErrorView._807.name());//只允许输入汉字
                                }
                                break;
                            case 5: //正则表达式过滤
                                if (userCustom_value.matches(userCustom.getRegular()) == false) {
//                                    error.put("userCustom_" + userCustom.getId(), ErrorView._808.name());//输入错误
                                }
                                break;
                            //	default:
                        }
                    } else {
                        if (userCustom.isRequired() == true) {//是否必填
//                            error.put("userCustom_" + userCustom.getId(), ErrorView._809.name());//必填项
                        }

                    }
                    userCustom.setUserInputValueList(userInputValueList);
                } else if (userCustom.getChooseType().equals(2)) {//2.单选按钮
                    String userCustom_value = request.getParameter("userCustom_" + userCustom.getId());

                    if (userCustom_value != null && !"".equals(userCustom_value.trim())) {

                        String itemValue = userCustom.getItemValue().get(userCustom_value.trim());
                        if (itemValue != null) {
                            UserInputValue userInputValue = new UserInputValue();
                            userInputValue.setUserCustomId(userCustom.getId());
                            userInputValue.setOptions(userCustom_value.trim());
                            userInputValueList.add(userInputValue);

                        } else {
                            if (userCustom.isRequired() == true) {//是否必填
//                                error.put("userCustom_" + userCustom.getId(), ErrorView._809.name());//必填项
                            }
                        }

                    } else {
                        if (userCustom.isRequired() == true) {//是否必填
//                            error.put("userCustom_" + userCustom.getId(), ErrorView._809.name());//必填项
                        }
                    }
                    userCustom.setUserInputValueList(userInputValueList);

                } else if (userCustom.getChooseType().equals(3)) {//3.多选按钮
                    String[] userCustom_value_arr = request.getParameterValues("userCustom_" + userCustom.getId());

                    if (userCustom_value_arr != null && userCustom_value_arr.length > 0) {
                        for (String userCustom_value : userCustom_value_arr) {

                            if (userCustom_value != null && !"".equals(userCustom_value.trim())) {

                                String itemValue = userCustom.getItemValue().get(userCustom_value.trim());
                                if (itemValue != null) {
                                    UserInputValue userInputValue = new UserInputValue();
                                    userInputValue.setUserCustomId(userCustom.getId());
                                    userInputValue.setOptions(userCustom_value.trim());
                                    userInputValueList.add(userInputValue);
                                }


                            }
                        }
                    } else {
                        if (userCustom.isRequired() == true) {//是否必填
//                            error.put("userCustom_" + userCustom.getId(), ErrorView._809.name());//必填项
                        }
                    }
                    if (userInputValueList.size() == 0) {
                        if (userCustom.isRequired() == true) {//是否必填
//                            error.put("userCustom_" + userCustom.getId(), ErrorView._809.name());//必填项
                        }
                    }
                    userCustom.setUserInputValueList(userInputValueList);

                } else if (userCustom.getChooseType().equals(4)) {//4.下拉列表
                    String[] userCustom_value_arr = request.getParameterValues("userCustom_" + userCustom.getId());

                    if (userCustom_value_arr != null && userCustom_value_arr.length > 0) {
                        for (String userCustom_value : userCustom_value_arr) {

                            if (userCustom_value != null && !"".equals(userCustom_value.trim())) {

                                String itemValue = userCustom.getItemValue().get(userCustom_value.trim());
                                if (itemValue != null) {
                                    UserInputValue userInputValue = new UserInputValue();
                                    userInputValue.setUserCustomId(userCustom.getId());
                                    userInputValue.setOptions(userCustom_value.trim());
                                    userInputValueList.add(userInputValue);
                                }


                            }
                        }
                    } else {
                        if (userCustom.isRequired() == true) {//是否必填
//                            error.put("userCustom_" + userCustom.getId(), ErrorView._809.name());//必填项
                        }
                    }
                    if (userInputValueList.size() == 0) {
                        if (userCustom.isRequired() == true) {//是否必填
//                            error.put("userCustom_" + userCustom.getId(), ErrorView._809.name());//必填项
                        }
                    }
                    userCustom.setUserInputValueList(userInputValueList);
                } else if (userCustom.getChooseType().equals(5)) {// 5.文本域
                    String userCustom_value = request.getParameter("userCustom_" + userCustom.getId());

                    if (userCustom_value != null && !"".equals(userCustom_value.trim())) {
                        UserInputValue userInputValue = new UserInputValue();
                        userInputValue.setUserCustomId(userCustom.getId());
                        userInputValue.setContent(userCustom_value);

                        userInputValueList.add(userInputValue);

                    } else {
                        if (userCustom.isRequired() == true) {//是否必填
//                            error.put("userCustom_" + userCustom.getId(), ErrorView._809.name());//必填项
                        }
                    }
                    userCustom.setUserInputValueList(userInputValueList);
                }
            }
        }
    }
}
