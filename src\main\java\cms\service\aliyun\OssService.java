package cms.service.aliyun;

import cms.handle.MsgException;
import cms.service.fileCloud.OssInterface;
import cms.service.messageSource.ErrorMessageService;
import cn.hutool.core.collection.CollectionUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.*;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Optional;

import static cms.constant.ErrorCode.C_2_0001_0500;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/6/26 14:05
 */
@Service
@ConditionalOnProperty(value = "fileCloud.type", havingValue = "oss")
public class OssService implements OssInterface {

    public final static String separator = "/";

    @Value("${fileCloud.aliyun.oss.endpoint}")
    private String endpoint;
    @Value("${fileCloud.aliyun.oss.accessKeyId}")
    private String accessKeyId;
    @Value("${fileCloud.aliyun.oss.accessKeySecret}")
    private String accessKeySecret;
    @Value("${fileCloud.aliyun.oss.bucketName}")
    private String bucketName;
    @Value("${fileCloud.aliyun.oss.enable}")
    private Boolean enable;
    @Value("${fileCloud.aliyun.oss.policy.expire}")
    private Integer expire;
    @Value("${fileCloud.aliyun.oss.maxSize}")
    private Integer maxSize;
    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private ErrorMessageService errorMessageService;

    public List<Bucket> getBuckets() throws Exception {
        // 创建OSSClient实例。
        OSS ossClient = getOSS();
        try {
            return ossClient.listBuckets();
        } finally {
            Optional.ofNullable(ossClient).ifPresent(OSS::shutdown);
        }
    }

//    public String uploadByMp4Code264(InputStream input, String objectName) throws Exception {
//        String suffix = FileUtil.getExtension(objectName).toLowerCase();
//        if (SUFFIX_MP4.equals(suffix)) {
//            return Optional.ofNullable(Mp4Util.mp4Code(input)).map(o -> this.upload(o, objectName)).orElse("");
//        } else {
//            return this.upload(input, objectName);
//        }
//    }

    @Override
    public String upload(InputStream input, String objectName) {
        OSS ossClient = getOSS();
        try {
            String path = getTitlePath() + objectName.replaceAll("\\\\", separator);
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, path, input);
            // 创建PutObject请求。
            PutObjectResult result = ossClient.putObject(putObjectRequest);

            return String.format("%s%s", getUploadStartPath(), path);
        } finally {
            Optional.ofNullable(ossClient).ifPresent(OSS::shutdown);
        }
    }

    @Override
    public List<String> listFile() {
        List<String> result = Lists.newArrayList();
        // 创建OSSClient实例。
        OSS ossClient = getOSS();
        try {
            // 构造ListObjectsRequest请求。
            ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucketName);
            listObjectsRequest.setMaxKeys(1000);
            // 设置prefix参数来获取fun目录下的所有文件。
            listObjectsRequest.setPrefix(getTitlePath());
            // 递归列举fun目录下的所有文件。
            ObjectListing listing = ossClient.listObjects(listObjectsRequest);

            // 遍历所有文件。
            for (OSSObjectSummary objectSummary : listing.getObjectSummaries()) {
                result.add(objectSummary.getKey());
            }

            // 遍历所有commonPrefix。
            for (String commonPrefix : listing.getCommonPrefixes()) {
                result.add(commonPrefix);
            }
            return result;
        } finally {
            Optional.ofNullable(ossClient).ifPresent(OSS::shutdown);
        }
    }

    public void del() {
        if (Optional.ofNullable(active).orElse("1").equals("pro")) {
            return;
        }
        // 创建OSSClient实例。
        OSS ossClient = getOSS();
        try {
            // 删除文件或目录。如果要删除目录，目录必须为空。
            this.listFile().forEach(o -> {
                Optional.ofNullable(o).filter(x -> x.startsWith("bbs" + separator)).ifPresent(x -> ossClient.deleteObject(bucketName, o));
            });
        } finally {
            Optional.ofNullable(ossClient).ifPresent(OSS::shutdown);
        }
    }

    @Override
    public void del(List<String> path) {
        if (CollectionUtil.isEmpty(path)) {
            return;
        }
        OSS ossClient = getOSS();
        try {
            path.forEach(o -> ossClient.deleteObject(bucketName, o));
        } finally {
            Optional.ofNullable(ossClient).ifPresent(OSS::shutdown);
        }
    }

    @Override
    public InputStream getFile(String path) {
        OSS ossClient = getOSS();
        try {
            OSSObject ossObject = ossClient.getObject(bucketName, path);
            return new ByteArrayInputStream(IOUtils.toByteArray(ossObject.getObjectContent()));
        } catch (IOException e) {
            throw new MsgException(errorMessageService.getMessage(C_2_0001_0500));
        } finally {
            Optional.ofNullable(ossClient).ifPresent(OSS::shutdown);
        }
    }

    @Override
    public String getTitlePath() {
        return Optional.ofNullable(active).filter("pro"::equals).map(o -> "").orElseGet(() -> "bbs" + separator);
    }

    public OSS getOSS() {
        CredentialsProvider credentialsProvider = new DefaultCredentialProvider(accessKeyId, accessKeySecret);
        String endpoint = this.getEndpoint();
        OSS ossClient = new OSSClientBuilder().build(endpoint, credentialsProvider);
        return ossClient;
    }

    public String getEndpoint() {
        return "https://" + this.endpoint;
    }

    @Override
    public String getUploadStartPath() {
        return String.format("https://%s.%s/", bucketName, this.endpoint);
    }
}
