package cms.service.fileCloud;

import cms.utils.FileUtil;
import cms.utils.Mp4Util;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Java2DFrameConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Optional;

import static cms.constant.Constant.SUFFIX_MP4;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/9/21 20:25
 */
@Service
@Slf4j
public class FileCloudService {

    @Autowired
    private OssInterface ossInterface;

    public String uploadByMp4Code264(InputStream input, String objectName) throws Exception {
        String suffix = FileUtil.getExtension(objectName).toLowerCase();
        if (SUFFIX_MP4.equals(suffix)) {
            return Optional.ofNullable(Mp4Util.mp4Code(input)).map(o -> ossInterface.upload(o, objectName)).orElse("");
        } else {
            return ossInterface.upload(input, objectName);
        }
    }

    public List<String> listFile() {
        return ossInterface.listFile();
    }

    public void del(List<String> path) {
        ossInterface.del(path);
    }

    public String getUploadStartPath() {
        return ossInterface.getUploadStartPath();
    }

    public String getTitlePath() {
        return ossInterface.getTitlePath();
    }

    public byte[] getMediaImage(String path) throws Exception {
        InputStream is = ossInterface.getFile(path);
        try {
            FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(is);
            grabber.start();
            Java2DFrameConverter converter = new Java2DFrameConverter();
            BufferedImage image = converter.convert(grabber.grabImage());
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(image, "png", os);
            grabber.stop();
            return os.toByteArray();
        } finally {
            Optional.ofNullable(is).ifPresent(o -> {
                try {
                    o.close();
                } catch (IOException e) {

                }
            });
        }
    }
}
