package cms.bean.template;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/23 9:51
 */
@Entity
@Table(name = "rollImg")
@Data
public class RollImg {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(nullable = false)
    private String src;
    @Column(nullable = false)
    private String url;
    // 1：pc，10:手机
    @Column(nullable = false)
    private Integer type;

    public RollImg() {

    }

    public RollImg(String src, String url) {
        this.src = src;
        this.url = url;
    }
}
