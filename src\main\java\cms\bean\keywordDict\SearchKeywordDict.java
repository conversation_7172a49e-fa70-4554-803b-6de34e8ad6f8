package cms.bean.keywordDict;

import cms.bean.BaseUserDel;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/24 13:49
 */
@Entity
@Table(name = "searchKeywordDict")
@Data
public class SearchKeywordDict extends BaseUserDel implements Serializable {

    private static final long serialVersionUID = -1L;

    @Column(length = 100, nullable = false)
    private String keyWord;
}
