package cms.service.user.impl;

import cms.bean.user.TopDonLoginResult;
import cms.bean.user.User;
import cms.bean.user.UserTopDon;
import cms.requset.bean.UserLogin;
import cms.service.besa.DaoSupport;
import cms.service.user.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16 14:19
 */
@Service
public class UserTopDonService extends DaoSupport<UserTopDon> {

    public final static String DEFAULT_ANSWER = "123456";

    @Autowired
    private UserService userService;

    public User addByLogin(UserLogin userLogin, TopDonLoginResult topDonLoginResult) {
        User user = userService.add(userLogin.getAccount(), userLogin.getPassword(), userLogin.getType());
        UserTopDon userTopDon = new UserTopDon();
        userTopDon.setUserId(user.getId());
        userTopDon.setTopDonUserId(topDonLoginResult.getUser_id());
        this.add(userTopDon);
        user.setUserTopDon(userTopDon);
        return user;
    }

    private void add(UserTopDon userTopDon) {
        this.save(userTopDon);
    }
}
