package cms.aspect.impl;

import cms.aspect.FilterKeywordLock;
import cms.handle.MsgException;
import cms.service.messageSource.ErrorMessageService;
import cms.utils.ErrorUtil;
import cms.utils.JsonUtils;
import cms.utils.StringUtil;
import cms.utils.WebUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.Optional;

import static cms.constant.ErrorCode.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/28 16:33
 */
@Aspect
@Component
@Slf4j
public class FilterKeywordLockAspect {

    private static final ThreadLocal<Long> lockKey = new ThreadLocal<Long>();
    private static final ThreadLocal<String> lockEnumsName = new ThreadLocal<String>();

    @Autowired
    private ErrorMessageService errorMessageService;

    @Pointcut("@annotation(cms.aspect.FilterKeywordLock)")
    private void cutMethod() {

    }

    @Before("cutMethod()")
    public void before(JoinPoint joinPoint) throws Throwable {
        Object[] params = joinPoint.getArgs();
        FilterKeywordLock anno = this.getAnno(joinPoint);
        Map<Long, Object> lockMap = Optional.ofNullable(anno).map(FilterKeywordLock::lockMap).map(FilterKeywordLock.LockMapEnum::getLockMap).orElseThrow(() -> new MsgException(errorMessageService.getMessage(C_2_0001_0021)));
        Long lockKey = Optional.ofNullable(anno.lockKeyIndex()).filter(o -> o < params.length).map(o -> params[o]).map(Object::toString).filter(StringUtil::isNumeric).map(Long::parseLong).orElseThrow(() -> new MsgException(errorMessageService.getMessage(C_2_0001_0022)));
        FilterKeywordLockAspect.lockKey.set(Optional.of(lockKey).filter(o -> null == lockMap.putIfAbsent(o, new Object())).orElseThrow(() -> new MsgException(errorMessageService.getMessage(C_2_0001_0020))));
        lockEnumsName.set(anno.lockMap().getName());
    }

    @After(value = "cutMethod()")
    public void after() {
    }

    @AfterReturning(returning = "object", pointcut = "cutMethod()")
    public void doAfterReturning(Object object) {
    }

    @AfterThrowing(value = "cutMethod()", throwing = "exception")
    public void afterThrowing(Exception exception) {
        System.out.println("afterThrowing.........");
        log.info("exception:", exception);
    }

    @Around(value = "cutMethod()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        Object res = null;
        Optional<String> errorMsg = Optional.empty();
        try {
            res = joinPoint.proceed(joinPoint.getArgs());
        } catch (MsgException e) {
            log.error("切面异常", e);
            errorMsg = Optional.ofNullable(e.getMsg()).filter(Strings::isNotEmpty);
        } catch (Throwable e) {
            log.error("切面异常", e);
            errorMsg = Optional.ofNullable(errorMessageService.getMessage(C_2_0001_0500));
        } finally {
            removeLock();
            FilterKeywordLock filterKeywordLock = getAnno(joinPoint);
            Optional<Map<String, Object>> resStr = errorMsg.map(o -> ErrorUtil.getErrorReturnValue("filterKeyword", o));
            Object finalRes = res;
            res = resStr.filter(o -> !filterKeywordLock.isResponseWriter()).map(JSONUtil::toJsonStr).map(o -> (Object) o).orElseGet(() -> {
                resStr.ifPresent(o -> Optional.ofNullable((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).map(ServletRequestAttributes::getResponse).ifPresent(x -> this.writerResponse(o, x)));
                return finalRes;
            });
        }
        return res;
    }

    private FilterKeywordLock getAnno(JoinPoint joinPoint) throws NoSuchMethodException {
        String methodName = joinPoint.getSignature().getName();
        Class<?> targetClass = joinPoint.getTarget().getClass();
        Class<?>[] parameterTypes = ((MethodSignature) joinPoint.getSignature()).getParameterTypes();
        Method objMethod = targetClass.getMethod(methodName, parameterTypes);

        return Optional.ofNullable(objMethod.getDeclaredAnnotation(FilterKeywordLock.class)).orElseGet(() -> this.getAnno(joinPoint, objMethod, parameterTypes));
    }

    private FilterKeywordLock getAnno(JoinPoint joinPoint, Method objMethod, Class<?>[] parameterTypes) {
        try {
            return ((Class) AopUtils.getTargetClass(joinPoint.getTarget()).getGenericInterfaces()[0]).getDeclaredMethod(objMethod.getName(), parameterTypes).getAnnotation(FilterKeywordLock.class);
        } catch (Exception e) {
            return null;
        }
    }

    private void writerResponse(Map<String, Object> resultMap, HttpServletResponse response) {
        try {
            WebUtil.writeToWeb(JsonUtils.toJSONString(resultMap), "json", response);
        } catch (IOException e) {
            log.error(String.format("writer message error! %s", resultMap), e);
        }
    }

    private void removeLock() {
        Optional.ofNullable(lockKey.get()).ifPresent(k -> FilterKeywordLock.LockMapEnum.getEnum(lockEnumsName.get()).map(FilterKeywordLock.LockMapEnum::getLockMap).ifPresent(m -> m.remove(k)));
    }
}
