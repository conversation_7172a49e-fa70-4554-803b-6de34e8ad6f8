package cms.web.action;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;

import javax.validation.Validator;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.Locale;
import java.util.Map;

/**
 * 异常国际化文件
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class I18nConfig {

//    @Autowired
//    private MessageSource messageSource;

    @Primary
    @Bean(name = "messageSource")
    public ReloadableResourceBundleMessageSource messageSource() {
        ReloadableResourceBundleMessageSource messageBundle = new ReloadableResourceBundleMessageSource();
        messageBundle.setBasenames("classpath:i18n/errorMessages", "classpath:i18n/springSecurityMessages", "classpath:i18n/validateMessages", "classpath:i18n/responseCodeMessages");//即配置文件所在目录为 i18n，文件前缀为 errorMessages
        messageBundle.setDefaultEncoding("UTF-8");
        messageBundle.setFallbackToSystemLocale(false);//是否使用系统默认的编码  默认为true
        converExcel();
        return messageBundle;
    }

    @Bean
    public Validator getValidator() {
        LocalValidatorFactoryBean validatorFactoryBean = new LocalValidatorFactoryBean();
        validatorFactoryBean.setValidationMessageSource(messageSource());
        return validatorFactoryBean;
    }

    @Bean
    public LocaleResolver localeResolver() {
        AcceptHeaderLocaleResolver acceptHeaderLocaleResolver = new AcceptHeaderLocaleResolver();
        acceptHeaderLocaleResolver.setSupportedLocales(Arrays.asList(Locale.SIMPLIFIED_CHINESE, Locale.US));
        acceptHeaderLocaleResolver.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
        return acceptHeaderLocaleResolver;
    }

    private void converExcel() {
//        List<String> fileNames = new ArrayList<String>();
//        fileNames.add("validateMessages");
//        fileNames.add("responseCodeMessages");
//
//        File dir = new File(getClass().getClassLoader().getResource("i18n").getPath());
//        if (!dir.exists()) {
//            return;
//        }
//        fileNames.stream().forEach(o -> {
//            Map<String, Map<String, String>> ex = Maps.newHashMap();
//            List<String> fs = Arrays.stream(dir.list()).filter(f -> f.startsWith(o)).collect(Collectors.toList());
//            fs.forEach(f -> {
//                try {
//                    String key = Optional.of(f.replace(o, "").replace(".properties", "")).filter(StringUtils::isNotEmpty).orElse("默认");
//                    ex.put(key, readFile(f));
//                } catch (Exception e) {
//                    log.error("读取文件" + f + "错误！", e);
//                }
//            });
//            Set<String> head = Sets.newHashSet();
//            List<Map<String, String>> ex1 = ex.get("默认").entrySet().stream().map(m -> {
//                Map<String, String> result = Maps.newHashMap();
//                result.put("唯一编码", m.getKey());
//                ex.entrySet().stream().filter(m1 -> !m1.getKey().equals("默认")).filter(m1 -> StringUtils.isNotEmpty(m1.getValue().get(m.getKey()))).forEach(m1 -> {
//                    result.put(m1.getKey(), m1.getValue().get(m.getKey()));
//                });
//                head.addAll(result.keySet());
//                return result;
//            }).collect(Collectors.toList());
//            File readfile = new File("d:/fanyi/" + o + ".xlsx");
//            List<List<String>> ab = head.stream().map(Lists::newArrayList).collect(Collectors.toList());
//            ab.set(0, Lists.newArrayList("唯一编码"));
//            List<List<String>> va = ex1.stream().map(m -> ab.stream().map(x -> Optional.ofNullable(m.get(x.get(0))).orElse("")).collect(Collectors.toList())).collect(Collectors.toList());
//
//            EasyExcel.write(readfile).head(ab).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("fanyi").doWrite(va);
//        });
    }

    private Map<String, String> readFile(String ptah) throws Exception {
        Map<String, String> map = Maps.newHashMap();
        InputStreamReader ir = new InputStreamReader(getClass().getClassLoader().getResourceAsStream("i18n/" + ptah));
        BufferedReader bufferedReader = new BufferedReader(ir);
        while (bufferedReader.ready()) {
            String s = bufferedReader.readLine();
            String[] a = s.split("=");
            if (a.length >= 2) {
                map.put(a[0], a[1]);
            } else {
                map.put(a[0], "");
            }
        }
        return map;
    }
}
