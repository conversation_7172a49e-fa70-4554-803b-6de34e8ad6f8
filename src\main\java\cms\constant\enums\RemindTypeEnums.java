package cms.constant.enums;

import cms.constant.Constant;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static cms.constant.Constant.EMAIL;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/24 10:33
 */
public enum RemindTypeEnums {

    COMMENT_MY_TOPIC(10, "别人评论了我的话题"),
    REPLY_MY_TOPIC(20, "别人回复了我的话题"),
    QUOTE_MY_COMMENT(30, "别人引用了我的评论"),
    REPLY_MY_COMMENT(40, "别人回复了我的评论"),
    REPLY_MY_REPLY_COMMENT(50, "别人回复了我回复过的评论"),
    UNLOCK_MY_TOPIC(60, "别人解锁了我的话题"),
    LIKE_MY_TOPIC(70, "别人点赞了我的话题"),
    FOLLOW_ME(80, "别人关注了我"),
    IDOL_TOPIC(90, "我关注的人发表了话题"),
    IDOL_COMMENT(100, "我关注的人发表了评论"),
    IDOL_REPLY(110, "我关注的人发表了回复"),
    ANSWER_MY_QA(120, "别人回答了我的问题"),
    REPLY_MY_QA(130, "别人回复了我的问题"),
    REPLY_MY_ANSWER(140, "别人回复了我的答案"),
    REPLY_MY_REPLY_ANSWER(150, "别人回复了我回复过的答案"),
    FAVORITE_MY_TOPIC(160, "别人收藏了我的话题"),
    IDOL_QA(170, "我关注的人提出了问题"),
    IDOL_APPEND_QA(180, "我关注的人追加了提问"),
    ADOPTION_MY_ANSWER(190, "别人采纳了我的答案"),

    PRIVATE_LETTER(1000, "私信", EMAIL),
    ;

    private Integer code;
    private String desc;
    private String type = Constant.DEFAULT;
    final static Map<Integer, String> map;

    static {
        map = Arrays.stream(RemindTypeEnums.values()).collect(Collectors.toMap(RemindTypeEnums::getCode, RemindTypeEnums::getDesc));
    }

    RemindTypeEnums(Integer code, String desc, String type) {
        this(code, desc);
        this.type = type;
    }

    RemindTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(Integer code) {
        return Optional.ofNullable(map.get(code)).orElse("");
    }
}
