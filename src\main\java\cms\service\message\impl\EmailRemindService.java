package cms.service.message.impl;

import cms.bean.follow.FollowEntity;
import cms.bean.message.EmailRemind;
import cms.bean.setting.EmailRemindSetting;
import cms.bean.user.User;
import cms.constant.enums.RemindTypeEnums;
import cms.service.besa.DaoSupport;
import cms.service.follow.FollowService;
import cms.service.setting.impl.EmailRemindSettingService;
import cms.web.action.user.UserManage;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static cms.constant.Constant.EMAIL_REMIND_TEMPLATE_KEY_MESSAGEPAGE;
import static cms.constant.Constant.EMAIL_REMIND_TEMPLATE_KEY_RECEIVEDNICKNAME;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/23 17:56
 */
@Service
@Slf4j
public class EmailRemindService extends DaoSupport<EmailRemind> {

    private static ConcurrentMap<String, ConcurrentMap<Integer, AtomicInteger>> emailRemindSendCountMap = Maps.newConcurrentMap();
    private static ConcurrentMap<String, ConcurrentMap<Integer, AtomicInteger>> emailRemindSendCountCache = Maps.newConcurrentMap();
    private static ConcurrentMap<String, Boolean> isNoSendEmailRemind = Maps.newConcurrentMap();
    private static Semaphore semaphore = new Semaphore(1);
    public final static Integer totalEmailRemindNumKey = 0;

    public final static int EmailRemindQueueSize = 5000;
    private static ArrayBlockingQueue<EmailRemind> queue = new ArrayBlockingQueue<EmailRemind>(EmailRemindQueueSize);
    private static Set<String> emailRemindBusinessUserName = Sets.newHashSet();
    private static AtomicReference<LocalDate> localDate = new AtomicReference<>(LocalDate.now());

    @Autowired
    private FollowService followService;
    @Autowired
    private EmailRemindSettingService emailRemindSettingService;
    @Autowired
    private UserManage userManage;
    @Value("${emailRemind.pagePath}")
    private String pagePath;
    @Autowired
    private EmailRemindService emailRemindService;

    public void initEmailRemindSendCountMap() {
        if (semaphore.tryAcquire()) {
            emailRemindSendCountMap = Optional.ofNullable(emailRemindSendCountMap).filter(CollUtil::isNotEmpty).orElseGet(() -> Optional.ofNullable(emailRemindService.getEmailRemindCache()).orElseGet(() -> emailRemindSendCountMap));
        }
    }

    @Cacheable(value = "emailRemind_cache", key = "'findAllEmailRemind_default'")
    public ConcurrentMap<String, ConcurrentMap<Integer, AtomicInteger>> getEmailRemindCache() {
        return emailRemindSendCountCache;
    }

    @CacheEvict(value = "emailRemind_cache", allEntries = true)
    public void removeAll() {
        emailRemindSendCountCache.clear();
        emailRemindSendCountMap.clear();
    }

    @CachePut(value = "emailRemind_cache", key = "'findAllEmailRemind_default'")
    public ConcurrentMap<String, ConcurrentMap<Integer, AtomicInteger>> addEmailRemindCache(String email, Integer remindType) {
        ConcurrentMap<Integer, AtomicInteger> sendMap = getInitMap(emailRemindSendCountCache, email);
        sendMap.get(totalEmailRemindNumKey).getAndIncrement();
        sendMap.get(remindType).getAndIncrement();
        return emailRemindSendCountCache;
    }

    @Cacheable(value = "emailRemind_time_cache", key = "'findAllEmailRemind_time_default'")
    public LocalDate getSendTime() {
        return localDate.get();
    }

    @CacheEvict(value = "emailRemind_time_cache", allEntries = true)
    public boolean removeSendTime(LocalDate o) {
        return localDate.compareAndSet(o, LocalDate.now());
    }

    public List<String> getNoSendNum() {
        return Optional.ofNullable(isNoSendEmailRemind).map(m -> m.keySet().stream().collect(Collectors.toList())).orElseGet(() -> Lists.newArrayList());
    }

    public Boolean isSendEmail(String email, Integer remindType) {
        Map<Integer, EmailRemindSetting> emailRemindSettingMap = emailRemindSettingService.findEmailRemindSetting_cache();
        ConcurrentMap<Integer, AtomicInteger> sendMap = getInitMap(emailRemindSendCountMap, email);

        String isNoSendEmailRemindKey = isNoSendEmailRemindKey(email, remindType);
        AtomicInteger remindTypeNum = sendMap.get(remindType);
        int sendNum = remindTypeNum.getAndIncrement();
        if (Optional.ofNullable(emailRemindSettingMap.get(remindType)).map(EmailRemindSetting::getSendNum).filter(o -> 0 != o).map(o -> Boolean.TRUE).orElse(Boolean.FALSE)) {
            if (sendNum >= emailRemindSettingMap.get(remindType).getSendNum()) {
                isNoSendEmailRemind.put(isNoSendEmailRemindKey, Boolean.TRUE);
                remindTypeNum.getAndDecrement();
                return Boolean.FALSE;
            }
        }
        AtomicInteger totalNum = sendMap.get(totalEmailRemindNumKey);
        int totalSendNum = totalNum.getAndIncrement();
        if (Optional.ofNullable(emailRemindSettingMap.get(totalEmailRemindNumKey)).map(EmailRemindSetting::getSendNum).map(o -> 0 != o).map(o -> Boolean.TRUE).orElse(Boolean.FALSE)) {
            if (totalSendNum >= emailRemindSettingMap.get(totalEmailRemindNumKey).getSendNum()) {
                remindTypeNum.getAndDecrement();
                totalNum.getAndDecrement();
                isNoSendEmailRemind.put(email, Boolean.TRUE);
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    @CacheEvict(value = "emailRemind_cache", allEntries = true)
    public void decrementSendNum(String email, Integer remindType) {
        ConcurrentMap<Integer, AtomicInteger> sendMap = emailRemindSendCountMap.get(email);
        sendMap.get(totalEmailRemindNumKey).getAndDecrement();
        sendMap.get(remindType).getAndDecrement();
    }

    public static String isNoSendEmailRemindKey(String email, Integer remindType) {
        return String.format("%s,%s", email, remindType);
    }

    public static void addEmailRemind(EmailRemind emailRemind) {
        if (emailRemind.getReceiverUser().map(o -> EmailRemindService.isNoSendEmailRemindKey(o.getEmail(), emailRemind.getRemindType())).map(isNoSendEmailRemind::get).orElse(Boolean.FALSE)) {
            return;
        } else if (Optional.ofNullable(emailRemind).filter(o -> !o.getReceiverUser().isPresent()).map(EmailRemind::getBusinessUserName).map(emailRemindBusinessUserName::contains).orElse(Boolean.FALSE)) {
            return;
        }
        queue.add(emailRemind);
        Optional.ofNullable(emailRemind).filter(o -> !o.getReceiverUser().isPresent()).map(EmailRemind::getBusinessUserName).ifPresent(emailRemindBusinessUserName::add);
    }

    public static void putEmailRemind(EmailRemind emailRemind) throws InterruptedException {
        if (emailRemind.getReceiverUser().map(o -> EmailRemindService.isNoSendEmailRemindKey(o.getEmail(), emailRemind.getRemindType())).map(isNoSendEmailRemind::get).orElse(Boolean.FALSE)) {
            return;
        } else if (Optional.ofNullable(emailRemind).filter(o -> !o.getReceiverUser().isPresent()).map(EmailRemind::getBusinessUserName).map(emailRemindBusinessUserName::contains).orElse(Boolean.FALSE)) {
            return;
        }
        queue.put(emailRemind);
        Optional.ofNullable(emailRemind).filter(o -> !o.getReceiverUser().isPresent()).map(EmailRemind::getBusinessUserName).ifPresent(emailRemindBusinessUserName::add);
    }

    public static int getEmailRemindSize() {
        return queue.size();
    }

    public static void putEmailRemindNoThrow(EmailRemind emailRemind) {
        Optional.ofNullable(emailRemind).ifPresent(o -> {
            try {
                EmailRemindService.putEmailRemind(o);
            } catch (InterruptedException ex) {
                log.error("send error email remind,once more add error!" + JSON.toJSONString(o), ex);
            }
        });
    }

    public static EmailRemind takeEmailRemind() throws InterruptedException {
        EmailRemind emailRemind = queue.take();
        Optional.ofNullable(emailRemind.getBusinessUserName()).ifPresent(emailRemindBusinessUserName::remove);
        return emailRemind;
    }

    public List<User> findEmailByFollow(Long userId, String userName, int firstIndex, int maxResult) {
        return Optional.ofNullable(followService.findFollowerByUserNameAndEmailRemind(userId, userName, firstIndex, maxResult).getResultlist()).filter(CollUtil::isNotEmpty)
                .map(l -> l.stream().map(FollowEntity::getFriendUserName).map(userManage::query_cache_findUserByUserName).collect(Collectors.toList())).orElseGet(() -> Lists.newArrayList());
    }

    public SenMessageBean getSendMsg(User receiverUser, EmailRemind emailRemind) {
        Map<Integer, EmailRemindSetting> emailRemindSettingMap = emailRemindSettingService.findEmailRemindSetting_cache();
        EmailRemindSetting emailRemindSetting = Optional.ofNullable(emailRemindSettingMap.get(emailRemind.getRemindType())).orElseGet(() -> Optional.ofNullable(emailRemindSettingMap.get(totalEmailRemindNumKey)).orElseGet(() -> EmailRemindSettingService.defaultMap.get(totalEmailRemindNumKey)));

        SenMessageBean result = new SenMessageBean();
        result.setTitle(emailRemindSetting.getTitleTemplate());
        result.setContent(StrUtil.format(emailRemindSetting.getContentTemplate(), this.templateMap(receiverUser)));
        return result;
    }

    private Map<String, String> templateMap(User receiverUser) {
        Map<String, String> result = Maps.newHashMap();
        result.put(EMAIL_REMIND_TEMPLATE_KEY_MESSAGEPAGE, this.pagePath + "user/message");
        result.put(EMAIL_REMIND_TEMPLATE_KEY_RECEIVEDNICKNAME, receiverUser.getNickname());
        return result;
    }

    private ConcurrentMap<Integer, AtomicInteger> getInitMap(ConcurrentMap<String, ConcurrentMap<Integer, AtomicInteger>> map, String email) {
        ConcurrentMap<Integer, AtomicInteger> sendMap = Optional.ofNullable(map.get(email)).orElseGet(() -> {
            ConcurrentMap<Integer, AtomicInteger> concurrentMap = Maps.newConcurrentMap();
            Arrays.stream(RemindTypeEnums.values()).forEach(o -> concurrentMap.put(o.getCode(), new AtomicInteger(0)));
            concurrentMap.put(totalEmailRemindNumKey, new AtomicInteger(0));
            return Optional.ofNullable(map.putIfAbsent(email, concurrentMap)).orElseGet(() -> map.get(email));
        });
        return sendMap;
    }

    @Data
    public static class SenMessageBean {
        private String title;
        private String content;
    }
}
