package cms.web.action.common;


import cms.bean.user.ResourceEnum;
import cms.service.follow.FollowService;
import cms.service.follow.impl.FollowFormService;
import cms.service.message.RemindService;
import cms.service.setting.SettingService;
import cms.service.template.TemplateService;
import cms.web.action.AccessSourceDeviceManage;
import cms.web.action.CSRFTokenManage;
import cms.web.action.follow.FollowManage;
import cms.web.action.follow.FollowerManage;
import cms.web.action.message.RemindManage;
import cms.web.action.user.RoleAnnotation;
import cms.web.action.user.UserManage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 关注接收表单
 */
@Controller
@RequestMapping("user/control/follow")
public class FollowFormAction {

    @Autowired
    private FollowFormService followFormService;

    /**
     * 关注   添加
     *
     * @param model
     * @param userName 用户名称
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @RoleAnnotation(resourceCode = ResourceEnum._5001000)
    public String add(ModelMap model, String userName, String token, String jumpUrl,
                      RedirectAttributes redirectAttrs,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {
        return followFormService.add(model, userName, token, jumpUrl, redirectAttrs, request, response);
    }
}
