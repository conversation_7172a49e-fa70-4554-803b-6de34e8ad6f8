package cms.listeners;

import cms.bean.BaseUserDel;
import cms.utils.BaseUserUtils;
import com.hy.corecode.idgen.YitIdGenerator;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/9 14:55
 */
public class BaseUserDelListeners implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PrePersist
    public void prePersist(Object obj) {
        if (!(obj instanceof BaseUserDel)) {
            return;
        }
        YitIdGenerator yitIdGenerator = applicationContext.getBean("yitIdGenerator", YitIdGenerator.class);
        BaseUserUtils.addUser(obj, yitIdGenerator.next());
    }

    @PreUpdate
    public void proUpdate(Object obj) {
        BaseUserUtils.updateUser(obj);
    }
}
