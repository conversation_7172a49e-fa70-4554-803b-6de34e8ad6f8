package cms.utils;

import java.util.Iterator;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/11 11:55
 */
public class TopDonUtil {

    private static final String QUERY_APP_KEY = "appkey";
    private static final String QUERY_APP_SECRET = "appSecret";
    private static final String QUERY_TIMESTAMP = "timestamp";
    private static final String QUERY_NONCE_STR = "nonceStr";
    private static final String QUERY_SIGN = "sign";
    private final static String app_key = "6DCC55A836A246BEBAD583EBBA995ED1";
    private final static String app_Secret = "DAC450E89BEB43B5B02CD6BE82B8C76E";

    public static void initSign(Map<String, String> queryParam) {
        queryParam.put(QUERY_NONCE_STR, String.valueOf(System.currentTimeMillis()));
        queryParam.put(QUERY_TIMESTAMP, String.valueOf(System.currentTimeMillis()));
        queryParam.put(QUERY_APP_KEY, app_key);
        StringBuffer signTemp = new StringBuffer(asciiSort(queryParam));
        signTemp.append(QUERY_APP_SECRET).append("=").append(app_Secret);
        queryParam.put(QUERY_SIGN, MD5Utils.md5(signTemp.toString()).toUpperCase());
    }

    public static String getParamString(Map<String, String> queryParam) {
        return queryParam.entrySet().stream().map(o -> String.format("%s=%s", o.getKey(), o.getValue())).collect(Collectors.joining("&"));
    }

    public static String asciiSort(Map<String, String> map) {
        SortedMap<String, Object> params = new TreeMap();
        Iterator var2 = map.keySet().iterator();

        while (var2.hasNext()) {
            String s = (String) var2.next();
            params.put(s, map.get(s));
        }

        StringBuffer str = new StringBuffer();
        Iterator var6 = params.keySet().iterator();

        while (var6.hasNext()) {
            String s = (String) var6.next();
            str.append(s).append("=").append(params.get(s)).append("&");
        }

        return str.toString();
    }
}
