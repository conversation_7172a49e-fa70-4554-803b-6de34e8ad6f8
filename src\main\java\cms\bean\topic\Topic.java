package cms.bean.topic;

import cms.bean.MediaInfo;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

import static javax.persistence.FetchType.EAGER;

/**
 * 话题
 */
@Entity
@Table(indexes = {@Index(name = "topic_idx", columnList = "tagId,status"), @Index(name = "topic_3_idx", columnList = "userName,postTime"), @Index(name = "topic_5_idx", columnList = "status,sort,lastReplyTime")})
@Data
public class Topic implements Serializable {
    private static final long serialVersionUID = -684257451052921859L;

    /**
     * Id
     **/
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 标题
     **/
    @Column(length = 190)
    private String title;

    /**
     * 标签Id
     **/
    private Long tagId;
    /**
     * 标签名称
     **/
    @Transient
    private String tagName;

    /**
     * 话题内容
     **/
    @Lob
    private String content;
    /**
     * 内容摘要
     **/
    @Lob
    private String summary;

    @OneToMany(fetch = EAGER)
    @JoinColumn(name = "topicId", referencedColumnName = "id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private List<TopicTagAssociation> associations;

    /**
     * 发表时间
     **/
    @Temporal(TemporalType.TIMESTAMP)
    private Date postTime = new Date();
    /**
     * 最后回复时间
     **/
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastReplyTime;
    /**
     * 最后修改时间
     **/
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastUpdateTime;
    /**
     * IP
     **/
    @Column(length = 45)
    private String ip;
    /**
     * IP归属地
     **/
    @Transient
    private String ipAddress;

    /**
     * 图片 List<ImageInfo>json格式
     **/
    @Lob
    private String image;
    @Transient
    private List<ImageInfo> imageInfoList = new ArrayList<ImageInfo>();

    /**
     * 媒体文件信息集合
     **/
    @Transient
    private List<MediaInfo> mediaInfoList = new ArrayList<MediaInfo>();
    /**
     * youtube视频信息集合
     **/
    @Transient
    private List<String> youtubeLinkList = new ArrayList<>();

    /**
     * 评论总数
     **/
    private Long commentTotal = 0L;
    /**
     * 允许评论
     **/
    private boolean allow = true;

    /**
     * 查看总数
     **/
    private Long viewTotal = 0L;


    /**
     * 用户名称
     **/
    @Column(length = 30)
    private String userName;
    /**
     * 账号
     **/
    @Transient
    private String account;
    /**
     * 呢称
     **/
    @Transient
    private String nickname;
    /**
     * 头像路径
     **/
    @Transient
    private String avatarPath;
    /**
     * 头像名称
     **/
    @Transient
    private String avatarName;
    /**
     * 用户信息状态 -30.账号已注销(不显示数据) -20.账号已逻辑删除(不显示数据) -10.账号已禁用(不显示数据)  0.正常 10.账号已禁用(显示数据) 20.账号已逻辑删除(显示数据)
     **/
    @Transient
    private Integer userInfoStatus = 0;

    /**
     * 用户等级,按积分从低祷告，最高五级
     */
    @Transient
    private Integer userGrade;

    /**
     * 用户角色名称集合
     **/
    @Transient
    private List<String> userRoleNameList = new ArrayList<String>();

    /**
     * 话题允许查看的角色名称集合(默认角色除外)
     **/
    @Transient
    private List<String> allowRoleViewList = new ArrayList<String>();

    /**
     * key:内容含有隐藏标签类型  10.输入密码可见  20.评论话题可见  30.达到等级可见 40.积分购买可见 50.余额购买可见  value:当前用户是否已对隐藏内容解锁
     **/
    @Transient
    private LinkedHashMap<Integer, Boolean> hideTagTypeMap = new LinkedHashMap<Integer, Boolean>();

    /**
     * 是否为员工 true:员工  false:会员
     **/
    private Boolean isStaff = false;
    /**
     * 排序
     **/
    private Integer sort = 0;

    /**
     * 精华
     **/
    private Boolean essence = false;

    /**
     * 状态 10.待审核 20.已发布 110.待审核删除 120.已发布删除
     **/
    private Integer status = 10;

    /**
     * 收藏总数
     */
    @Transient
    private Long favoriteCount;

    /**
     * 点赞总数
     */
    @Transient
    private Long likeCount;

    /**
     * 简介
     */
    @Transient
    private String intro;
}
