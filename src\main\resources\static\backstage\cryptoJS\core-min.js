(function(k,g){if(typeof exports==="object")module.exports=exports=g();else if(typeof define==="function"&&define.amd)define([],g);else k.CryptoJS=g()})(this,function(){var k=k||function(g,s){var t=Object.create||function(){function a(){}return function(b){a.prototype=b;b=new a;a.prototype=null;return b}}(),l={},m=l.lib={},i=m.Base=function(){return{extend:function(a){var b=t(this);a&&b.mixIn(a);if(!b.hasOwnProperty("init")||this.init===b.init)b.init=function(){b.$super.init.apply(this,arguments)};
b.init.prototype=b;b.$super=this;return b},create:function(){var a=this.extend();a.init.apply(a,arguments);return a},init:function(){},mixIn:function(a){for(var b in a)if(a.hasOwnProperty(b))this[b]=a[b];if(a.hasOwnProperty("toString"))this.toString=a.toString},clone:function(){return this.init.prototype.extend(this)}}}(),j=m.WordArray=i.extend({init:function(a,b){a=this.words=a||[];this.sigBytes=b!=s?b:a.length*4},toString:function(a){return(a||u).stringify(this)},concat:function(a){var b=this.words,
d=a.words,c=this.sigBytes;a=a.sigBytes;this.clamp();if(c%4)for(var e=0;e<a;e++)b[c+e>>>2]|=(d[e>>>2]>>>24-e%4*8&255)<<24-(c+e)%4*8;else for(e=0;e<a;e+=4)b[c+e>>>2]=d[e>>>2];this.sigBytes+=a;return this},clamp:function(){var a=this.words,b=this.sigBytes;a[b>>>2]&=4294967295<<32-b%4*8;a.length=g.ceil(b/4)},clone:function(){var a=i.clone.call(this);a.words=this.words.slice(0);return a},random:function(a){for(var b=[],d=function(f){f=f;var n=987654321;return function(){n=36969*(n&65535)+(n>>16)&4294967295;
f=18E3*(f&65535)+(f>>16)&4294967295;var o=(n<<16)+f&4294967295;o/=4294967296;o+=0.5;return o*(g.random()>0.5?1:-1)}},c=0,e;c<a;c+=4){var h=d((e||g.random())*4294967296);e=h()*987654071;b.push(h()*4294967296|0)}return new j.init(b,a)}}),p=l.enc={},u=p.Hex={stringify:function(a){var b=a.words;a=a.sigBytes;for(var d=[],c=0;c<a;c++){var e=b[c>>>2]>>>24-c%4*8&255;d.push((e>>>4).toString(16));d.push((e&15).toString(16))}return d.join("")},parse:function(a){for(var b=a.length,d=[],c=0;c<b;c+=2)d[c>>>3]|=
parseInt(a.substr(c,2),16)<<24-c%8*4;return new j.init(d,b/2)}},q=p.Latin1={stringify:function(a){var b=a.words;a=a.sigBytes;for(var d=[],c=0;c<a;c++)d.push(String.fromCharCode(b[c>>>2]>>>24-c%4*8&255));return d.join("")},parse:function(a){for(var b=a.length,d=[],c=0;c<b;c++)d[c>>>2]|=(a.charCodeAt(c)&255)<<24-c%4*8;return new j.init(d,b)}},v=p.Utf8={stringify:function(a){try{return decodeURIComponent(escape(q.stringify(a)))}catch(b){throw new Error("Malformed UTF-8 data");}},parse:function(a){return q.parse(unescape(encodeURIComponent(a)))}},
r=m.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new j.init;this._nDataBytes=0},_append:function(a){if(typeof a=="string")a=v.parse(a);this._data.concat(a);this._nDataBytes+=a.sigBytes},_process:function(a){var b=this._data,d=b.words,c=b.sigBytes,e=this.blockSize,h=c/(e*4);h=a?g.ceil(h):g.max((h|0)-this._minBufferSize,0);a=h*e;c=g.min(a*4,c);if(a){for(var f=0;f<a;f+=e)this._doProcessBlock(d,f);f=d.splice(0,a);b.sigBytes-=c}return new j.init(f,c)},clone:function(){var a=i.clone.call(this);
a._data=this._data.clone();return a},_minBufferSize:0});m.Hasher=r.extend({cfg:i.extend(),init:function(a){this.cfg=this.cfg.extend(a);this.reset()},reset:function(){r.reset.call(this);this._doReset()},update:function(a){this._append(a);this._process();return this},finalize:function(a){a&&this._append(a);return this._doFinalize()},blockSize:16,_createHelper:function(a){return function(b,d){return(new a.init(d)).finalize(b)}},_createHmacHelper:function(a){return function(b,d){return(new w.HMAC.init(a,
d)).finalize(b)}}});var w=l.algo={};return l}(Math);return k});
