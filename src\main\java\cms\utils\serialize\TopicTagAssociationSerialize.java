package cms.utils.serialize;

import cms.bean.topic.Tag;
import cms.bean.topic.TopicTagAssociation;
import cms.service.topic.TagService;
import cms.utils.SpringUtils;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.google.common.collect.Maps;

import java.io.IOException;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/6/15 15:32
 */
public class TopicTagAssociationSerialize extends JsonSerializer<TopicTagAssociation> {
    @Override
    public void serialize(TopicTagAssociation value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        Map<Long, Tag> tagMap = Optional.ofNullable(SpringUtils.getBean(TagService.class)).map(TagService::findAllTag_cache).map(l -> l.stream().collect(Collectors.toMap(Tag::getId, o -> o))).orElseGet(() -> Maps.newHashMap());
        value.setTagName(Optional.ofNullable(value.getTagName()).orElseGet(() -> Optional.ofNullable(value.getTagId()).map(tagMap::get).map(Tag::getName).orElse("")));
        value.setGrade(Optional.ofNullable(value.getGrade()).orElseGet(() -> Optional.ofNullable(value.getTagId()).map(tagMap::get).map(Tag::getGrade).orElse(0)));
        gen.writeObject(JSONUtil.parse(value));
    }
}
