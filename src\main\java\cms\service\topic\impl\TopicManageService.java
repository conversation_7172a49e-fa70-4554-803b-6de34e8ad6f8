package cms.service.topic.impl;

import cms.bean.RequestResult;
import cms.bean.ResultCode;
import cms.bean.staff.SysUsers;
import cms.bean.topic.*;
import cms.bean.user.User;
import cms.bean.user.UserGrade;
import cms.constant.ErrorCode;
import cms.handle.CustomException;
import cms.service.aliyun.OssFileChangeService;
import cms.service.topic.TagService;
import cms.service.topic.TopicIndexService;
import cms.service.topic.TopicService;
import cms.service.user.UserGradeService;
import cms.service.user.UserService;
import cms.utils.IpAddress;
import cms.utils.JsonUtils;
import cms.utils.Verification;
import cms.web.action.TextFilterManage;
import cms.web.action.topic.TopicManage;
import cms.web.action.user.UserManage;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

import static cms.constant.Constant.TOPIC_TAG_GRADE_ONE;
import static cms.constant.Constant.TOPIC_TAG_GRADE_TWO;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/5 9:15
 */
@Service
@Slf4j
public class TopicManageService {

    @Resource
    TopicService topicService;
    @Resource
    TextFilterManage textFilterManage;
    @Resource
    TagService tagService;
    @Resource
    TopicManage topicManage;
    @Resource
    TopicIndexService topicIndexService;
    @Autowired
    private TopicTagAssociationService topicTagAssociationService;
    @Resource
    UserGradeService userGradeService;
    @Resource
    UserManage userManage;
    @Resource
    UserService userService;
    @Autowired
    private OssFileChangeService ossFileChangeService;

    @Transactional(propagation = Propagation.REQUIRED)
    public String add(ModelMap model, Long[] tagIdArray, String tagName, String title, Boolean allow, Integer status, Boolean essence,
                      String content, String sort,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<Long> tagId = Arrays.stream(tagIdArray).distinct().collect(Collectors.toList());
        Topic topic = new Topic();
        List<String> imageNameList = null;
        boolean isImage = false;//是否含有图片
        List<String> flashNameList = null;
        boolean isFlash = false;//是否含有Flash
        List<String> mediaNameList = null;
        boolean isMedia = false;//是否含有音视频
        List<String> fileNameList = null;
        boolean isFile = false;//是否含有文件
        boolean isMap = false;//是否含有地图
        Map<String, String> error = new HashMap<String, String>();

        String username = "";//用户名称
        Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (obj instanceof SysUsers) {
            username = ((SysUsers) obj).getUserAccount();
        }

        List<UserGrade> userGradeList = userGradeService.findAllGrade();

        topic.setTagId(0L);
        topic.setTagName(tagName);
        topic.setAllow(allow);
        topic.setStatus(status);
        topic.setEssence(essence);
        Date d = new Date();
        topic.setPostTime(d);
        topic.setLastReplyTime(d);
        String finalUsername = username;
        List<TopicTagAssociation> topicTagAssociations = tagId.stream().filter(o -> null != o).map(o -> new TopicTagAssociation(o, finalUsername)).collect(Collectors.toList());
        Map<Long, Tag> tagMap = tagService.findAllTag_cache().stream().collect(Collectors.toMap(o -> o.getId(), o -> o));
        if (topicTagAssociations.size() == 0) {
            error.put("tagId", "标签不能为空");
        } else {

            if (title != null && !"".equals(title.trim())) {
                topic.setTitle(title);
                if (title.length() > 150) {
                    error.put("title", "不能大于150个字符");
                }
            } else {
                error.put("title", "标题不能为空");
            }
            topic.setTagId(topicTagAssociations.stream().map(TopicTagAssociation::getTagId).map(tagMap::get).filter(o -> o.getGrade() == TOPIC_TAG_GRADE_ONE).findAny().map(Tag::getId).orElseThrow(() -> new CustomException(ErrorCode.C_1_0002_0002, "tagId")));

            if (content != null && !"".equals(content.trim())) {
                //过滤标签
                content = textFilterManage.filterTag(request, content);
                Object[] object = textFilterManage.filterHtml(request, content, "topic", null);
                String value = (String) object[0];
                imageNameList = (List<String>) object[1];
                isImage = (Boolean) object[2];//是否含有图片
                flashNameList = (List<String>) object[3];
                isFlash = (Boolean) object[4];//是否含有Flash
                mediaNameList = (List<String>) object[5];
                isMedia = (Boolean) object[6];//是否含有音视频
                fileNameList = (List<String>) object[7];
                isFile = (Boolean) object[8];//是否含有文件
                isMap = (Boolean) object[9];//是否含有地图

                //校正隐藏标签
                String validValue = textFilterManage.correctionHiddenTag(value, userGradeList);

                //解析隐藏标签
                Map<Integer, Object> analysisHiddenTagMap = textFilterManage.analysisHiddenTag(validValue);
                for (Map.Entry<Integer, Object> entry : analysisHiddenTagMap.entrySet()) {
                    //管理员账户不能发表'余额购买'话题
                    if (entry.getKey().equals(HideTagType.AMOUNT.getName())) {
                        error.put("content", "管理员账户不能发表'余额购买'话题");
                        break;
                    }
                }

                //删除隐藏标签
                String new_content = textFilterManage.deleteHiddenTag(value);

                //不含标签内容
                String text = textFilterManage.filterText(textFilterManage.specifyHtmlTagToText(new_content));
                //清除空格&nbsp;
                String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();
                //摘要
                if (trimSpace != null && !"".equals(trimSpace)) {
                    if (trimSpace.length() > 180) {
                        topic.setSummary(trimSpace.substring(0, 180) + "..");
                    } else {
                        topic.setSummary(trimSpace + "..");
                    }
                }

                //不含标签内容
                String source_text = textFilterManage.filterText(content);
                //清除空格&nbsp;
                String source_trimSpace = cms.utils.StringUtil.replaceSpace(source_text).trim();

                if (isImage == true || isFlash == true || isMedia == true || isFile == true || isMap == true || (!"".equals(source_text.trim()) && !"".equals(source_trimSpace))) {

                    topic.setIp(IpAddress.getClientIpAddress(request));
                    topic.setUserName(username);
                    topic.setIsStaff(true);
                    topic.setContent(validValue);
                } else {
                    error.put("content", "话题内容不能为空");
                }
            } else {
                error.put("content", "话题内容不能为空");
            }
        }
        if (sort != null) {
            if (Verification.isNumeric(sort)) {
                if (sort.length() <= 8) {
                    topic.setSort(Integer.parseInt(sort));
                } else {
                    error.put("sort", "不能超过8位数字");
                }
            } else {
                error.put("sort", "请填写整数");
            }
        } else {
            error.put("sort", "排序不能为空");
        }

        if (error.size() == 0) {
            topicService.saveTopic(topic, topicTagAssociations, null);
            topicManage.delete_cache_markUpdateTopicStatus(topic.getId());//删除 标记修改话题状态
            topicManage.delete_cache_postNumByUserName(topic.getUserName());
            Optional.ofNullable(null != topic.getEssence() && topic.getEssence()).filter(o -> o).ifPresent(o -> topicManage.delete_essenceTop10_cache());
            //更新索引
            topicIndexService.addTopicIndex(new TopicIndex(String.valueOf(topic.getId()), 1));
            Optional.ofNullable(topicTagAssociations).filter(o -> null != topic.getStatus() && topic.getStatus().equals(20)).map(l -> l.stream().map(TopicTagAssociation::getTagId).collect(Collectors.toList())).filter(CollUtil::isNotEmpty).ifPresent(l -> {
                TopicManage.incTwoTagTopicNum(l);
                l.stream().map(tagMap::get).map(Tag::getName).forEach(topicManage::delete_cache_twoTagNum);
            });
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public String edit(ModelMap model, Long topicId, Long[] tagIdArray,
                       String tagName, String title, Boolean allow, Integer status, Boolean essence,
                       String content, String sort,
                       HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<Long> tagId = Arrays.stream(tagIdArray).distinct().collect(Collectors.toList());
        Topic topic = null;
        List<String> imageNameList = null;
        boolean isImage = false;//是否含有图片
        List<String> flashNameList = null;
        boolean isFlash = false;//是否含有Flash
        List<String> mediaNameList = null;
        boolean isMedia = false;//是否含有音视频
        List<String> fileNameList = null;
        boolean isFile = false;//是否含有文件
        boolean isMap = false;//是否含有地图
        Map<String, String> error = new HashMap<String, String>();

        //旧状态
        Integer old_status = -1;

        if (status == null) {
            error.put("status", "话题状态不能为空");
        }

        String old_content = "";
        boolean oldEssence = false;
        if (topicId != null && topicId > 0L) {
            topic = topicService.findById(topicId);
            if (topic != null && error.size() == 0) {
                oldEssence = topic.getEssence();
                old_status = topic.getStatus();
                topic.setTagId(0L);
                topic.setTagName(tagName);
                topic.setAllow(allow);
                topic.setStatus(status);
                topic.setEssence(essence);

                final String finalUsername = Optional.ofNullable(SecurityContextHolder.getContext()).map(SecurityContext::getAuthentication).map(Authentication::getPrincipal).filter(o -> o instanceof SysUsers).map(o -> ((SysUsers) o).getUserAccount()).orElse("");
                List<TopicTagAssociation> topicTagAssociations = tagId.stream().filter(o -> null != o).map(o -> new TopicTagAssociation(o, finalUsername)).collect(Collectors.toList());
                Map<Long, Tag> tagMap = tagService.findAllTag_cache().stream().collect(Collectors.toMap(o -> o.getId(), o -> o));
                topic.setTagId(topicTagAssociations.stream().map(TopicTagAssociation::getTagId).map(tagMap::get).filter(Objects::nonNull).filter(o -> o.getGrade() == TOPIC_TAG_GRADE_ONE).findAny().map(Tag::getId).orElseThrow(() -> new CustomException(ErrorCode.C_1_0002_0002, "tagId")));

                old_content = topic.getContent();

                if (title != null && !"".equals(title.trim())) {
                    topic.setTitle(title);
                    if (title.length() > 150) {
                        error.put("title", "不能大于150个字符");
                    }
                } else {
                    error.put("title", "标题不能为空");
                }
                if (content != null && !"".equals(content.trim())) {
                    //过滤标签
                    content = textFilterManage.filterTag(request, content);
                    Object[] object = textFilterManage.filterHtml(request, content, "topic", null);

                    String value = (String) object[0];
                    imageNameList = (List<String>) object[1];
                    isImage = (Boolean) object[2];//是否含有图片
                    flashNameList = (List<String>) object[3];
                    isFlash = (Boolean) object[4];//是否含有Flash
                    mediaNameList = (List<String>) object[5];
                    isMedia = (Boolean) object[6];//是否含有音视频
                    fileNameList = (List<String>) object[7];
                    isFile = (Boolean) object[8];//是否含有文件
                    isMap = (Boolean) object[9];//是否含有地图

                    List<UserGrade> userGradeList = userGradeService.findAllGrade();
                    //校正隐藏标签
                    String validValue = textFilterManage.correctionHiddenTag(value, userGradeList);

                    if (topic.getIsStaff()) {//如果是员工
                        //解析隐藏标签
                        Map<Integer, Object> analysisHiddenTagMap = textFilterManage.analysisHiddenTag(validValue);
                        for (Map.Entry<Integer, Object> entry : analysisHiddenTagMap.entrySet()) {
                            //管理员账户不能发表'余额购买'话题
                            if (entry.getKey().equals(HideTagType.AMOUNT.getName())) {
                                error.put("content", "管理员账户不能发表'余额购买'话题");
                                break;
                            }
                        }
                    }

                    //删除隐藏标签
                    String new_content = textFilterManage.deleteHiddenTag(value);

                    //不含标签内容
                    String text = textFilterManage.filterText(textFilterManage.specifyHtmlTagToText(new_content));

                    //清除空格&nbsp;
                    String trimSpace = cms.utils.StringUtil.replaceSpace(text).trim();

                    //摘要
                    if (trimSpace != null && !"".equals(trimSpace)) {
                        if (trimSpace.length() > 180) {
                            topic.setSummary(trimSpace.substring(0, 180) + "..");
                        } else {
                            topic.setSummary(trimSpace + "..");
                        }
                    }

                    //不含标签内容
                    String source_text = textFilterManage.filterText(content);
                    //清除空格&nbsp;
                    String source_trimSpace = cms.utils.StringUtil.replaceSpace(source_text).trim();
                    if (isImage == true || isFlash == true || isMedia == true || isFile == true || isMap == true || (!"".equals(source_text.trim()) && !"".equals(source_trimSpace))) {
                        topic.setContent(validValue);
                    } else {
                        error.put("content", "话题内容不能为空");
                    }
                } else {
                    error.put("content", "话题内容不能为空");
                }

                if (sort != null) {
                    if (Verification.isNumeric(sort)) {
                        if (sort.length() <= 8) {
                            topic.setSort(Integer.parseInt(sort));
                        } else {
                            error.put("sort", "不能超过8位数字");
                        }
                    } else {
                        error.put("sort", "请填写整数");
                    }
                } else {
                    error.put("sort", "排序不能为空");
                }


                if (error.size() == 0) {
                    if (null == TopicFormService.topicUpdateLock.putIfAbsent(topic.getId(), Boolean.FALSE)) {
                        try {
                            List<Long> oldTagIds = topicTagAssociationService.findByTopicId(topic.getId()).stream().map(tagMap::get).filter(Objects::nonNull).filter(o -> o.getGrade() == TOPIC_TAG_GRADE_TWO).map(Tag::getId).collect(Collectors.toList());
                            List<Long> newTagIds = Arrays.asList(tagId).stream().map(tagMap::get).filter(Objects::nonNull).filter(o -> o.getGrade() == TOPIC_TAG_GRADE_TWO).map(Tag::getId).collect(Collectors.toList());
                            topic.setLastUpdateTime(new Date());//最后修改时间
                            int i = topicService.updateTopic(topic, topicTagAssociations);
                            //更新索引
                            topicIndexService.addTopicIndex(new TopicIndex(String.valueOf(topic.getId()), 2));

                            if (i > 0 && !old_status.equals(status)) {
                                User user = userManage.query_cache_findUserByUserName(topic.getUserName());
                                if (user != null) {
                                    //修改用户动态话题状态
                                    userService.updateUserDynamicTopicStatus(user.getId(), topic.getUserName(), topic.getId(), topic.getStatus());
                                }

                            }

                            //删除缓存
                            topicManage.deleteTopicCache(topic.getId());//删除话题缓存
                            topicManage.delete_cache_analysisHiddenTag(topic.getId());//删除解析隐藏标签缓存
                            topicManage.delete_cache_analysisFullFileName(topic.getId());//删除 解析上传的文件完整路径名称缓存
                            topicManage.delete_cache_markUpdateTopicStatus(topic.getId());//删除 标记修改话题状态
                            Optional.ofNullable(oldEssence || (null != topic.getEssence() && topic.getEssence())).ifPresent(o -> topicManage.delete_essenceTop10_cache());

                            ossFileChangeService.fileChange(old_content, "topic", imageNameList, flashNameList, mediaNameList, fileNameList);
                            Optional.ofNullable(topic.getStatus()).filter(o -> o.equals(20)).ifPresent(o -> {
                                TopicManage.incDecTwoTagTopicNum(oldTagIds, newTagIds);
                                oldTagIds.stream().map(tagMap::get).map(Tag::getName).forEach(topicManage::delete_cache_twoTagNum);
                                newTagIds.stream().map(tagMap::get).map(Tag::getName).forEach(topicManage::delete_cache_twoTagNum);
                            });
                        } finally {
                            TopicFormService.topicUpdateLock.remove(topic.getId());
                        }
                    } else {
                        error.put("topic", "话题正在修改,请稍后再试");
                    }
                }
            } else {
                error.put("topic", "话题不存在");
            }
        } else {
            error.put("topic", "Id不存在");
        }

        if (error.size() == 0) {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, null));
        }
        return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, error));
    }
}
