package cms.service.operation;

import cms.bean.operation.OperationTopic;
import cms.service.besa.DaoSupport;
import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import javax.persistence.Query;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/30 15:47
 */
@Service
@Slf4j
public class OperationTopicService extends DaoSupport<OperationTopic> {

    @Cacheable(value = "api_sponsor_operation_topic_cache", key = "'getSponsorOperationTopic_'+#operationId")
    public Optional<OperationTopic> getSponsorByOperationId(Long operationId) {
        Query query = em.createQuery("select o from OperationTopic o where o.deleted = false and o.sponsor = true and o.operationId=?1  ")
                .setParameter(1, operationId);
        return Optional.ofNullable(query.getResultList()).filter(CollUtil::isNotEmpty).map(l -> (OperationTopic) l.get(0));
    }

    @Caching(evict = {@CacheEvict(value = "api_sponsor_operation_topic_cache", key = "'getSponsorOperationTopic_'+#operationId"),
            @CacheEvict(value = "api_topic_sys_config_cache", key = "'getTopicAndSys_'+#operationId")
    })
    public void add(long operationId, long topicId, boolean sponsor) {
        OperationTopic operationTopic = new OperationTopic();
        operationTopic.setOperationId(operationId);
        operationTopic.setTopicId(topicId);
        operationTopic.setSponsor(sponsor);
        this.save(operationTopic);
    }

    @Caching(evict = {@CacheEvict(value = "api_sponsor_operation_topic_cache", key = "'getSponsorOperationTopic_'+#operationId"),
            @CacheEvict(value = "api_topic_sys_config_cache", key = "'getTopicAndSys_'+#operationId")
    })
    public Integer delByOperationId(Long operationId, Boolean sponsor) {
        Query query = em.createQuery("update OperationTopic o set o.deleted=true where  o.operationId=:operationId and o.sponsor = :sponsor")
                .setParameter("operationId", operationId)
                .setParameter("sponsor", sponsor);
        int i = query.executeUpdate();
        return i;
    }

    public OperationTopic add(Long operationId, Long topicId) {
        return this.add(OperationTopic.build(operationId, topicId));
    }

    public OperationTopic add(OperationTopic operationTopic) {
        this.save(operationTopic);
        return operationTopic;
    }
}
