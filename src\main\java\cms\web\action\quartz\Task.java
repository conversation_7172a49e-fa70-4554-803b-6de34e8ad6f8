package cms.web.action.quartz;

import cms.service.keywordDict.TextFilterKeywordService;
import cms.web.action.fileSystem.FileManage;
import cms.web.action.lucene.QuestionIndexManage;
import cms.web.action.lucene.TopicIndexManage;
import cms.web.action.template.LayoutManage;
import cms.web.action.thumbnail.ThumbnailManage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static cms.constant.Constant.*;

/**
 * 定时任务类
 */
@Component("task")
@Slf4j
public class Task {

    @Resource
    FileManage fileManage;

    @Resource
    ThumbnailManage thumbnailManage;
    @Resource
    TopicIndexManage topicIndexManage;
    @Resource
    QuestionIndexManage questionIndexManage;
    @Resource
    LayoutManage layoutManage;
    @Autowired
    private TextFilterKeywordService textFilterKeywordService;

    /**
     * 话题全文索引
     */
    @Async("taskExecutor_topicIndexManage_topicIndex")
    @Scheduled(cron = "0 0/1 * * * ?")//每隔1分钟执行一次
    public void topicIndex() {
        try {
            if (TOPIC_INDEX_SH.tryAcquire(1, UPDATE_TOPIC_INDEX_SECONDS, TimeUnit.SECONDS)) {
                try {
                    topicIndexManage.updateTopicIndex();
                } finally {
                    TOPIC_INDEX_SH.release();
                }
            }
        } catch (InterruptedException e) {
            log.error("topicIndex job tryAcquire Interrupted!");
        }
    }

    /**
     * 问题全文索引
     */
    @Async("taskExecutor_questionIndexManage_questionIndex")
    @Scheduled(cron = "0 0/1 * * * ?")//每隔1分钟执行一次
    public void questionIndex() {
        try {
            if (QUESTION_INDEX_SH.tryAcquire(1, UPDATE_QUESTION_INDEX_SECONDS, TimeUnit.SECONDS)) {
                try {
                    questionIndexManage.updateQuestionIndex();
                } finally {
                    QUESTION_INDEX_SH.release();
                }
            }
        } catch (InterruptedException e) {
            log.error("questionIndex job tryAcquire Interrupted!");
        }
    }

    /**
     * 处理缩略图
     */
    @Async("taskExecutor_thumbnailManage_treatmentThumbnail")
    @Scheduled(cron = "0 0/10 * * * ?")//每隔10分钟运行一次
    public void treatmentThumbnail() {
        thumbnailManage.treatmentThumbnail();
    }

    /**
     * 删除无效的上传临时文件
     */
    @Async("taskExecutor_fileManage_deleteInvalidFile")
    @Scheduled(cron = "0 50 0/2 * * ?")//每隔两小时的50分运行一次
    public void deleteInvalidFile() {
        fileManage.deleteInvalidFile();
    }

    /**
     * 定时处理布局路径
     * 异步任务配置cms.web.action.AsyncConfig.java
     */
    @Async("taskExecutor_layoutManage_timerProcessLayoutUrl")
    @Scheduled(cron = "0/3 * * * * ?")//每隔3秒执行一次
    public void timerProcessLayoutUrl() {
        layoutManage.timerProcessLayoutUrl();
    }

    @Async("taskExecutor_keywordDicManage_runFilterKeyWord")
    @Scheduled(cron = "0 0 0 * * ?")//0点执行
    public void runFilterKeyWord() {
        try {
            textFilterKeywordService.runFilterKeyWord();
        } catch (Exception e) {
            log.error("task runFilterKeyWord error!", e);
        }
    }
}
