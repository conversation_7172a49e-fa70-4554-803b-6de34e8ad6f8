package cms.web.action.user;

import cms.bean.RequestResult;
import cms.bean.ResultCode;
import cms.constant.ErrorCode;
import cms.service.messageSource.ErrorMessageService;
import cms.service.user.impl.TopDonUserService;
import cms.utils.JsonUtils;
import cms.web.action.common.OAuthManage;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/11 11:51
 */
@Controller
@RequestMapping("/topDonUser/manage")
public class TopDonUserManagerAction {

    @Autowired
    private TopDonUserService topDonUserService;
    @Autowired
    private OAuthManage oAuthManage;
    @Autowired
    private ErrorMessageService errorMessageService;

    private static Map<String, String> lockMap = Maps.newConcurrentMap();

    /**
     * @param model
     * @param email
     * @param type  1注册.2修改密码.4忘记密码
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/verificationCode", method = RequestMethod.POST)
    public String verificationCode(ModelMap model, String email, String type) throws Exception {
        String key = String.format("%s_%s", email, type);
        if (null == oAuthManage.getEmailMsg(email, type) && null == lockMap.putIfAbsent(key, key)) {
            try {
                topDonUserService.verificationCode(email, type);
                oAuthManage.addEmailMsg(email, type);
                return JsonUtils.toJSONString(new RequestResult(ResultCode.SUCCESS, ""));
            } finally {
                lockMap.remove(key);
            }
        } else {
            return JsonUtils.toJSONString(new RequestResult(ResultCode.FAILURE, errorMessageService.getMessage(ErrorCode.C_1_0001_0011)));
        }
    }
}
