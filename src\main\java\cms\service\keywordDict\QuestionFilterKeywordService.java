package cms.service.keywordDict;

import cms.bean.question.Answer;
import cms.bean.question.AnswerReply;
import cms.bean.question.AppendQuestionItem;
import cms.bean.question.Question;
import cms.bean.setting.EditorTag;
import cms.handle.MsgException;
import cms.service.question.AnswerService;
import cms.service.question.QuestionService;
import cms.utils.JsonUtils;
import cms.web.action.TextFilterManage;
import cms.web.action.filterWord.SensitiveWordFilterManage;
import cms.web.action.question.AnswerManage;
import cms.web.action.question.QuestionManage;
import cms.web.action.setting.SettingManage;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/28 11:29
 */
@Service
@Slf4j
public class QuestionFilterKeywordService {

    @Resource
    QuestionService questionService;
    @Autowired
    private AnswerService answerService;
    @Resource
    QuestionManage questionManage;
    @Resource
    AnswerManage answerManage;
    @Resource
    SensitiveWordFilterManage sensitiveWordFilterManage;
    @Resource
    TextFilterManage textFilterManage;
    @Autowired
    private SettingManage settingManage;

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void filterKeyWord(Long id, String wordReplace, Date date) {
        Question question = Optional.ofNullable(questionService.findById(id)).orElseThrow(() -> new MsgException(String.format("questionId %s not find question", id)));
        if (Optional.ofNullable(question.getLastUpdateTime()).orElse(question.getPostTime()).compareTo(date) > 0) {
            return;
        }

        String title = sensitiveWordFilterManage.filterSensitiveWord(question.getTitle(), wordReplace);
        //过滤标签
        EditorTag editorTag = settingManage.readQuestionEditorTag();
        String content = textFilterManage.filterTag(question.getContent(), editorTag);
        Object[] object = textFilterManage.filterHtml(null, content, "question", editorTag);
        String value = (String) object[0];

        //不含标签内容
        String text = Optional.ofNullable(value).filter(Strings::isNotEmpty).map(textFilterManage::specifyHtmlTagToText).map(textFilterManage::filterText).orElse("");
        String trimSpace = Optional.ofNullable(cms.utils.StringUtil.replaceSpace(text)).filter(Strings::isNotEmpty).map(String::trim).map(o -> sensitiveWordFilterManage.filterSensitiveWord(o, wordReplace)).orElse("");
        value = Optional.ofNullable(value).filter(Strings::isNotEmpty).map(o -> sensitiveWordFilterManage.filterSensitiveWord(o, wordReplace)).orElse("");

        Optional<String> appendContent = Optional.ofNullable(question.getAppendContent()).filter(Strings::isNotEmpty)
                .map(o -> StringUtils.substringBeforeLast(o, ",")).map(o -> JSON.parseArray(o + "]", AppendQuestionItem.class))
                .filter(CollUtil::isNotEmpty).map(o -> this.filterKeyWordAppend(o, wordReplace));

        int i = questionService.updateQuestionByFilterKeyWord(id, question.getLastUpdateTime(), title, question.getContent(), appendContent.orElse("["));
        if (i > 0) {
            //删除问题缓存
            questionManage.delete_cache_findById(question.getId());
        }
    }

    private String filterKeyWordAppend(List<AppendQuestionItem> appendQuestionItems, String wordReplace) {
        appendQuestionItems.stream().forEach(o -> {
            EditorTag editorTag = settingManage.readQuestionEditorTag();
            //过滤标签
            String content = textFilterManage.filterTag(o.getContent(), editorTag);
            Object[] object = textFilterManage.filterHtml(null, content, "question", editorTag);
            String value = (String) object[0];
            Optional.ofNullable(value).filter(Strings::isNotEmpty).map(x -> sensitiveWordFilterManage.filterSensitiveWord(x, wordReplace)).ifPresent(o::setContent);
        });

        return Optional.of(appendQuestionItems).filter(CollUtil::isNotEmpty).map(JsonUtils::toJSONString).map(o -> StringUtils.substringBeforeLast(o, "]") + ",").orElse(null);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void filterKeyWordAnswer(Long id, String wordReplace, Date date) {
        Answer answer = Optional.ofNullable(answerService.findByAnswerId(id)).orElseThrow(() -> new MsgException(String.format("answerId %s not find answer", id)));
        if (Optional.ofNullable(answer.getLastUpdateTime()).orElse(answer.getPostTime()).compareTo(date) > 0) {
            return;
        }

        String content = textFilterManage.filterTag(answer.getContent(), settingManage.readAnswerEditorTag());
        Object[] object = textFilterManage.filterHtml(null, content, "answer", settingManage.readAnswerEditorTag());
        String value = (String) object[0];
        value = Optional.ofNullable(value).filter(Strings::isNotEmpty).map(o -> sensitiveWordFilterManage.filterSensitiveWord(o, wordReplace)).orElse("");
        int i = answerService.updateAnswerByFilterKeyWord(id, answer.getLastUpdateTime(), value);
        if (i > 0) {
            //删除缓存
            answerManage.delete_cache_findByAnswerId(answer.getId());
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void filterKeyWordReply(Long id, String wordReplace, Date date) {
        AnswerReply answerReply = Optional.ofNullable(answerService.findReplyByReplyId(id)).orElseThrow(() -> new MsgException(String.format("answerReplyId %s not find answerReply", id)));
        if (Optional.ofNullable(answerReply.getLastUpdateTime()).orElse(answerReply.getPostTime()).compareTo(date) > 0) {
            return;
        }

        //不含标签内容
        String text = textFilterManage.filterText(answerReply.getContent());
        text = Optional.ofNullable(text).map(o -> sensitiveWordFilterManage.filterSensitiveWord(o, wordReplace)).orElse("");
        int i = answerService.updateReplyByFilterKeyWord(id, answerReply.getLastUpdateTime(), text);
        if (i > 0) {
            //删除缓存
            answerManage.delete_cache_findReplyByReplyId(answerReply.getId());
        }
    }
}
