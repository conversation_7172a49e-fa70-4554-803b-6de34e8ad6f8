package cms.requset.bean;

import cms.constraints.ContainsObject;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16 11:06
 */
@Data
public class UserLogin {

    public final static Integer TYPE_ACCOUNT = 10;
    public final static Integer TYPE_PHONE = 20;

    // 账号
    @NotEmpty
    private String account;
    // 密码
    @NotEmpty
    private String password;
    // 用户类型
    @NotNull
    @ContainsObject(contains = {"10", "20"})
    private Integer type;
    // 手机号
    private String mobile;
    // 记住密码
    private Boolean rememberMe;
    // 跳转URL
    private String jumpUrl;
    private String token;
    private String captchaKey;
    private String captchaValue;
    private String thirdPartyOpenId;

    private boolean code;
}
