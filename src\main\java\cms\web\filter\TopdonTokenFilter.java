package cms.web.filter;

import cms.bean.topdon.PlatUser;
import cms.bean.topdon.TopdonResult;
import cms.bean.user.AccessUser;
import cms.handle.CustomException;
import cms.service.user.UserService;
import cms.service.user.impl.TopDonUserService;
import cms.utils.threadLocal.AccessUserThreadLocal;
import cms.web.action.fileSystem.FileManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;

import javax.annotation.Resource;
import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Optional;

import static cms.constant.Constant.USER_TYPE_LOCAL;
import static cms.constant.ErrorCode.C_1_0001_0020;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/29 15:12
 */
@WebFilter(urlPatterns = "/api/control/*", filterName = "topdonTokenFilter", asyncSupported = true)
@Slf4j
@Order(3)
public class TopdonTokenFilter implements Filter {


    public static final String TOPDON_AUTHORIZATION = "Topdon-Authorization";

    @Autowired
    private TopDonUserService topDonUserService;
    @Autowired
    private UserService userService;
    @Resource
    FileManage fileManage;

    @Override
    public void doFilter(ServletRequest req, ServletResponse res,
                         FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) req;
        initTopDonTokenUser(request);
        chain.doFilter(req, res);
    }

    private void initTopDonTokenUser(HttpServletRequest request) {
        String authorization = Optional.ofNullable(request.getHeader(TOPDON_AUTHORIZATION)).filter(Strings::isNotEmpty).orElseThrow(() -> new CustomException(C_1_0001_0020, "authorization"));
        Optional.ofNullable(topDonUserService.getUserInfoByApp(authorization)).map(TopdonResult::getData).map(PlatUser::getEmail).map(e ->
                Optional.ofNullable(userService.getByEmail(e).orElse(null)).orElseGet(() -> userService.add(e, "", USER_TYPE_LOCAL))
        ).ifPresent(user -> {
            AccessUserThreadLocal.set(new AccessUser(user.getId(), user.getUserName(), user.getAccount(), user.getNickname(), user.getAvatarPath(fileManage.fileOssAddress()), user.getAvatarName(), user.getSecurityDigest(), Boolean.FALSE, ""));
        });
    }
}
