package cms.constraints;

import cms.constraints.validator.ContainsObjectValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16 11:23
 */
@Documented
@Constraint(validatedBy = {
        ContainsObjectValidator.class
})
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.TYPE_USE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ContainsObject {

    String[] contains() default {};

    String message() default "必须是不正确!";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
