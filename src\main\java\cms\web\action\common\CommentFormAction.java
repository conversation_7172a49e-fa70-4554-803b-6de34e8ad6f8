package cms.web.action.common;


import cms.bean.RequestResult;
import cms.bean.setting.EditorTag;
import cms.bean.setting.SystemSetting;
import cms.bean.user.AccessUser;
import cms.bean.user.User;
import cms.constant.ErrorCode;
import cms.constant.FileConstant;
import cms.service.aliyun.OssFileChangeService;
import cms.service.setting.SettingService;
import cms.service.topic.impl.CommentFormService;
import cms.utils.threadLocal.AccessUserThreadLocal;
import cms.web.action.setting.SettingManage;
import cms.web.action.user.UserManage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;

/**
 * 评论接收表单
 */
@Controller
@RequestMapping("user/control/comment")
public class CommentFormAction {

    @Resource
    SettingManage settingManage;
    @Resource
    SettingService settingService;
    @Resource
    UserManage userManage;
    @Autowired
    private CommentFormService commentFormService;
    @Autowired
    private OssFileChangeService ossFileChangeService;

    /**
     * 评论   添加
     *
     * @param model
     * @param topicId  话题Id
     * @param content  评论内容
     * @param jumpUrl  跳转地址
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public String add(ModelMap model, Long topicId, String content,
                      String token, String captchaKey, String captchaValue, String jumpUrl,
                      RedirectAttributes redirectAttrs,
                      HttpServletRequest request, HttpServletResponse response) throws Exception {
        return commentFormService.add(model, topicId, content, token, captchaKey, captchaValue, jumpUrl, redirectAttrs, request, response);
    }


    /**
     * 评论  图片上传
     *
     * @param model
     * @param topicId  话题Id
     * @param fileName 文件名称 预签名时有值
     * @param file
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/uploadImage", method = RequestMethod.POST)
    @ResponseBody//方式来做ajax,直接返回字符串
    public String uploadImage(ModelMap model, Long topicId, String fileName,
                              MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Optional<EditorTag> editorTagOptional = Optional.ofNullable(settingManage.readEditorTag());
        Optional<AccessUser> accessUser = Optional.ofNullable(AccessUserThreadLocal.get());
        String userName = accessUser.map(AccessUser::getUserName).orElse("");
        Optional<User> user = Optional.ofNullable(userManage.query_cache_findUserByUserName(userName));
        return ossFileChangeService.upload(errorMessageService -> {
            Optional<SystemSetting> systemSetting = Optional.ofNullable(settingService.findSystemSetting_cache());
            // 只读模式不允许提交数据
            systemSetting.map(SystemSetting::getCloseSite).filter(o -> !o.equals(2)).orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0001));
            //如果全局不允许提交评论,如果实名用户才允许提交话题
            systemSetting.filter(o -> o.isAllowComment()).filter(o -> o.isRealNameUserAllowComment() == Boolean.FALSE || user.map(User::isRealNameAuthentication).orElse(Boolean.TRUE)).orElseThrow(() -> errorMessageService.getExcMessage(ErrorCode.C_2_0001_0014));
        }, file, editorTagOptional, FileConstant.FILE_TYPE_IMG, accessUser.map(o -> String.format("b%s", o.getUserId())), "comment", Optional.ofNullable(topicId).map(String::valueOf));
    }


    /**
     * 引用  添加
     *
     * @param model
     * @param commentId 评论Id
     */
    @RequestMapping(value = "/addQuote", method = RequestMethod.POST)
    public String addQuote(ModelMap model, Long commentId, String content,
                           String token, String captchaKey, String captchaValue, String jumpUrl,
                           RedirectAttributes redirectAttrs,
                           HttpServletRequest request, HttpServletResponse response) throws Exception {
        return commentFormService.addQuote(model, commentId, content, token, captchaKey, captchaValue, jumpUrl, redirectAttrs, request, response);
    }


    /**
     * 评论  修改
     *
     * @param model
     * @param commentId 评论Id
     * @param content   内容
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    public String edit(ModelMap model, Long commentId, String content,
                       String token, String captchaKey, String captchaValue, String jumpUrl,
                       RedirectAttributes redirectAttrs,
                       HttpServletRequest request, HttpServletResponse response) throws Exception {
        return commentFormService.edit(model, commentId, content, token, captchaKey, captchaValue, jumpUrl, redirectAttrs, request, response);
    }


    /**
     * 评论  删除
     *
     * @param model
     * @param commentId 评论Id
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public String delete(ModelMap model, Long commentId,
                         String token, String jumpUrl,
                         RedirectAttributes redirectAttrs,
                         HttpServletRequest request, HttpServletResponse response) throws Exception {
        return commentFormService.delete(model, commentId, token, jumpUrl, redirectAttrs, request, response);
    }


    /**
     * 回复  添加
     *
     * @param model
     * @param commentId    评论Id
     * @param content
     * @param token
     * @param captchaKey
     * @param captchaValue
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/addReply", method = RequestMethod.POST)
    public String addReply(ModelMap model, Long commentId, String content,
                           String token, String captchaKey, String captchaValue, String jumpUrl,
                           RedirectAttributes redirectAttrs,
                           HttpServletRequest request, HttpServletResponse response) throws Exception {
        return commentFormService.addReply(model, commentId, content, token, captchaKey, captchaValue, jumpUrl, redirectAttrs, request, response);
    }


    /**
     * 评论回复  修改
     *
     * @param model
     * @param replyId  回复Id
     * @param content  内容
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/editReply", method = RequestMethod.POST)
    public String editReply(ModelMap model, Long replyId, String content,
                            String token, String captchaKey, String captchaValue, String jumpUrl,
                            RedirectAttributes redirectAttrs,
                            HttpServletRequest request, HttpServletResponse response) throws Exception {
        return commentFormService.editReply(model, replyId, content, token, captchaKey, captchaValue, jumpUrl, redirectAttrs, request, response);
    }

    /**
     * 评论回复  删除
     *
     * @param model
     * @param replyId  回复Id
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/deleteReply", method = RequestMethod.POST)
    public String deleteReply(ModelMap model, Long replyId,
                              String token, String jumpUrl,
                              RedirectAttributes redirectAttrs,
                              HttpServletRequest request, HttpServletResponse response) throws Exception {
        return commentFormService.deleteReply(model, replyId, token, jumpUrl, redirectAttrs, request, response);
    }

    @ResponseBody
    @RequestMapping(value = "/like", method = RequestMethod.POST)
    public RequestResult like(@RequestParam(name = "commentId", required = true) Long commentId, String token, HttpServletRequest request) {
        commentFormService.likeLock(commentId, token, request);
        return RequestResult.ok("");
    }

    @ResponseBody
    @RequestMapping(value = "/deleteLike", method = RequestMethod.POST)
    public RequestResult deleteLike(@RequestParam(name = "commentId", required = true) Long commentId, String token, HttpServletRequest request) {
        commentFormService.deleteLikeLock(commentId, token, request);
        return RequestResult.ok("");
    }

    @ResponseBody
    @RequestMapping(value = "/likeReply", method = RequestMethod.POST)
    public RequestResult likeReply(@RequestParam(name = "replyId", required = true) Long replyId, String token, HttpServletRequest request) {
        commentFormService.likeReplyLock(replyId, token, request);
        return RequestResult.ok("");
    }

    @ResponseBody
    @RequestMapping(value = "/deleteReplyLike", method = RequestMethod.POST)
    public RequestResult deleteReplyLike(@RequestParam(name = "replyId", required = true) Long replyId, String token, HttpServletRequest request) {
        commentFormService.deleteReplyLikeLock(replyId, token, request);
        return RequestResult.ok("");
    }
}
