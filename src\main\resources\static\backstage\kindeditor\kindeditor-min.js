(function(g,h){function u(a){function b(){$("#_kindEditor_msg").remove()}$("#_kindEditor_msg").remove();a='<div id="_kindEditor_msg" class="ke-kindEditor_msg" ><p>'+a+"</p></div>";$("body").append(a);v($("#_kindEditor_msg"));setTimeout(b,4E3)}function v(a){var b=$(g).width(),c=$(g).height(),d=$(document).scrollTop(),e=(c-a.height())/2+d;a.css({top:e+"px"});$(g).resize(function(){b=$(g).width();c=$(g).height();d=$(document).scrollTop();e=(c-a.height())/2+d;a.css({top:e+"px"})});$(g).scroll(function(){b=
$(g).width();c=$(g).height();d=$(document).scrollTop();e=(c-a.height())/2+d;a.css({top:e+"px"})})}function m(a){if(!a)return false;return Object.prototype.toString.call(a)==="[object Array]"}function t(a){if(!a)return false;return Object.prototype.toString.call(a)==="[object Function]"}function o(a,b){for(var c=0,d=b.length;c<d;c++)if(a===b[c])return c;return-1}function l(a,b){if(m(a))for(var c=0,d=a.length;c<d;c++){if(b.call(a[c],c,a[c])===false)break}else for(c in a)if(a.hasOwnProperty(c))if(b.call(a[c],
c,a[c])===false)break}function q(a){return a.replace(/(?:^[ \t\n\r]+)|(?:[ \t\n\r]+$)/g,"")}function p(a,b,c){c=c===h?",":c;return(c+b+c).indexOf(c+a+c)>=0}function r(a,b){b=b||"px";return a&&/^-?\d+(?:\.\d+)?$/.test(a)?a+b:a}function z(a){var b;return a&&(b=/(\d+)/.exec(a))?parseInt(b[1],10):0}function H(a){return a.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")}function C(a){return a.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&amp;/g,
"&")}function D(a){var b=a.split("-");a="";l(b,function(c,d){a+=c>0?d.charAt(0).toUpperCase()+d.substr(1):d});return a}function Q(a){function b(c){c=parseInt(c,10).toString(16).toUpperCase();return c.length>1?c:"0"+c}return a.replace(/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/ig,function(c,d,e,f){return"#"+b(d)+b(e)+b(f)})}function J(a,b){b=b===h?",":b;var c={};a=m(a)?a:a.split(b);var d;l(a,function(e,f){if(d=/^(\d+)\.\.(\d+)$/.exec(f))for(e=parseInt(d[1],10);e<=parseInt(d[2],10);e++)c[e.toString()]=
true;else c[f]=true});return c}function da(a,b){return Array.prototype.slice.call(a,b||0)}function B(a,b){return a===h?b:a}function M(a){return!a||/[<>"]/.test(a)}function T(a,b){return a.indexOf("?")>=0?a+"&"+b:a+"?"+b}function G(a,b,c){if(!c){c=b;b=null}var d;if(b){var e=function(){};e.prototype=b.prototype;d=new e;l(c,function(f,i){d[f]=i})}else d=c;d.constructor=a;a.prototype=d;a.parent=b?b.prototype:null}function V(a){var b;if(b=/\{[\s\S]*\}|\[[\s\S]*\]/.exec(a))a=b[0];b=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g;
b.lastIndex=0;if(b.test(a))a=a.replace(b,function(c){return"\\u"+("0000"+c.charCodeAt(0).toString(16)).slice(-4)});if(/^[\],:{}\s]*$/.test(a.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"")))return eval("("+a+")");throw"JSON parse error";}function Z(){for(var a=document.getElementsByTagName("script"),b,c=0,d=a.length;c<d;c++){b=a[c].src||"";if(/kindeditor[\w\-\.]*\.js/.test(b))return b.substring(0,
b.lastIndexOf("/")+1)}return""}function I(){for(var a=document.getElementsByTagName("base"),b=0,c=a.length;b<c;b++){var d=a[b].href;if(d)return d}return""}function P(a,b,c){if(a.addEventListener)a.addEventListener(b,c,Nb);else a.attachEvent&&a.attachEvent("on"+b,c)}function aa(a,b,c){if(a.removeEventListener)a.removeEventListener(b,c,Nb);else a.detachEvent&&a.detachEvent("on"+b,c)}function R(a,b){this.init(a,b)}function W(a){return a[hb]||null}function X(a){a[hb]=++Ob;return Ob}function ha(a){try{delete a[hb]}catch(b){a.removeAttribute&&
a.removeAttribute(hb)}}function fa(a,b,c){if(b.indexOf(",")>=0)l(b.split(","),function(){fa(a,this,c)});else{var d=W(a);d||(d=X(a));if(pa[d]===h)pa[d]={};var e=pa[d][b];if(e&&e.length>0)aa(a,b,e[0]);else{pa[d][b]=[];pa[d].el=a}e=pa[d][b];if(e.length===0)e[0]=function(f){var i=f?new R(a,f):h;l(e,function(k,n){k>0&&n&&n.call(a,i)})};o(c,e)<0&&e.push(c);P(a,b,e[0])}}function ma(a,b,c){if(b&&b.indexOf(",")>=0)l(b.split(","),function(){ma(a,this,c)});else{var d=W(a);if(d)if(b===h){if(d in pa){l(pa[d],
function(i,k){i!="el"&&k.length>0&&aa(a,i,k[0])});delete pa[d];ha(a)}}else if(pa[d]){var e=pa[d][b];if(e&&e.length>0){if(c===h){aa(a,b,e[0]);delete pa[d][b]}else{l(e,function(i,k){i>0&&k===c&&e.splice(i,1)});if(e.length==1){aa(a,b,e[0]);delete pa[d][b]}}var f=0;l(pa[d],function(){f++});if(f<2){delete pa[d];ha(a)}}}}}function ra(a,b){if(b.indexOf(",")>=0)l(b.split(","),function(){ra(a,this)});else{var c=W(a);if(c){b=pa[c][b];pa[c]&&b&&b.length>0&&b[0]()}}}function ua(a,b,c){b=/^\d{2,}$/.test(b)?b:
b.toUpperCase().charCodeAt(0);fa(a,"keydown",function(d){if(d.ctrlKey&&d.which==b&&!d.shiftKey&&!d.altKey){c.call(a);d.stop()}})}function va(a){function b(){if(!e){e=true;a(KindEditor);Pb=true}}function c(){if(!e){try{document.documentElement.doScroll("left")}catch(k){setTimeout(c,100);return}b()}}function d(){document.readyState==="complete"&&b()}if(Pb)a(KindEditor);else{var e=false;if(document.addEventListener)fa(document,"DOMContentLoaded",b);else if(document.attachEvent){fa(document,"readystatechange",
d);var f=false;try{f=g.frameElement==null}catch(i){}document.documentElement.doScroll&&f&&c()}fa(g,"load",b)}}function wa(a){a=a.replace(/&quot;/g,'"');for(var b={},c=/\s*([\w\-]+)\s*:([^;]*)(;|$)/g,d;d=c.exec(a);){var e=q(d[1].toLowerCase());d=q(Q(d[2]));b[e]=d}return b}function ba(a){for(var b={},c=/\s+(?:([\w\-:]+)|(?:([\w\-:]+)=([^\s"'<>]+))|(?:([\w\-:"]+)="([^"]*)")|(?:([\w\-:"]+)='([^']*)'))(?=(?:\s|\/|>)+)/g,d;d=c.exec(a);){var e=(d[1]||d[2]||d[4]||d[6]).toLowerCase();b[e]=(d[2]?d[3]:d[4]?
d[5]:d[7])||""}return b}function sa(a,b){return a=/\s+class\s*=/.test(a)?a.replace(/(\s+class=["']?)([^"']*)(["']?[\s>])/,function(c,d,e,f){return(" "+e+" ").indexOf(" "+b+" ")<0?e===""?d+b+f:d+e+" "+b+f:c}):a.substr(0,a.length-1)+' class="'+b+'">'}function Ma(a){var b="";l(wa(a),function(c,d){b+=c+":"+d+";"});return b}function Na(a,b,c,d){function e(n){n=n.split("/");for(var s=[],w=0,A=n.length;w<A;w++){var y=n[w];if(y=="..")s.length>0&&s.pop();else y!==""&&y!="."&&s.push(y)}return"/"+s.join("/")}
function f(n,s){if(a.substr(0,n.length)===n){for(var w=[],A=0;A<s;A++)w.push("..");s=".";if(w.length>0)s+="/"+w.join("/");if(d=="/")s+="/";return s+a.substr(n.length)}else if(k=/^(.*)\//.exec(n))return f(k[1],++s)}b=B(b,"").toLowerCase();if(a.substr(0,5)!="data:")a=a.replace(/([^:])\/\//g,"$1/");if(o(b,["absolute","relative","domain"])<0)return a;c=c||location.protocol+"//"+location.host;if(d===h){var i=location.pathname.match(/^(\/.*)\//);d=i?i[1]:""}var k;if(k=/^(\w+:\/\/[^\/]*)/.exec(a)){if(k[1]!==
c)return a}else if(/^\w+:/.test(a))return a;if(/^\//.test(a))a=c+e(a.substr(1));else/^\w+:\/\//.test(a)||(a=c+e(d+"/"+a));if(b==="relative")a=f(c+d,0).substr(2);else if(b==="absolute")if(a.substr(0,c.length)===c)a=a.substr(c.length);return a}function Aa(a,b,c,d,e){if(a==null)a="";c=c||"";d=B(d,false);e=B(e,"\t");var f="xx-small,x-small,small,medium,large,x-large,xx-large".split(",");a=a.replace(/(<(?:pre|pre\s[^>]*)>)([\s\S]*?)(<\/pre>)/ig,function(n,s,w,A){return s+w.replace(/<(?:br|br\s[^>]*)>/ig,
"\n")+A});a=a.replace(/<(?:br|br\s[^>]*)\s*\/?>\s*<\/p>/ig,"</p>");a=a.replace(/(<(?:p|p\s[^>]*)>)\s*(<\/p>)/ig,"$1<br />$2");a=a.replace(/\u200B/g,"");a=a.replace(/\u00A9/g,"&copy;");a=a.replace(/\u00AE/g,"&reg;");a=a.replace(/\u2003/g,"&emsp;");a=a.replace(/\u3000/g,"&emsp;");a=a.replace(/<[^>]+/g,function(n){return n.replace(/\s+/g," ")});var i={};if(b){l(b,function(n,s){n=n.split(",");for(var w=0,A=n.length;w<A;w++)i[n[w]]=J(s)});i.script||(a=a.replace(/(<(?:script|script\s[^>]*)>)([\s\S]*?)(<\/script>)/ig,
""));i.style||(a=a.replace(/(<(?:style|style\s[^>]*)>)([\s\S]*?)(<\/style>)/ig,""))}var k=[];a=a.replace(/(\s*)<(\/)?([\w\-:]+)((?:\s+|(?:\s+[\w\-:]+)|(?:\s+[\w\-:]+=[^\s"'<>]+)|(?:\s+[\w\-:"]+="[^"]*")|(?:\s+[\w\-:"]+='[^']*'))*)(\/)?>(\s*)/g,function(n,s,w,A,y,K,x){s=s||"";w=w||"";var L=A.toLowerCase(),ca=y||"";A=K?" "+K:"";x=x||"";if(b&&!i[L])return"";if(A===""&&Qb[L])A=" /";if(Rb[L]){if(s)s=" ";if(x)x=" "}if(rb[L])if(w)x="\n";else s="\n";if(d&&L=="br")x="\n";if(Sb[L]&&!rb[L])if(d){w&&k.length>
0&&k[k.length-1]===L?k.pop():k.push(L);x=s="\n";y=0;for(K=w?k.length:k.length-1;y<K;y++){s+=e;w||(x+=e)}if(A)k.pop();else w||(x+=e)}else s=x="";if(ca!==""){var F=ba(n);if(L==="font"){var U={},ga="";l(F,function(E,N){if(E==="color"){U.color=N;delete F[E]}if(E==="size"){U["font-size"]=f[parseInt(N,10)-1]||"";delete F[E]}if(E==="face"){U["font-family"]=N;delete F[E]}if(E==="style")ga=N});if(ga&&!/;$/.test(ga))ga+=";";l(U,function(E,N){if(N!==""){if(/\s/.test(N))N="'"+N+"'";ga+=E+":"+N+";"}});F.style=
ga}l(F,function(E,N){if(vc[E])F[E]=E;if(o(E,["src","href"])>=0)F[E]=Na(N,c);if(b&&E!=="style"&&!i[L]["*"]&&!i[L][E]||L==="body"&&E==="contenteditable"||/^kindeditor_\d+$/.test(E))delete F[E];if(E==="style"&&N!==""){var ja=wa(N);l(ja,function(S){b&&!i[L].style&&!i[L]["."+S]&&delete ja[S]});var oa="";l(ja,function(S,Y){oa+=S+":"+Y+";"});F.style=oa}});ca="";l(F,function(E,N){if(!(E==="style"&&N==="")){N=N.replace(/"/g,"&quot;");ca+=" "+E+'="'+N+'"'}})}if(L==="font")L="span";return s+"<"+w+L+ca+A+">"+
x});a=a.replace(/(<(?:pre|pre\s[^>]*)>)([\s\S]*?)(<\/pre>)/ig,function(n,s,w,A){return s+w.replace(/\n/g,'<span id="__kindeditor_pre_newline__">\n')+A});a=a.replace(/\n\s*\n/g,"\n");a=a.replace(/<span id="__kindeditor_pre_newline__">\n/g,"\n");return q(a)}function Tb(a,b){a=a.replace(/<meta[\s\S]*?>/ig,"").replace(/<![\s\S]*?>/ig,"").replace(/<style[^>]*>[\s\S]*?<\/style>/ig,"").replace(/<script[^>]*>[\s\S]*?<\/script>/ig,"").replace(/<w:[^>]+>[\s\S]*?<\/w:[^>]+>/ig,"").replace(/<o:[^>]+>[\s\S]*?<\/o:[^>]+>/ig,
"").replace(/<xml>[\s\S]*?<\/xml>/ig,"").replace(/<(?:table|td)[^>]*>/ig,function(c){return c.replace(/border-bottom:([#\w\s]+)/ig,"border:$1")});return Aa(a,b)}function sb(){return"video/mp4"}function wc(){return"ke-video"}function tb(a){return ba(unescape(a))}function ub(a){var b="<video  ";l(a,function(c,d){b+=c+'="'+d+'" '});b+="/>";return b}function Ub(a,b){var c=b.width,d=b.height,e=b.type||sb(b.src);b=ub(b);var f="";if(/\D/.test(c))f+="width:"+c+";";else if(c>0)f+="width:"+c+"px;";if(/\D/.test(d))f+=
"height:"+d+";";else if(d>0)f+="height:"+d+"px;";a='<img class="'+wc(e)+'" src="'+a+'" ';if(f!=="")a+='style="'+f+'" ';a+='data-ke-tag="'+escape(b)+'" alt="" />';return a}function xc(){return"ke-iframe"}function vb(a){var b="<iframe  ";l(a,function(c,d){b+=c+'="'+d+'" '});b+="></iframe>";return b}function Vb(a,b){var c=b.width,d=b.height,e=b.type||sb(b.src);b=vb(b);var f="";if(/\D/.test(c))f+="width:"+c+";";else if(c>0)f+="width:"+c+"px;";if(/\D/.test(d))f+="height:"+d+";";else if(d>0)f+="height:"+
d+"px;";a='<img class="'+xc(e)+'" src="'+a+'" ';if(f!=="")a+='style="'+f+'" ';a+='data-ke-tag="'+escape(b)+'" alt="" />';return a}function yc(a,b){a=new Function("obj","var p=[],print=function(){p.push.apply(p,arguments);};with(obj){p.push('"+a.replace(/[\r\t\n]/g," ").split("<%").join("\t").replace(/((^|%>)[^\t]*)'/g,"$1\r").replace(/\t=(.*?)%>/g,"',$1,'").split("\t").join("');").split("%>").join("p.push('").split("\r").join("\\'")+"');}return p.join('');");return b?a(b):a}function zc(a,b,c,d,e){var f;
if(g.XMLHttpRequest)try{f=new XMLHttpRequest;f.overrideMimeType("text/html;charset=UTF-8")}catch(i){}else if(g.ActiveXObject)try{f=new ActiveXObject("Microsoft.XMLHTTP")}catch(k){try{f=new ActiveXObject("Msxml2.XMLHttp")}catch(n){try{f=new ActiveXObject("Msxml3.XMLHttp")}catch(s){}}}f.open("POST",b,c);if(e){b=g.sessionStorage.getItem("oauth2Token");if(b!=null){b=JSON.parse(b);f.setRequestHeader("Authorization","Bearer "+b.access_token)}b=j.getCookie("XSRF-TOKEN");f.setRequestHeader("X-XSRF-TOKEN",
b)}f.onreadystatechange=function(){if(f.readyState==4)try{f.status==200?a(f):j.popupMessage(KindEditor.lang("uploadError"))}catch(w){j.popupMessage(KindEditor.lang("sendRequestFailed")+w)}};f.send(d)}function Ac(a,b,c,d,e){var f;if(g.XMLHttpRequest)try{f=new XMLHttpRequest;f.overrideMimeType("text/html;charset=UTF-8")}catch(i){}else if(g.ActiveXObject)try{f=new ActiveXObject("Microsoft.XMLHTTP")}catch(k){try{f=new ActiveXObject("Msxml2.XMLHttp")}catch(n){try{f=new ActiveXObject("Msxml3.XMLHttp")}catch(s){}}}f.open("PUT",
b,c);if(e){b=g.sessionStorage.getItem("oauth2Token");if(b!=null){b=JSON.parse(b);f.setRequestHeader("Authorization","Bearer "+b.access_token)}b=j.getCookie("XSRF-TOKEN");f.setRequestHeader("X-XSRF-TOKEN",b)}f.onreadystatechange=function(){if(f.readyState==4)try{f.status==200?a(f):j.popupMessage(KindEditor.lang("uploadError"))}catch(w){j.popupMessage(KindEditor.lang("sendRequestFailed")+w)}};f.send(d)}function Bc(a){if(!document.cookie)return null;var b=document.cookie.split(";").map(function(c){return c.trim()}).filter(function(c){return c.startsWith(a+
"=")});if(b.length===0)return null;return decodeURIComponent(b[0].split("=")[1])}function ib(a,b){if(a.nodeType==9&&b.nodeType!=9)return true;for(;b=b.parentNode;)if(b==a)return true;return false}function jb(a,b){b=b.toLowerCase();var c=null;if(!Cc&&a.nodeName.toLowerCase()!="script"){var d=a.ownerDocument.createElement("div");d.appendChild(a.cloneNode(false));a=ba(C(d.innerHTML));if(b in a)c=a[b]}else try{c=a.getAttribute(b,2)}catch(e){c=a.getAttribute(b,1)}if(b==="style"&&c!==null)c=Ma(c);return c}
function kb(a,b){function c(S){if(typeof S!="string")return S;return S.replace(/([^\w\-])/g,"\\$1")}function d(S){return S.replace(/\\/g,"")}function e(S,Y){return S==="*"||S.toLowerCase()===c(Y.toLowerCase())}function f(S,Y,ea){var O=[];(S=(ea.ownerDocument||ea).getElementById(d(S)))&&e(Y,S.nodeName)&&ib(ea,S)&&O.push(S);return O}function i(S,Y,ea){var O=ea.ownerDocument||ea,ia=[],na,Ba,Ca;if(ea.getElementsByClassName){O=ea.getElementsByClassName(d(S));na=0;for(Ba=O.length;na<Ba;na++){Ca=O[na];e(Y,
Ca.nodeName)&&ia.push(Ca)}}else if(O.querySelectorAll){O=O.querySelectorAll((ea.nodeName!=="#document"?ea.nodeName+" ":"")+Y+"."+S);na=0;for(Ba=O.length;na<Ba;na++){Ca=O[na];ib(ea,Ca)&&ia.push(Ca)}}else{O=ea.getElementsByTagName(Y);S=" "+S+" ";na=0;for(Ba=O.length;na<Ba;na++){Ca=O[na];if(Ca.nodeType==1)(Y=Ca.className)&&(" "+Y+" ").indexOf(S)>-1&&ia.push(Ca)}}return ia}function k(S,Y,ea){var O=[];S=(ea.ownerDocument||ea).getElementsByName(d(S));for(var ia,na=0,Ba=S.length;na<Ba;na++){ia=S[na];e(Y,
ia.nodeName)&&ib(ea,ia)&&ia.getAttribute("name")!==null&&O.push(ia)}return O}function n(S,Y,ea,O){var ia=[];ea=O.getElementsByTagName(ea);for(var na=0,Ba=ea.length;na<Ba;na++){O=ea[na];if(O.nodeType==1)if(Y===null)jb(O,S)!==null&&ia.push(O);else Y===c(jb(O,S))&&ia.push(O)}return ia}function s(S,Y){var ea=[],O,ia=(O=/^((?:\\.|[^.#\s\[<>])+)/.exec(S))?O[1]:"*";if(O=/#((?:[\w\-]|\\.)+)$/.exec(S))ea=f(O[1],ia,Y);else if(O=/\.((?:[\w\-]|\\.)+)$/.exec(S))ea=i(O[1],ia,Y);else if(O=/\[((?:[\w\-]|\\.)+)\]/.exec(S))ea=
n(O[1].toLowerCase(),null,ia,Y);else if(O=/\[((?:[\w\-]|\\.)+)\s*=\s*['"]?((?:\\.|[^'"]+)+)['"]?\]/.exec(S)){ea=O[1].toLowerCase();O=O[2];ea=ea==="id"?f(O,ia,Y):ea==="class"?i(O,ia,Y):ea==="name"?k(O,ia,Y):n(ea,O,ia,Y)}else{Y=Y.getElementsByTagName(ia);O=0;for(S=Y.length;O<S;O++){ia=Y[O];ia.nodeType==1&&ea.push(ia)}}return ea}var w=a.split(",");if(w.length>1){var A=[];l(w,function(){l(kb(this,b),function(){o(this,A)<0&&A.push(this)})});return A}b=b||document;w=[];for(var y,K=/((?:\\.|[^\s>])+|[\s>])/g;y=
K.exec(a);)y[1]!==" "&&w.push(y[1]);a=[];if(w.length==1)return s(w[0],b);y=false;var x,L,ca,F,U,ga,E,N,ja,oa;U=0;for(N=w.length;U<N;U++){K=w[U];if(K===">")y=true;else{if(U>0){x=[];ga=0;for(ja=a.length;ga<ja;ga++){ca=a[ga];L=s(K,ca);E=0;for(oa=L.length;E<oa;E++){F=L[E];if(y)ca===F.parentNode&&x.push(F);else x.push(F)}}a=x}else a=s(K,b);if(a.length===0)return[]}}return a}function Dc(a,b){a=kb(a,b);return a.length>0?a[0]:null}function Da(a){return j(a)[0]}function Oa(a){if(!a)return document;return a.ownerDocument||
a.document||a}function Pa(a){if(!a)return g;a=Oa(a);return a.parentWindow||a.defaultView}function Ec(a,b){if(a.nodeType==1){var c=Oa(a);try{a.innerHTML='<img id="__kindeditor_temp_tag__" width="0" height="0" style="display:none;" />'+b;var d=c.getElementById("__kindeditor_temp_tag__");d.parentNode.removeChild(d)}catch(e){j(a).empty();j("@"+b,c).each(function(){a.appendChild(this)})}}}function wb(a,b){return p(b,a.className," ")}function xb(a,b,c){if(ka&&qa<8&&b.toLowerCase()=="class")b="className";
a.setAttribute(b,""+c)}function Fc(a,b){if(ka&&qa<8&&b.toLowerCase()=="class")b="className";xb(a,b,"");a.removeAttribute(b)}function Wb(a){if(!a||!a.nodeName)return"";return a.nodeName.toLowerCase()}function Gc(a,b){var c=Pa(a),d=D(b),e="";if(c.getComputedStyle){c=c.getComputedStyle(a,null);e=c[d]||c.getPropertyValue(b)||a.style[d]}else if(a.currentStyle)e=a.currentStyle[d]||a.style[d];return e}function Xb(a){return!!Hc[Wb(a)]}function ya(a){a=a||document;return Ia?a.body:a.documentElement}function Ta(a){a=
a||document;var b;if(ka||Ic||yb){b=ya(a).scrollLeft;a=ya(a).scrollTop}else{b=Pa(a).scrollX;a=Pa(a).scrollY}return{x:b,y:a}}function ta(a){this.init(a)}function Yb(a){a.collapsed=a.startContainer===a.endContainer&&a.startOffset===a.endOffset;return a}function zb(a,b,c){function d(F,U,ga){var E=F.nodeValue.length,N;if(b){N=F.cloneNode(true);N=U>0?N.splitText(U):N;ga<E&&N.splitText(ga-U)}if(c){var ja=F;if(U>0){ja=F.splitText(U);a.setStart(F,U)}if(ga<E){F=ja.splitText(ga-U);a.setEnd(F,0)}k.push(ja)}return N}
function e(){c&&a.up().collapse(true);for(var F=0,U=k.length;F<U;F++){var ga=k[F];ga.parentNode&&ga.parentNode.removeChild(ga)}}function f(F,U){F=F.firstChild;for(var ga;F;){ga=(new Ea(i)).selectNode(F);s=ga.compareBoundaryPoints(Va,a);if(s>=0&&w<=0)w=ga.compareBoundaryPoints(Wa,a);if(w>=0&&A<=0)A=ga.compareBoundaryPoints(Ua,a);if(A>=0&&y<=0)y=ga.compareBoundaryPoints(Xa,a);if(y>=0)return false;ga=F.nextSibling;if(s>0)if(F.nodeType==1)if(w>=0&&A<=0){b&&U.appendChild(F.cloneNode(true));c&&k.push(F)}else{var E;
if(b){E=F.cloneNode(false);U.appendChild(E)}if(f(F,E)===false)return false}else if(F.nodeType==3){F=F==n.startContainer?d(F,n.startOffset,F.nodeValue.length):F==n.endContainer?d(F,0,n.endOffset):d(F,0,F.nodeValue.length);if(b)try{U.appendChild(F)}catch(N){}}F=ga}}var i=a.doc,k=[],n=a.cloneRange().down(),s=-1,w=-1,A=-1,y=-1,K=a.commonAncestor(),x=i.createDocumentFragment();if(K.nodeType==3){K=d(K,a.startOffset,a.endOffset);b&&x.appendChild(K);e();return b?x:a}f(K,x);c&&a.up().collapse(true);K=0;for(var L=
k.length;K<L;K++){var ca=k[K];ca.parentNode&&ca.parentNode.removeChild(ca)}return b?x:a}function Ya(a,b){for(var c=b;c;){var d=j(c);if(d.name=="marquee"||d.name=="select")return;c=c.parentNode}try{a.moveToElementText(b)}catch(e){}}function Zb(a,b){var c=a.parentElement().ownerDocument,d=a.duplicate();d.collapse(b);var e=d.parentElement(),f=e.childNodes;if(f.length===0)return{node:e.parentNode,offset:j(e).index()};b=c;var i=0,k=-1,n=a.duplicate();Ya(n,e);for(var s=0,w=f.length;s<w;s++){var A=f[s];
k=n.compareEndPoints("StartToStart",d);if(k===0)return{node:A.parentNode,offset:s};if(A.nodeType==1){var y=a.duplicate(),K,x=j(A),L=A;if(x.isControl()){K=c.createElement("span");x.after(K);L=K;i+=x.text().replace(/\r\n|\n|\r/g,"").length}Ya(y,L);n.setEndPoint("StartToEnd",y);if(k>0)i+=y.text.replace(/\r\n|\n|\r/g,"").length;else i=0;K&&j(K).remove()}else if(A.nodeType==3){n.moveStart("character",A.nodeValue.length);i+=A.nodeValue.length}if(k<0)b=A}if(k<0&&b.nodeType==1)return{node:e,offset:j(e.lastChild).index()+
1};if(k>0)for(;b.nextSibling&&b.nodeType==1;)b=b.nextSibling;n=a.duplicate();Ya(n,e);n.setEndPoint("StartToEnd",d);i-=n.text.replace(/\r\n|\n|\r/g,"").length;if(k>0&&b.nodeType==3)for(a=b.previousSibling;a&&a.nodeType==3;){i-=a.nodeValue.length;a=a.previousSibling}return{node:b,offset:i}}function $b(a,b){var c=a.ownerDocument||a,d=c.body.createTextRange();if(c==a){d.collapse(true);return d}if(a.nodeType==1&&a.childNodes.length>0){a=a.childNodes;var e,f;if(b===0){f=a[0];e=true}else{f=a[b-1];e=false}if(!f)return d;
if(j(f).name==="head"){if(b===1)e=true;if(b===2)e=false;d.collapse(e);return d}if(f.nodeType==1){b=j(f);var i;if(b.isControl()){i=c.createElement("span");e?b.before(i):b.after(i);f=i}Ya(d,f);d.collapse(e);i&&j(i).remove();return d}a=f;b=e?0:f.nodeValue.length}c=c.createElement("span");j(a).before(c);Ya(d,c);d.moveStart("character",b);j(c).remove();return d}function ac(a){function b(e){if(j(e.node).name=="tr"){e.node=e.node.cells[e.offset];e.offset=0}}var c;if(za){if(a.item){c=Oa(a.item(0));c=new Ea(c);
c.selectNode(a.item(0));return c}c=a.parentElement().ownerDocument;var d=Zb(a,true);a=Zb(a,false);b(d);b(a);c=new Ea(c);c.setStart(d.node,d.offset);c.setEnd(a.node,a.offset);return c}d=a.startContainer;c=d.ownerDocument||d;c=new Ea(c);c.setStart(d,a.startOffset);c.setEnd(a.endContainer,a.endOffset);return c}function Ea(a){this.init(a)}function Ab(a){if(!a.nodeName)return a.constructor===Ea?a:ac(a);return new Ea(a)}function Qa(a,b,c){try{a.execCommand(b,false,c)}catch(d){}}function bc(a,b){var c="";
try{c=a.queryCommandValue(b)}catch(d){}if(typeof c!=="string")c="";return c}function Bb(a){var b=Pa(a);return za?a.selection:b.getSelection()}function Jc(a){var b=Bb(a),c;try{c=b.rangeCount>0?b.getRangeAt(0):b.createRange()}catch(d){}if(za&&(!c||!c.item&&c.parentElement().ownerDocument!==a))return null;return c}function cc(a){var b={},c,d;l(a,function(e,f){c=e.split(",");e=0;for(var i=c.length;e<i;e++){d=c[e];b[d]=f}});return b}function Cb(a,b){return dc(a,b,"*")||dc(a,b)}function dc(a,b,c){c=c||
a.name;if(a.type!==1)return false;b=cc(b);if(!b[c])return false;c=b[c].split(",");b=0;for(var d=c.length;b<d;b++){var e=c[b];if(e==="*")return true;var f=/^(\.?)([^=]+)(?:=([^=]*))?$/.exec(e),i=f[1]?"css":"attr";e=f[2];f=f[3]||"";if(f===""&&a[i](e)!=="")return true;if(f!==""&&a[i](e)===f)return true}return false}function Fa(a,b){if(a.type==1){ec(a,b,"*");ec(a,b)}}function ec(a,b,c){c=c||a.name;if(a.type===1){b=cc(b);if(b[c]){c=b[c].split(",");b=false;for(var d=0,e=c.length;d<e;d++){var f=c[d];if(f===
"*"){b=true;break}var i=/^(\.?)([^=]+)(?:=([^=]*))?$/.exec(f);f=i[2];if(i[1]){f=D(f);if(a[0].style[f])a[0].style[f]=""}else a.removeAttr(f)}b&&a.remove(true)}}}function Db(a){for(a=a;a.first();)a=a.first();return a}function xa(a){if(a.type!=1||a.isSingle())return false;return a.html().replace(/<[^>]+>/g,"")===""}function Kc(a,b){a=a.clone(true);for(var c=Db(a),d=a,e=false;b;){for(;d;){if(d.name===b.name){Lc(d,b.attr(),b.css());e=true}d=d.first()}e||c.append(b.clone(false));e=false;b=b.first()}return a}
function Mc(a,b){b=b.clone(true);if(a.type==3){Db(b).append(a.clone(false));a.replaceWith(b);return b}for(var c=a,d;(d=a.first())&&d.children().length==1;)a=d;d=a.first();for(a=a.doc.createDocumentFragment();d;){a.appendChild(d[0]);d=d.next()}b=Kc(c,b);a.firstChild&&Db(b).append(a);c.replaceWith(b);return b}function Lc(a,b,c){l(b,function(d,e){d!=="style"&&a.attr(d,e)});l(c,function(d,e){a.css(d,e)})}function Nc(a){for(;a&&a.name!="body";){if(rb[a.name]||a.name=="div"&&a.hasClass("ke-script"))return true;
a=a.parent()}return false}function Za(a){this.init(a)}function fc(a){if(a.nodeName){a=Oa(a);a=Ab(a).selectNodeContents(a.body).collapse(false)}return new Za(a)}function Eb(a){var b=a.moveEl,c=a.moveFn,d=a.clickEl||b,e=a.beforeDrag,f=[document];(a.iframeFix===h?true:a.iframeFix)&&j("iframe").each(function(){if(!/^https?:\/\//.test(Na(this.src||"","absolute"))){var i;try{i=Fb(this)}catch(k){}if(i){var n=j(this).pos();j(i).data("pos-x",n.x);j(i).data("pos-y",n.y);f.push(i)}}});d.mousedown(function(i){function k(F){F.preventDefault();
var U=j(Oa(F.target)),ga=Ja((U.data("pos-x")||0)+F.pageX-L);F=Ja((U.data("pos-y")||0)+F.pageY-ca);c.call(d,A,y,K,x,ga,F)}function n(F){F.preventDefault()}function s(F){F.preventDefault();j(f).unbind("mousemove",k).unbind("mouseup",s).unbind("selectstart",n);w.releaseCapture&&w.releaseCapture()}if(!(i.button!==0&&i.button!==1)){i.stopPropagation();var w=d.get(),A=z(b.css("left")),y=z(b.css("top")),K=b.width(),x=b.height(),L=i.pageX,ca=i.pageY;e&&e();j(f).mousemove(k).mouseup(s).bind("selectstart",
n);w.setCapture&&w.setCapture()}})}function Ka(a){this.init(a)}function Gb(a){return new Ka(a)}function Fb(a){a=Da(a);return a.contentDocument||a.contentWindow.document}function Oc(a,b,c,d){var e=[Hb===""?"<html>":'<html dir="'+Hb+'">','<head><meta charset="utf-8" /><title></title>',j.pageBasePath===""?"":'<base href="'+j.pageBasePath+'">',"<!--[if (IE 6)|(IE 7)|(IE 8)]>",'<script type="text/javascript">',"(function() {",'var a = ["hide"];',"for (var i = 0, j = a.length; i < j; i++) {","document.createElement(a[i]);",
"}","})();","<\/script>","<![endif]--\>","<style>","html {margin:0;padding:0;}","body {margin:0;padding:5px;}",'body, td {font:12px/1.5 "sans serif",tahoma,verdana,helvetica;}',"body, p, div {word-wrap: break-word;}","p {margin:5px 0;}","table {border-collapse:collapse;}","img {border:0;}","noscript {display:none;}","table.ke-zeroborder td {border:1px dotted #AAA;}","img.ke-flash {","\tborder:1px solid #AAA;","\tbackground-image:url("+a+"common/flash.gif);","\tbackground-position:center center;",
"\tbackground-repeat:no-repeat;","\twidth:100px;","\theight:100px;","}","img.ke-rm {","\tborder:1px solid #AAA;","\tbackground-image:url("+a+"common/rm.gif);","\tbackground-position:center center;","\tbackground-repeat:no-repeat;","\twidth:100px;","\theight:100px;","}","img.ke-media,img.ke-video,img.ke-iframe {","\tborder:1px solid #AAA;","\tbackground-image:url("+a+"common/media.gif);","\tbackground-position:center center;","\tbackground-repeat:no-repeat;","\twidth:320px;","\theight:240px;","}",
"img.ke-anchor {","\tborder:1px dashed #666;","\twidth:16px;","\theight:16px;","}",".ke-script, .ke-noscript, .ke-display-none {","\tdisplay:none;","\tfont-size:0;","\twidth:0;","\theight:0;","}",".ke-pagebreak {","\tborder:1px dotted #AAA;","\tfont-size:0;","\theight:2px;","}","</style>"];m(c)||(c=[c]);l(c,function(f,i){i&&e.push('<link href="'+i+'" rel="stylesheet" />')});d&&e.push("<style>"+d+"</style>");e.push("</head><body "+(b?'class="'+b+'"':"")+"></body></html>");return e.join("\n")}function $a(a,
b){if(a.hasVal()){if(b===h){a=a.val();return a=a.replace(/(<(?:p|p\s[^>]*)>) *(<\/p>)/ig,"")}return a.val(b)}return a.html(b)}function ab(a){this.init(a)}function gc(a){return new ab(a)}function hc(a,b){if(a=this.get(a))a.hasClass("ke-disabled")||b(a)}function lb(a){this.init(a)}function ic(a){return new lb(a)}function bb(a){this.init(a)}function Ib(a){return new bb(a)}function cb(a){this.init(a)}function jc(a){return new cb(a)}function Jb(a){this.init(a)}function Pc(a){return new Jb(a)}function Qc(a){a=
a||{};var b=a.name||"",c=j('<span class="ke-button-common ke-button-outer" title="'+b+'"></span>');b=j('<input class="ke-button-common ke-button" type="button" value="'+b+'" />');a.click&&b.click(a.click);c.append(b);return c}function db(a){this.init(a)}function kc(a){return new db(a)}function Rc(a){var b=Gb(a),c=b.remove,d=a.afterSelect;a=b.div;var e=[];a.addClass("ke-tabs").bind("contextmenu,mousedown,mousemove",function(i){i.preventDefault()});var f=j('<ul class="ke-tabs-ul ke-clearfix"></ul>');
a.append(f);b.add=function(i){var k=i.title!=""?j('<li class="ke-tabs-li">'+i.title+"</li>"):j('<li class="">'+i.title+"</li>");k.data("tab",i);e.push(k);f.append(k)};b.selectedIndex=0;b.select=function(i){b.selectedIndex=i;l(e,function(k,n){n.unbind();if(k===i){n.addClass("ke-tabs-li-selected");j(n.data("tab").panel).show("")}else{n.removeClass("ke-tabs-li-selected").removeClass("ke-tabs-li-on").mouseover(function(){j(this).addClass("ke-tabs-li-on")}).mouseout(function(){j(this).removeClass("ke-tabs-li-on")}).click(function(){b.select(k)});
j(n.data("tab").panel).hide()}});d&&d.call(b,i)};b.remove=function(){l(e,function(){this.remove()});f.remove();c.call(b)};return b}function Kb(a,b){var c=document.getElementsByTagName("head")[0]||(Ia?document.body:document.documentElement),d=document.createElement("script");c.appendChild(d);d.src=a;d.charset="utf-8";d.onload=d.onreadystatechange=function(){if(!this.readyState||this.readyState==="loaded"){b&&b();d.onload=d.onreadystatechange=null;c.removeChild(d)}}}function lc(a){var b=a.indexOf("?");
return b>0?a.substr(0,b):a}function Lb(a){for(var b=document.getElementsByTagName("head")[0]||(Ia?document.body:document.documentElement),c=document.createElement("link"),d=lc(Na(a,"absolute")),e=j('link[rel="stylesheet"]',b),f=0,i=e.length;f<i;f++)if(lc(Na(e[f].href,"absolute"))===d)return;b.appendChild(c);c.href=a;c.rel="stylesheet"}function Sc(a,b,c,d,e){c=c||"GET";e=e||"json";var f=g.XMLHttpRequest?new g.XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP");f.open(c,a,true);f.onreadystatechange=
function(){if(f.readyState==4&&f.status==200)if(b){var n=q(f.responseText);if(e=="json")n=V(n);b(n)}};if(c=="POST"){var i=[];l(d,function(n,s){i.push(encodeURIComponent(n)+"="+encodeURIComponent(s))});try{f.setRequestHeader("Content-Type","application/x-www-form-urlencoded")}catch(k){}f.send(i.join("&"))}else f.send(null)}function mc(a,b){if(a===h)return Ga;if(!b)return Ga[a];Ga[a]=b}function nc(a){var b,c="core";if(b=/^(\w+)\.(\w+)$/.exec(a)){c=b[1];a=b[2]}return{ns:c,key:a}}function oc(a,b){b=b===
h?j.options.langType:b;if(typeof a==="string"){if(!Ha[b])return"no language";var c=a.length-1;if(a.substr(c)===".")return Ha[b][a.substr(0,c)];a=nc(a);return Ha[b][a.ns][a.key]}l(a,function(d,e){d=nc(d);Ha[b]||(Ha[b]={});Ha[b][d.ns]||(Ha[b][d.ns]={});Ha[b][d.ns][d.key]=e})}function mb(a,b){if(!a.collapsed){a=a.cloneRange().up();var c=a.startContainer,d=a.startOffset;if(Ra||a.isControl()){a=j(c.childNodes[d]);if(!(!a||a.name!="img"))if(b(a))return a}}}function Tc(){var a=this;j(a.edit.doc).contextmenu(function(b){a.menu&&
a.hideMenu();if(a.useContextmenu){if(a._contextmenus.length!==0){var c=0,d=[];for(l(a._contextmenus,function(){if(this.title=="-")d.push(this);else if(this.cond&&this.cond()){d.push(this);if(this.width&&this.width>c)c=this.width}});d.length>0&&d[0].title=="-";)d.shift();for(;d.length>0&&d[d.length-1].title=="-";)d.pop();var e=null;l(d,function(n){this.title=="-"&&e.title=="-"&&delete d[n];e=this});if(d.length>0){b.preventDefault();var f=b.clientY;if(b.screenY<b.clientY)if(document.querySelector(".el-main")){f=
document.querySelector(".el-main").offsetTop;var i=document.querySelector(".el-main").offsetParent;if(i!=null){f+=i.offsetTop;i=i.offsetParent}f=b.screenY-f}else f=b.screenY;i=j(a.edit.iframe).pos();var k=Ib({x:i.x+b.clientX,y:i.y+f,width:c,css:{visibility:"hidden"},shadowMode:a.shadowMode});l(d,function(){this.title&&k.addItem(this)});f=ya(k.doc);i=k.div.height();b.clientY+i>=f.clientHeight-100&&k.pos(k.x,z(k.y)-i);k.div.css("visibility","visible");a.menu=k}}}else b.preventDefault()})}function Uc(){function a(k){for(k=
j(k.commonAncestor());k;){if(k.type==1&&!k.isStyle())break;k=k.parent()}return k.name}function b(k){for(k=j(k.commonAncestor());k;){if(k.type==1&&!k.isStyle())break;if(k.name=="pre"||k.name=="code"||k.name=="hide")break;k=k.parent()}return k}var c=this,d=c.edit.doc,e=c.newlineTag;if(!(ka&&e!=="br"))if(!(Sa&&qa<3&&e!=="p"))if(!(yb&&qa<9)){var f=J("h1,h2,h3,h4,h5,h6,pre,code,hide,li"),i=J("p,h1,h2,h3,h4,h5,h6,pre,code,hide,li,blockquote");j(d).keydown(function(k){if(!(k.which!=13||k.shiftKey||k.ctrlKey||
k.altKey)){c.cmd.selection();var n=a(c.cmd.range);if(!(n=="marquee"||n=="select"))if(e==="br"&&!f[n]){k.preventDefault();c.insertHtml("<br />"+(ka&&qa<9?"":"\u200b"))}else{var s=b(c.cmd.range);if(s.name=="pre"||s.name=="code"||s.name=="hide"){c.cmd.range.selectNodeContents(s).collapse(false);c.cmd.select();if(c.cmd.range.html()==""){if(s.name=="code"){c.cmd.range.selectNodeContents(s.parent()).collapse(false);c.cmd.select()}c.cmd.range.insertNode(d.createTextNode("\u200b"))}else if(s.name=="code"){c.cmd.range.selectNodeContents(s.parent()).collapse(false);
c.cmd.select()}setTimeout(function(){c.cmd.selection();b(c.cmd.range).remove(true)},4)}else{var w="";j(c.cmd.range.commonAncestor()).scan(function(A){if(j(A).name=="pre"||j(A).name=="code"||j(A).name=="hide")w=j(A).name});if(w!="")if(c.cmd.range.startOffset==0&&c.cmd.range.endOffset==0){if(s.name=="code"){c.cmd.range.selectNodeContents(w.parent()).collapse(false);c.cmd.select()}setTimeout(function(){c.cmd.selection();b(c.cmd.range).remove(true)},4)}else{k.preventDefault();c.insertHtml("<br />"+(ka&&
qa<9?"":"\u200b"))}else i[n]||Qa(d,"formatblock","<p>")}}}});j(d).keyup(function(k){if(!(k.which!=13||k.shiftKey||k.ctrlKey||k.altKey))if(e!="br")if(Sa){k=c.cmd.commonAncestor("p");var n=c.cmd.commonAncestor("a");if(n&&n.text()==""){n.remove(true);c.cmd.range.selectNodeContents(k[0]).collapse(true);c.cmd.select()}}else{c.cmd.selection();a(c.cmd.range)}})}}function Vc(){var a=this,b=a.edit.doc;j(b).keydown(function(c){if(c.which==9){c.preventDefault();if(a.afterTab)a.afterTab.call(a,c);else{c=a.cmd;
var d=c.range;d.shrink();if(d.collapsed&&d.startContainer.nodeType==1){d.insertNode(j("@&nbsp;",b)[0]);c.select()}a.insertHtml("&nbsp;&nbsp;&nbsp;&nbsp;")}}})}function Wc(){var a=this;j(a.edit.textarea[0],a.edit.win).focus(function(b){a.afterFocus&&a.afterFocus.call(a,b)}).blur(function(b){a.afterBlur&&a.afterBlur.call(a,b)})}function La(a){return q(a.replace(/<span [^>]*id="?__kindeditor_bookmark_\w+_\d+__"?[^>]*><\/span>/ig,""))}function nb(a){return a.replace(/<div[^>]+class="?__kindeditor_paste__"?[^>]*>[\s\S]*?<\/div>/ig,
"")}function pc(a,b){if(a.length===0)a.push(b);else{var c=a[a.length-1];La(b.html)!==La(c.html)&&a.push(b)}}function qc(a,b){var c=this.edit,d=c.doc.body,e,f;if(a.length===0)return this;if(c.designMode){e=this.cmd.range;f=e.createBookmark(true);f.html=d.innerHTML}else f={html:d.innerHTML};pc(b,f);b=a.pop();if(La(f.html)===La(b.html)&&a.length>0)b=a.pop();if(c.designMode){c.html(b.html);if(b.start){e.moveToBookmark(b);this.select()}}else j(d).html(La(b.html));return this}function eb(a){function b(e,
f){if(eb.prototype[e]===h)c[e]=f;c.options[e]=f}var c=this;c.options={};l(a,function(e){b(e,a[e])});l(j.options,function(e,f){c[e]===h&&b(e,f)});var d=j(c.srcElement||"<textarea/>");if(!c.width)c.width=d[0].style.width||d.width();if(!c.height)c.height=d[0].style.height||d.height();b("width",B(c.width,c.minWidth));b("height",B(c.height,c.minHeight));b("width",r(c.width));b("height",r(c.height));if(Xc&&(!Yc||qa<534))c.designMode=false;c.srcElement=d;c.initContent="";c.plugin={};c.isCreated=false;c._handlers=
{};c._contextmenus=[];c._undoStack=[];c._redoStack=[];c._firstAddBookmark=true;c.menu=c.contextmenu=null;c.dialogs=[]}function Zc(a){return new eb(a)}function rc(a,b){function c(w){l(Ga,function(A,y){if(t(y)){y.call(w,KindEditor);if(!w._pluginStatus)w._pluginStatus={};w._pluginStatus[A]="inited"}});return w.create()}b=b||{};b.basePath=B(b.basePath,j.basePath);b.themesPath=B(b.themesPath,b.basePath+"themes/");b.langPath=B(b.langPath,b.basePath+"lang/");b.pluginsPath=B(b.pluginsPath,b.basePath+"plugins/");
if(B(b.loadStyleMode,j.options.loadStyleMode)){var d=B(b.themeType,j.options.themeType),e="",f=[],i=d.split(/\s+/);if(i!=null&&i.length>0)for(var k=0;k<i.length;k++){var n=i[k];if(n!=null&&n!="")if(n.substr(0,1)==":"){e=n.substr(1,n.length);f.push(e)}else f.push(n)}if(d=="default")Lb(b.themesPath+d+"/default.css");else for(k=0;k<f.length;k++)Lb(b.themesPath+e+"/"+f[k]+".css")}a=j(a);if(!(!a||a.length===0)){if(a.length>1){a.each(function(){rc(this,b)});return fb[0]}b.srcElement=a[0];var s=new eb(b);
fb.push(s);if(Ha[s.langType])return c(s);Kb(s.langPath+s.langType+".js?ver="+encodeURIComponent(j.DEBUG?ob:pb),function(){c(s)});return s}}function gb(a,b){j(a).each(function(c,d){j.each(fb,function(e,f){if(f&&f.srcElement[0]==d){b.call(f,e);return false}})})}if(!g.KindEditor){if(!g.console)g.console={};if(!console.log)console.log=function(){};var pb="4.1.12 (2019-03-07)",la=navigator.userAgent.toLowerCase(),ka=la.indexOf("msie")>-1&&la.indexOf("opera")==-1,Ic=la.indexOf("msie")==-1&&la.indexOf("trident")>
-1,Sa=la.indexOf("gecko")>-1&&la.indexOf("khtml")==-1,Ra=la.indexOf("applewebkit")>-1,yb=la.indexOf("opera")>-1,Xc=la.indexOf("mobile")>-1,Yc=/ipad|iphone|ipod/.test(la),Ia=document.compatMode!="CSS1Compat",za=!g.getSelection,qa=(la=/(?:msie|firefox|webkit|opera)[\/:\s](\d+)/.exec(la))?la[1]:"0",ob=(new Date).getTime(),Ja=Math.round,j={DEBUG:false,VERSION:pb,IE:ka,GECKO:Sa,WEBKIT:Ra,OPERA:yb,V:qa,TIME:ob,each:l,isArray:m,isFunction:t,inArray:o,inString:p,trim:q,addUnit:r,removeUnit:z,escape:H,unescape:C,
toCamel:D,toHex:Q,toMap:J,toArray:da,undef:B,invalidUrl:M,addParam:T,extend:G,json:V,popupMessage:u},Rb=J("a,abbr,acronym,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,img,input,ins,kbd,label,map,q,s,samp,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),Sb=J("address,applet,blockquote,body,center,dd,dir,div,dl,dt,fieldset,form,frameset,h1,h2,h3,h4,h5,h6,head,hr,html,iframe,ins,isindex,li,map,menu,meta,noframes,noscript,object,ol,p,pre,script,style,table,tbody,td,tfoot,th,thead,title,tr,ul"),
Qb=J("area,base,basefont,br,col,frame,hr,img,input,isindex,link,meta,param,video"),sc=J("b,basefont,big,del,em,font,i,s,small,span,strike,strong,sub,sup,u"),$c=J("img,table,input,textarea,button"),rb=J("pre,style,script"),qb=J("html,head,body,td,tr,table,ol,ul,li");J("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr");var vc=J("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),Hc=J("input,button,textarea,select");j.basePath=Z();j.pageBasePath=
I();j.options={designMode:true,fullscreenMode:false,filterMode:true,wellFormatMode:true,shadowMode:true,loadStyleMode:true,basePath:j.basePath,themesPath:j.basePath+"themes/",langPath:j.basePath+"lang/",pluginsPath:j.basePath+"plugins/",themeType:"default",langType:"zh-CN",uploadModule:0,urlType:"",newlineTag:"p",resizeType:2,syncType:"form",pasteType:2,dialogAlignType:"page",useContextmenu:true,fullscreenShortcut:false,bodyClass:"ke-content",indentChar:"\t",cssPath:"",cssData:"",minWidth:650,minHeight:100,
minChangeSize:50,zIndex:811213,items:["source","|","undo","redo","|","preview","print","template","code","cut","copy","paste","plainpaste","wordpaste","|","justifyleft","justifycenter","justifyright","justifyfull","insertorderedlist","insertunorderedlist","indent","outdent","subscript","superscript","clearhtml","quickformat","selectall","|","fullscreen","/","formatblock","fontname","fontsize","|","forecolor","hilitecolor","bold","italic","underline","strikethrough","lineheight","removeformat","|",
"image","multiimage","flash","media","insertfile","table","hr","emoticons","baidumap","pagebreak","anchor","link","unlink","|","about"],noDisableItems:["source","fullscreen","embedVideo","uploadVideo"],noToolbarItems:["embedVideo","uploadVideo","hidePassword","hideComment","hideGrade","hidePoint","hideAmount"],colorTable:[["#E53333","#E56600","#FF9900","#64451D","#DFC5A4","#FFE500"],["#009900","#006600","#99BB00","#B8D100","#60D978","#00D5FF"],["#337FE5","#003399","#4C33E5","#9933E5","#CC33E5","#EE33EE"],
["#FFFFFF","#CCCCCC","#999999","#666666","#333333","#000000"]],fontSizeTable:["9px","10px","12px","14px","16px","18px","24px","32px"],htmlTags:{font:["id","class","color","size","face",".background-color"],span:["id","class",".color",".background-color",".font-size",".font-family",".background",".font-weight",".font-style",".text-decoration",".vertical-align",".line-height"],div:["id","class","align",".border",".margin",".padding",".text-align",".color",".background-color",".font-size",".font-family",
".font-weight",".background",".font-style",".text-decoration",".vertical-align",".margin-left"],table:["id","class","border","cellspacing","cellpadding","width","height","align","bordercolor",".padding",".margin",".border","bgcolor",".text-align",".color",".background-color",".font-size",".font-family",".font-weight",".font-style",".text-decoration",".background",".width",".height",".border-collapse"],"td,th":["id","class","align","valign","width","height","colspan","rowspan","bgcolor",".text-align",
".color",".background-color",".font-size",".font-family",".font-weight",".font-style",".text-decoration",".vertical-align",".background",".border"],a:["id","class","href","target","name"],video:["id","class","src","width","height","loop","autoplay","muted",".width",".height","align","poster","preload","controls"],img:["id","class","src","width","height","border","alt","title","align",".width",".height",".border","data-ke-tag"],"p,ol,ul,li,blockquote,h1,h2,h3,h4,h5,h6":["id","class","align",".text-align",
".color",".background-color",".font-size",".font-family",".background",".font-weight",".font-style",".text-decoration",".vertical-align",".text-indent",".margin-left"],pre:["id","class"],code:[],hr:["id","class",".page-break-after"],"br,tbody,tr,strong,b,sub,sup,em,i,u,strike,s,del":["id","class"],iframe:["id","class","src","width","height",".width",".height","scrolling","border","allow","frameborder","framespacing","allowfullscreen"]},layout:'<div class="container"><div class="toolbar"></div><div class="edit"></div><div class="statusbar"></div></div>'};
var Nb=false,tc=J("8,9,13,32,46,48..57,59,61,65..90,106,109..111,188,190..192,219..222");la=J("33..40");var Mb={};l(tc,function(a,b){Mb[a]=b});l(la,function(a,b){Mb[a]=b});var ad="altKey,attrChange,attrName,bubbles,button,cancelable,charCode,clientX,clientY,ctrlKey,currentTarget,data,detail,eventPhase,fromElement,handler,keyCode,metaKey,newValue,offsetX,offsetY,originalTarget,pageX,pageY,prevValue,relatedNode,relatedTarget,screenX,screenY,shiftKey,srcElement,target,toElement,view,wheelDelta,which".split(",");
G(R,{init:function(a,b){var c=this,d=a.ownerDocument||a.document||a;c.event=b;l(ad,function(e,f){c[f]=b[f]});if(!c.target)c.target=c.srcElement||d;if(c.target.nodeType===3)c.target=c.target.parentNode;if(!c.relatedTarget&&c.fromElement)c.relatedTarget=c.fromElement===c.target?c.toElement:c.fromElement;if(c.pageX==null&&c.clientX!=null){a=d.documentElement;d=d.body;c.pageX=c.clientX+(a&&a.scrollLeft||d&&d.scrollLeft||0)-(a&&a.clientLeft||d&&d.clientLeft||0);c.pageY=c.clientY+(a&&a.scrollTop||d&&d.scrollTop||
0)-(a&&a.clientTop||d&&d.clientTop||0)}if(!c.which&&(c.charCode||c.charCode===0?c.charCode:c.keyCode))c.which=c.charCode||c.keyCode;if(!c.metaKey&&c.ctrlKey)c.metaKey=c.ctrlKey;if(!c.which&&c.button!==h)c.which=c.button&1?1:c.button&2?3:c.button&4?2:0;switch(c.which){case 186:c.which=59;break;case 187:case 107:case 43:c.which=61;break;case 189:case 45:c.which=109;break;case 42:c.which=106;break;case 47:c.which=111;break;case 78:c.which=110;break}if(c.which>=96&&c.which<=105)c.which-=48},preventDefault:function(){var a=
this.event;if(a.preventDefault)a.preventDefault();else a.returnValue=false},stopPropagation:function(){var a=this.event;if(a.stopPropagation)a.stopPropagation();else a.cancelBubble=true},stop:function(){this.preventDefault();this.stopPropagation()}});var hb="kindeditor_"+ob,Ob=0,pa={},Pb=false;g.attachEvent&&g.attachEvent("onunload",function(){l(pa,function(a,b){b.el&&ma(b.el)})});j.ctrl=ua;j.ready=va;j.formatUrl=Na;j.formatHtml=Aa;j.getCssList=wa;j.getAttrList=ba;j.mediaType=sb;j.mediaAttrs=tb;j.mediaVideo=
ub;j.mediaImg=Ub;j.embedVideo=vb;j.embedVideoImg=Vb;j.clearMsWord=Tb;j.tmpl=yc;j.post_ajax=zc;j.put_ajax=Ac;j.getCookie=Bc;la=document.createElement("div");la.setAttribute("className","t");var Cc=la.className!=="t";j.query=Dc;j.queryAll=kb;G(ta,{init:function(a){a=m(a)?a:[a];for(var b=0,c=0,d=a.length;c<d;c++)if(a[c]){this[c]=a[c].constructor===ta?a[c][0]:a[c];b++}this.length=b;this.doc=Oa(this[0]);this.name=Wb(this[0]);this.type=this.length>0?this[0].nodeType:null;this.win=Pa(this[0])},each:function(a){for(var b=
0;b<this.length;b++)if(a.call(this[b],b,this[b])===false)return this;return this},bind:function(a,b){this.each(function(){fa(this,a,b)});return this},unbind:function(a,b){this.each(function(){ma(this,a,b)});return this},fire:function(a){if(this.length<1)return this;ra(this[0],a);return this},hasAttr:function(a){if(this.length<1)return false;return!!jb(this[0],a)},attr:function(a,b){var c=this;if(a===h)return ba(c.outer());if(typeof a==="object"){l(a,function(d,e){c.attr(d,e)});return c}if(b===h){b=
c.length<1?null:jb(c[0],a);return b===null?"":b}c.each(function(){xb(this,a,b)});return c},removeAttr:function(a){this.each(function(){Fc(this,a)});return this},get:function(a){if(this.length<1)return null;return this[a||0]},eq:function(a){if(this.length<1)return null;return this[a]?new ta(this[a]):null},hasClass:function(a){if(this.length<1)return false;return wb(this[0],a)},addClass:function(a){this.each(function(){if(!wb(this,a))this.className=q(this.className+" "+a)});return this},removeClass:function(a){this.each(function(){if(wb(this,
a))this.className=q(this.className.replace(new RegExp("(^|\\s)"+a+"(\\s|$)")," "))});return this},html:function(a){if(a===h){if(this.length<1||this.type!=1)return"";return Aa(this[0].innerHTML)}this.each(function(){Ec(this,a)});return this},text:function(){if(this.length<1)return"";return ka?this[0].innerText:this[0].textContent},hasVal:function(){if(this.length<1)return false;return Xb(this[0])},val:function(a){if(a===h){if(this.length<1)return"";return this.hasVal()?this[0].value:this.attr("value")}else{this.each(function(){if(Xb(this))this.value=
a;else xb(this,"value",a)});return this}},css:function(a,b){var c=this;if(a===h)return wa(c.attr("style"));if(typeof a==="object"){l(a,function(d,e){c.css(d,e)});return c}if(b===h){if(c.length<1)return"";return c[0].style[D(a)]||Gc(c[0],a)||""}c.each(function(){this.style[D(a)]=b});return c},width:function(a){if(a===h){if(this.length<1)return 0;return this[0].offsetWidth}return this.css("width",r(a))},height:function(a){if(a===h){if(this.length<1)return 0;return this[0].offsetHeight}return this.css("height",
r(a))},opacity:function(a){this.each(function(){if(this.style.opacity===h)this.style.filter=a==1?"":"alpha(opacity="+a*100+")";else this.style.opacity=a==1?"":a});return this},data:function(a,b){a="kindeditor_data_"+a;if(b===h){if(this.length<1)return null;return this[0][a]}this.each(function(){this[a]=b});return this},pos:function(){var a=this[0],b=0,c=0;if(a)if(a.getBoundingClientRect){a=a.getBoundingClientRect();c=Ta(this.doc);b=a.left+c.x;c=(parseInt(a.top)<0?0:a.top)+c.y}else for(;a;){b+=a.offsetLeft;
c+=a.offsetTop;a=a.offsetParent}return{x:Ja(b),y:Ja(c)}},clone:function(a){if(this.length<1)return new ta([]);return new ta(this[0].cloneNode(a))},append:function(a){this.each(function(){this.appendChild&&this.appendChild(Da(a))});return this},appendTo:function(a){this.each(function(){Da(a).appendChild(this)});return this},before:function(a){this.each(function(){this.parentNode.insertBefore(Da(a),this)});return this},after:function(a){this.each(function(){this.nextSibling?this.parentNode.insertBefore(Da(a),
this.nextSibling):this.parentNode.appendChild(Da(a))});return this},replaceWith:function(a){var b=[];this.each(function(c,d){ma(d);c=Da(a);d.parentNode.replaceChild(c,d);b.push(c)});return j(b)},empty:function(){this.each(function(a,b){for(a=b.firstChild;a;){if(!b.parentNode)return;var c=a.nextSibling;a.parentNode.removeChild(a);a=c}});return this},remove:function(a){var b=this;b.each(function(c,d){if(d.parentNode){ma(d);if(a)for(var e=d.firstChild;e;){var f=e.nextSibling;d.parentNode.insertBefore(e,
d);e=f}d.parentNode.removeChild(d);delete b[c]}});b.length=0;return b},show:function(a){if(a===h)a=this._originDisplay||"";if(this.css("display")!="none")return this;return this.css("display",a)},hide:function(){if(this.length<1)return this;this._originDisplay=this[0].style.display;return this.css("display","none")},outer:function(){if(this.length<1)return"";var a=this.doc.createElement("div");a.appendChild(this[0].cloneNode(true));return Aa(a.innerHTML)},isSingle:function(){return!!Qb[this.name]},
isInline:function(){return!!Rb[this.name]},isBlock:function(){return!!Sb[this.name]},isStyle:function(){return!!sc[this.name]},isControl:function(){return!!$c[this.name]},contains:function(a){if(this.length<1)return false;return ib(this[0],Da(a))},parent:function(){if(this.length<1)return null;var a=this[0].parentNode;return a?new ta(a):null},children:function(){if(this.length<1)return new ta([]);for(var a=[],b=this[0].firstChild;b;){if(b.nodeType!=3||q(b.nodeValue)!=="")a.push(b);b=b.nextSibling}return new ta(a)},
first:function(){var a=this.children();return a.length>0?a.eq(0):null},last:function(){var a=this.children();return a.length>0?a.eq(a.length-1):null},index:function(){if(this.length<1)return-1;for(var a=-1,b=this[0];b;){a++;b=b.previousSibling}return a},prev:function(){if(this.length<1)return null;var a=this[0].previousSibling;return a?new ta(a):null},next:function(){if(this.length<1)return null;var a=this[0].nextSibling;return a?new ta(a):null},scan:function(a,b){function c(d){for(d=b?d.firstChild:
d.lastChild;d;){var e=b?d.nextSibling:d.previousSibling;if(a(d)===false)return false;if(c(d)===false)return false;d=e}}if(!(this.length<1)){b=b===h?true:b;c(this[0]);return this}}});l("blur,focus,focusin,focusout,load,resize,scroll,unload,click,dblclick,mousedown,mouseup,mousemove,mouseover,mouseout,mouseenter,mouseleave,change,select,submit,keydown,keypress,keyup,error,contextmenu".split(","),function(a,b){ta.prototype[b]=function(c){return c?this.bind(b,c):this.fire(b)}});la=j;j=function(a,b){function c(n){n[0]||
(n=[]);return new ta(n)}if(!(a===h||a===null)){if(typeof a==="string"){if(b)b=Da(b);var d=a.length;if(a.charAt(0)==="@")a=a.substr(1);if(a.length!==d||/<.+>/.test(a)){d=(b?b.ownerDocument||b:document).createElement("div");var e=[];d.innerHTML='<img id="__kindeditor_temp_tag__" width="0" height="0" style="display:none;" />'+a;for(var f=0,i=d.childNodes.length;f<i;f++){var k=d.childNodes[f];k.id!="__kindeditor_temp_tag__"&&e.push(k)}return c(e)}return c(kb(a,b))}if(a&&a.constructor===ta)return a;if(a.toArray)a=
a.toArray();if(m(a))return c(a);return c(da(arguments))}};l(la,function(a,b){j[a]=b});j.NodeClass=ta;g.KindEditor=j;var Wa=0,Va=1,Ua=2,Xa=3,uc=0;G(Ea,{init:function(a){this.startContainer=a;this.startOffset=0;this.endContainer=a;this.endOffset=0;this.collapsed=true;this.doc=a},commonAncestor:function(){function a(n){for(var s=[];n;){s.push(n);n=n.parentNode}return s}for(var b=a(this.startContainer),c=a(this.endContainer),d=0,e=b.length,f=c.length,i,k;++d;){i=b[e-d];k=c[f-d];if(!i||!k||i!==k)break}return b[e-
d+1]},setStart:function(a,b){var c=this.doc;this.startContainer=a;this.startOffset=b;if(this.endContainer===c){this.endContainer=a;this.endOffset=b}return Yb(this)},setEnd:function(a,b){var c=this.doc;this.endContainer=a;this.endOffset=b;if(this.startContainer===c){this.startContainer=a;this.startOffset=b}return Yb(this)},setStartBefore:function(a){return this.setStart(a.parentNode||this.doc,j(a).index())},setStartAfter:function(a){return this.setStart(a.parentNode||this.doc,j(a).index()+1)},setEndBefore:function(a){return this.setEnd(a.parentNode||
this.doc,j(a).index())},setEndAfter:function(a){return this.setEnd(a.parentNode||this.doc,j(a).index()+1)},selectNode:function(a){return this.setStartBefore(a).setEndAfter(a)},selectNodeContents:function(a){var b=j(a);if(b.type==3||b.isSingle())return this.selectNode(a);b=b.children();if(b.length>0)return this.setStartBefore(b[0]).setEndAfter(b[b.length-1]);return this.setStart(a,0).setEnd(a,0)},collapse:function(a){if(a)return this.setEnd(this.startContainer,this.startOffset);return this.setStart(this.endContainer,
this.endOffset)},compareBoundaryPoints:function(a,b){var c=this.get(),d=b.get();if(za){var e={};e[Wa]="StartToStart";e[Va]="EndToStart";e[Ua]="EndToEnd";e[Xa]="StartToEnd";c=c.compareEndPoints(e[a],d);if(c!==0)return c;var f,i,k,n;if(a===Wa||a===Xa){f=this.startContainer;k=this.startOffset}if(a===Va||a===Ua){f=this.endContainer;k=this.endOffset}if(a===Wa||a===Va){i=b.startContainer;n=b.startOffset}if(a===Ua||a===Xa){i=b.endContainer;n=b.endOffset}if(f===i){f=k-n;return f>0?1:f<0?-1:0}for(a=i;a&&a.parentNode!==
f;)a=a.parentNode;if(a)return j(a).index()>=k?-1:1;for(a=f;a&&a.parentNode!==i;)a=a.parentNode;if(a)return j(a).index()>=n?1:-1;if((a=j(i).next())&&a.contains(f))return 1;if((a=j(f).next())&&a.contains(i))return-1}else return c.compareBoundaryPoints(a,d)},cloneRange:function(){return(new Ea(this.doc)).setStart(this.startContainer,this.startOffset).setEnd(this.endContainer,this.endOffset)},toString:function(){var a=this.get();return(za?a.text:a.toString()).replace(/\r\n|\n|\r/g,"")},cloneContents:function(){return zb(this,
true,false)},deleteContents:function(){return zb(this,false,true)},extractContents:function(){return zb(this,true,true)},insertNode:function(a){var b=this.startContainer,c=this.startOffset,d=this.endContainer,e=this.endOffset,f,i,k,n=1;if(a.nodeName.toLowerCase()==="#document-fragment"){f=a.firstChild;i=a.lastChild;n=a.childNodes.length}if(b.nodeType==1)if(k=b.childNodes[c]){b.insertBefore(a,k);if(b===d)e+=n}else b.appendChild(a);else if(b.nodeType==3)if(c===0){b.parentNode.insertBefore(a,b);if(b.parentNode===
d)e+=n}else if(c>=b.nodeValue.length)b.nextSibling?b.parentNode.insertBefore(a,b.nextSibling):b.parentNode.appendChild(a);else{k=c>0?b.splitText(c):b;b.parentNode.insertBefore(a,k);if(b===d){d=k;e-=c}}f?this.setStartBefore(f).setEndAfter(i):this.selectNode(a);if(this.compareBoundaryPoints(Ua,this.cloneRange().setEnd(d,e))>=1)return this;return this.setEnd(d,e)},surroundContents:function(a){a.appendChild(this.extractContents());return this.insertNode(a).selectNode(a)},isControl:function(){var a=this.startContainer,
b=this.startOffset,c=this.endContainer,d=this.endOffset;return a.nodeType==1&&a===c&&b+1===d&&j(a.childNodes[b]).isControl()},get:function(a){var b=this.doc;if(!za){b=b.createRange();try{b.setStart(this.startContainer,this.startOffset);b.setEnd(this.endContainer,this.endOffset)}catch(c){}return b}if(a&&this.isControl()){b=b.body.createControlRange();b.addElement(this.startContainer.childNodes[this.startOffset]);return b}a=this.cloneRange().down();b=b.body.createTextRange();b.setEndPoint("StartToStart",
$b(a.startContainer,a.startOffset));b.setEndPoint("EndToStart",$b(a.endContainer,a.endOffset));return b},html:function(){return j(this.cloneContents()).outer()},down:function(){function a(c,d,e){if(c.nodeType==1){c=j(c).children();if(c.length!==0){var f,i,k,n;if(d>0)f=c.eq(d-1);if(d<c.length)i=c.eq(d);if(f&&f.type==3){k=f[0];n=k.nodeValue.length}if(i&&i.type==3){k=i[0];n=0}if(k)e?b.setStart(k,n):b.setEnd(k,n)}}}var b=this;a(b.startContainer,b.startOffset,true);a(b.endContainer,b.endOffset,false);
return b},up:function(){function a(c,d,e){if(c.nodeType==3)if(d===0)e?b.setStartBefore(c):b.setEndBefore(c);else if(d==c.nodeValue.length)e?b.setStartAfter(c):b.setEndAfter(c)}var b=this;a(b.startContainer,b.startOffset,true);a(b.endContainer,b.endOffset,false);return b},enlarge:function(a){function b(d,e,f){d=j(d);if(!(d.type==3||qb[d.name]||!a&&d.isBlock()))if(e===0){for(;!d.prev();){e=d.parent();if(!e||qb[e.name]||!a&&e.isBlock())break;d=e}f?c.setStartBefore(d[0]):c.setEndBefore(d[0])}else if(e==
d.children().length){for(;!d.next();){e=d.parent();if(!e||qb[e.name]||!a&&e.isBlock())break;d=e}f?c.setStartAfter(d[0]):c.setEndAfter(d[0])}}var c=this;c.up();b(c.startContainer,c.startOffset,true);b(c.endContainer,c.endOffset,false);return c},shrink:function(){for(var a,b=this.collapsed;this.startContainer.nodeType==1&&(a=this.startContainer.childNodes[this.startOffset])&&a.nodeType==1&&!j(a).isSingle();)this.setStart(a,0);if(b)return this.collapse(b);for(;this.endContainer.nodeType==1&&this.endOffset>
0&&(a=this.endContainer.childNodes[this.endOffset-1])&&a.nodeType==1&&!j(a).isSingle();)this.setEnd(a,a.childNodes.length);return this},createBookmark:function(a){var b,c=j('<span style="display:none;"></span>',this.doc)[0];c.id="__kindeditor_bookmark_start_"+uc++ +"__";if(!this.collapsed){b=c.cloneNode(true);b.id="__kindeditor_bookmark_end_"+uc++ +"__"}b&&this.cloneRange().collapse(false).insertNode(b).setEndBefore(b);this.insertNode(c).setStartAfter(c);return{start:a?"#"+c.id:c,end:b?a?"#"+b.id:
b:null}},moveToBookmark:function(a){var b=this.doc,c=j(a.start,b);a=a.end?j(a.end,b):null;if(!c||c.length<1)return this;this.setStartBefore(c[0]);c.remove();if(a&&a.length>0){this.setEndBefore(a[0]);a.remove()}else this.collapse(true);return this},dump:function(){console.log("--------------------");console.log(this.startContainer.nodeType==3?this.startContainer.nodeValue:this.startContainer,this.startOffset);console.log(this.endContainer.nodeType==3?this.endContainer.nodeValue:this.endContainer,this.endOffset)}});
j.RangeClass=Ea;j.range=Ab;j.START_TO_START=Wa;j.START_TO_END=Va;j.END_TO_END=Ua;j.END_TO_START=Xa;G(Za,{init:function(a){var b=a.doc;this.doc=b;this.win=Pa(b);this.sel=Bb(b);this.range=a},selection:function(a){var b=this.doc,c=Jc(b);this.sel=Bb(b);if(c){this.range=Ab(c);j(this.range.startContainer).name=="html"&&this.range.selectNodeContents(b.body).collapse(false);return this}a&&this.range.selectNodeContents(b.body).collapse(false);return this},select:function(a){a=B(a,true);var b=this.sel,c=this.range.cloneRange().shrink(),
d=c.startContainer,e=c.startOffset,f=Oa(d),i=this.win,k,n=false;if(a&&d.nodeType==1&&c.collapsed){if(za){b=j("<span>&nbsp;</span>",f);c.insertNode(b[0]);k=f.body.createTextRange();try{k.moveToElementText(b[0])}catch(s){}k.collapse(false);k.select();b.remove();i.focus();return this}if(Ra){a=d.childNodes;if(j(d).isInline()||e>0&&j(a[e-1]).isInline()||a[e]&&j(a[e]).isInline()){c.insertNode(f.createTextNode("\u200b"));n=true}}}if(za)try{k=c.get(true);k.select()}catch(w){}else{n&&c.collapse(false);k=c.get(true);
b.removeAllRanges();b.addRange(k);if(f!==document){c=j(k.endContainer).pos();i.scrollTo(c.x,c.y)}}i.focus();return this},wrap:function(a){var b=this.range,c;c=j(a,this.doc);if(b.collapsed){b.shrink();c.html("&#8203;");b.insertNode(c[0]).selectNodeContents(c[0]);b.collapse(false);this.select();return this}if(c.isBlock()){for(var d=a=c.clone(true);d.first();)d=d.first();d.append(b.extractContents());b.insertNode(a[0]).selectNode(a[0]);return this}b.enlarge();var e=b.createBookmark();a=b.commonAncestor();
var f=false;j(a).scan(function(i){if(!f&&i==e.start)f=true;else if(f){if(i==e.end)return false;var k=j(i);if(!Nc(k))if(k.type==3&&q(i.nodeValue).length>0){for(var n;(n=k.parent())&&n.isStyle()&&n.children().length==1;)k=n;Mc(k,c)}}});b.moveToBookmark(e);return this},split:function(a,b){for(var c=this.range,d=c.doc,e=c.cloneRange().collapse(a),f=e.startContainer,i=e.startOffset,k=f.nodeType==3?f.parentNode:f,n=false,s;k&&k.parentNode;){s=j(k);if(b){if(!s.isStyle())break;if(!Cb(s,b))break}else if(qb[s.name])break;
n=true;k=k.parentNode}if(n){b=d.createElement("span");c.cloneRange().collapse(!a).insertNode(b);a?e.setStartBefore(k.firstChild).setEnd(f,i):e.setStart(f,i).setEndAfter(k.lastChild);f=e.extractContents();i=f.firstChild;d=f.lastChild;if(a){e.insertNode(f);c.setStartAfter(d).setEndBefore(b)}else{k.appendChild(f);c.setStartBefore(b).setEndBefore(i)}e=b.parentNode;if(e==c.endContainer){k=j(b).prev();f=j(b).next();if(k&&f&&k.type==3&&f.type==3)c.setEnd(k[0],k[0].nodeValue.length);else a||c.setEnd(c.endContainer,
c.endOffset-1)}e.removeChild(b)}return this},remove:function(a,b){var c=this,d=c.doc,e=c.range;e.enlarge();if(e.startOffset===0){for(var f=j(e.startContainer),i;(i=f.parent())&&i.isStyle()&&i.children().length==1;)f=i;e.setStart(f[0],0);f=j(e.startContainer);f.isBlock()&&Fa(f,a);(f=f.parent())&&f.isBlock()&&Fa(f,a)}if(e.collapsed){c.split(true,a);d=e.startContainer;f=e.startOffset;if(f>0)if((i=j(d.childNodes[f-1]))&&xa(i)){i.remove();e.setStart(d,f-1)}(f=j(d.childNodes[f]))&&xa(f)&&f.remove();if(xa(d)){e.startBefore(d);
d.remove()}e.collapse(true);return c}c.split(true,a);c.split(false,a);var k=d.createElement("span"),n=d.createElement("span");e.cloneRange().collapse(false).insertNode(n);e.cloneRange().collapse(true).insertNode(k);var s=[],w=false;j(e.commonAncestor()).scan(function(K){if(!w&&K==k)w=true;else{if(K==n)return false;w&&s.push(K)}});j(k).remove();j(n).remove();d=e.startContainer;f=e.startOffset;i=e.endContainer;var A=e.endOffset;if(f>0){var y=j(d.childNodes[f-1]);if(y&&xa(y)){y.remove();e.setStart(d,
f-1);d==i&&e.setEnd(i,A-1)}if((f=j(d.childNodes[f]))&&xa(f)){f.remove();d==i&&e.setEnd(i,A-1)}}(d=j(i.childNodes[e.endOffset]))&&xa(d)&&d.remove();d=e.createBookmark(true);l(s,function(K,x){j(x).type!==1&&b!=h&&c.removeformatTag(b);Fa(j(x),a)});e.moveToBookmark(d);return c},commonNode:function(a){function b(i){var k=i;for(i=i;i;){if(Cb(j(i),a))return j(i);i=i.parentNode}for(;k&&(k=k.lastChild);)if(Cb(j(k),a))return j(k);return null}var c=this.range,d=c.endContainer;c=c.endOffset;var e=d.nodeType==
3||c===0?d:d.childNodes[c-1],f=b(e);if(f)return f;if(e.nodeType==1||d.nodeType==3&&c===0)if(d=j(e).prev())return b(d);return null},commonAncestor:function(a){function b(i){for(;i;){if(i.nodeType==1)if(i.tagName.toLowerCase()===a)return i;i=i.parentNode}return null}var c=this.range,d=c.startContainer,e=c.startOffset,f=c.endContainer;c=c.endOffset;f=f.nodeType==3||c===0?f:f.childNodes[c-1];d=b(d.nodeType==3||e===0?d:d.childNodes[e-1]);e=b(f);if(d&&e&&d===e)return j(d);return null},state:function(a){var b=
this.doc,c=false;try{c=b.queryCommandState(a)}catch(d){}return c},val:function(a){function b(e){return e.toLowerCase()}var c=this.doc;a=b(a);var d="";if(a==="fontfamily"||a==="fontname"){d=bc(c,"fontname");d=d.replace(/['"]/g,"");return b(d)}if(a==="formatblock"){d=bc(c,a);if(d==="")if(a=this.commonNode({"h1,h2,h3,h4,h5,h6,p,div,pre,address":"*"}))d=a.name;if(d==="Normal")d="p";return b(d)}if(a==="fontsize"){if(a=this.commonNode({"*":".font-size"}))d=a.css("font-size");return b(d)}if(a==="forecolor"){if(a=
this.commonNode({"*":".color"}))d=a.css("color");d=Q(d);if(d==="")d="default";return b(d)}if(a==="hilitecolor"){if(a=this.commonNode({"*":".background-color"}))d=a.css("background-color");d=Q(d);if(d==="")d="default";return b(d)}return d},toggle:function(a,b){var c=this.range,d=this.doc;if(this.commonNode(b)){a=j(a,d).name;this.remove(b,a);if(c.collapsed){b=j("<span></span>",d);b.html("&nbsp;");c.insertNode(b[0]).selectNodeContents(b[0]);this.removeformatTag(a);b.html("")}}else this.wrap(a);return this.select()},
bold:function(){return this.toggle("<strong></strong>",{span:".font-weight=bold",strong:"*",b:"*"})},italic:function(){return this.toggle("<em></em>",{span:".font-style=italic",em:"*",i:"*"})},underline:function(){return this.toggle("<u></u>",{span:".text-decoration=underline",u:"*"})},strikethrough:function(){return this.toggle("<s></s>",{span:".text-decoration=line-through",s:"*"})},forecolor:function(a){a==""&&this.removeformatColor(".color");return this.wrap('<span style="color:'+a+';"></span>').select()},
hilitecolor:function(a){a==""&&this.removeformatColor(".background-color");return this.wrap('<span style="background-color:'+a+';"></span>').select()},fontsize:function(a){return this.wrap('<span style="font-size:'+a+';"></span>').select()},fontname:function(a){return this.fontfamily(a)},fontfamily:function(a){return this.wrap('<span style="font-family:'+a+';"></span>').select()},removeformat:function(){if(!this.range.collapsed){var a={"*":".font-weight,.font-style,.text-decoration,.color,.background-color,.font-size,.font-family,.text-indent"};
l(sc,function(b){a[b]="*"});this.remove(a);return this.select()}},removeformatTag:function(a){var b={"*":".font-weight,.font-style,.text-decoration,.font-size,.font-family,.text-indent"};a=J(a);l(a,function(A){b[A]="*"});var c=this.doc;a=this.range;a.enlarge();if(a.startOffset===0){for(var d=j(a.startContainer),e;(e=d.parent())&&e.isStyle()&&e.children().length==1;)d=e;a.setStart(d[0],0);d=j(a.startContainer);d.isBlock()&&Fa(d,b);(e=d.parent())&&e.isBlock()&&Fa(e,b)}var f;this.split(true,b);this.split(false,
b);var i=c.createElement("span"),k=c.createElement("span");a.cloneRange().collapse(false).insertNode(k);a.cloneRange().collapse(true).insertNode(i);var n=[],s=false;j(a.commonAncestor()).scan(function(A){if(!s&&A==i)s=true;else{if(A==k)return false;s&&n.push(A)}});j(i).remove();j(k).remove();e=a.startContainer;f=a.startOffset;c=a.endContainer;d=a.endOffset;if(f>0){var w=j(e.childNodes[f-1]);if(w&&xa(w)){w.remove();a.setStart(e,f-1);e==c&&a.setEnd(c,d-1)}if((f=j(e.childNodes[f]))&&xa(f)){f.remove();
e==c&&a.setEnd(c,d-1)}}(e=j(c.childNodes[a.endOffset]))&&xa(e)&&e.remove();e=a.createBookmark(true);l(n,function(A,y){Fa(j(y),b)});a.moveToBookmark(e);return this.select()},removeformatColor:function(a){var b={"*":a},c=J("span");l(c,function(y){b[y]=a.substr(1)});var d=this.doc;c=this.range;c.enlarge();if(c.startOffset===0){for(var e=j(c.startContainer),f;(f=e.parent())&&f.isStyle()&&f.children().length==1;)e=f;c.setStart(e[0],0);e=j(c.startContainer);e.isBlock()&&Fa(e,b);(f=e.parent())&&f.isBlock()&&
Fa(f,b)}var i;this.split(true,b);this.split(false,b);var k=d.createElement("span"),n=d.createElement("span");c.cloneRange().collapse(false).insertNode(n);c.cloneRange().collapse(true).insertNode(k);var s=[],w=false;j(c.commonAncestor()).scan(function(y){if(!w&&y==k)w=true;else{if(y==n)return false;w&&s.push(y)}});j(k).remove();j(n).remove();d=c.startContainer;i=c.startOffset;f=c.endContainer;e=c.endOffset;if(i>0){var A=j(d.childNodes[i-1]);if(A&&xa(A)){A.remove();c.setStart(d,i-1);d==f&&c.setEnd(f,
e-1)}if((i=j(d.childNodes[i]))&&xa(i)){i.remove();d==f&&c.setEnd(f,e-1)}}(d=j(f.childNodes[c.endOffset]))&&xa(d)&&d.remove();d=c.createBookmark(true);l(s,function(y,K){Fa(j(K),b)});c.moveToBookmark(d);return this.select()},inserthtml:function(a,b){function c(k,n){n='<img id="__kindeditor_temp_tag__" width="0" height="0" style="display:none;" />'+n;var s=k.get();if(s.item)s.item(0).outerHTML=n;else s.pasteHTML(n);n=k.doc.getElementById("__kindeditor_temp_tag__");n.parentNode.removeChild(n);s=ac(s);
k.setEnd(s.endContainer,s.endOffset);k.collapse(false);e.select(false)}function d(k,n){var s=k.doc,w=s.createDocumentFragment();j("@"+n,s).each(function(){w.appendChild(this)});k.deleteContents();k.insertNode(w);k.collapse(false);e.select(false)}var e=this,f=e.range;if(a==="")return e;if(za&&b){try{c(f,a)}catch(i){d(f,a)}return e}d(f,a);return e},hr:function(){return this.inserthtml("<hr />")},print:function(){this.win.print();return this},insertimage:function(a,b,c,d,e,f){b=B(b,"");B(e,0);a='<img src="'+
H(a)+'" data-ke-src="'+H(a)+'" ';if(c)a+='width="'+H(c)+'" ';if(d)a+='height="'+H(d)+'" ';if(b)a+='title="'+H(b)+'" ';if(f)a+='align="'+H(f)+'" ';a+='alt="'+H(b)+'" ';a+="/>";return this.inserthtml(a)},createlink:function(a,b){function c(n,s,w){j(n).attr("href",s).attr("data-ke-src",s);w?j(n).attr("target",w):j(n).removeAttr("target")}var d=this.doc,e=this.range;this.select();var f=this.commonNode({a:"*"});if(f&&!e.isControl()){e.selectNode(f.get());this.select()}f='<a href="'+H(a)+'" data-ke-src="'+
H(a)+'" ';if(b)f+=' target="'+H(b)+'"';if(e.collapsed){f+=">"+H(a)+"</a>";return this.inserthtml(f)}if(e.isControl()){var i=j(e.startContainer.childNodes[e.startOffset]);f+="></a>";i.after(j(f,d));i.next().append(i);e.selectNode(i[0]);return this.select()}f=e.startContainer;i=e.startOffset;var k=e.endContainer;e=e.endOffset;if(f.nodeType==1&&f===k&&i+1===e){e=f.childNodes[i];if(e.nodeName.toLowerCase()=="a"){c(e,a,b);return this}}Qa(d,"createlink","__kindeditor_temp_url__");j('a[href="__kindeditor_temp_url__"]',
d).each(function(){c(this,a,b)});return this},unlink:function(){var a=this.doc,b=this.range;this.select();if(b.collapsed){var c=this.commonNode({a:"*"});if(c){b.selectNode(c.get());this.select()}Qa(a,"unlink",null);if(Ra&&j(b.startContainer).name==="img"){a=j(b.startContainer).parent();a.name==="a"&&a.remove(true)}}else Qa(a,"unlink",null);return this}});l("formatblock,selectall,justifyleft,justifycenter,justifyright,justifyfull,insertorderedlist,insertunorderedlist,indent,outdent,subscript,superscript".split(","),
function(a,b){Za.prototype[b]=function(c){this.select();Qa(this.doc,b,c);za&&o(b,"justifyleft,justifycenter,justifyright,justifyfull".split(","))>=0&&this.selection();if(!za||o(b,"formatblock,selectall,insertorderedlist,insertunorderedlist".split(","))>=0)this.selection();return this}});l("cut,copy,paste".split(","),function(a,b){Za.prototype[b]=function(){if(!this.doc.queryCommandSupported(b))throw"not supported";this.select();Qa(this.doc,b,null);return this}});j.CmdClass=Za;j.cmd=fc;G(Ka,{init:function(a){var b=
this;b.name=a.name||"";b.doc=a.doc||document;b.win=Pa(b.doc);b.x=r(a.x);b.y=r(a.y);b.z=a.z;b.width=r(a.width);b.height=r(a.height);b.div=j('<div style="display:block;"></div>');b.options=a;b._alignEl=a.alignEl;b.width&&b.div.css("width",b.width);b.height&&b.div.css("height",b.height);b.z&&b.div.css({position:"absolute",left:b.x,top:b.y,"z-index":b.z});if(b.z&&(b.x===h||b.y===h))b.autoPos(b.width,b.height);a.cls&&b.div.addClass(a.cls);a.shadowMode&&b.div.addClass("ke-shadow");a.css&&b.div.css(a.css);
a.src?j(a.src).replaceWith(b.div):j(b.doc.body).append(b.div);a.html&&b.div.html(a.html);if(a.autoScroll)if(ka&&qa<7||Ia){var c=Ta();j(b.win).bind("scroll",function(){var d=Ta(),e=d.x-c.x;d=d.y-c.y;b.pos(z(b.x)+e,z(b.y)+d,false)})}else b.div.css("position","fixed")},pos:function(a,b,c){c=B(c,true);if(a!==null){a=a<0?0:r(a);this.div.css("left",a);if(c)this.x=a}if(b!==null){b=b<0?0:r(parseInt(b));this.div.css("top",b);if(c)this.y=b}return this},autoPos:function(a,b){var c;c=z(a)||0;a=z(b)||0;b=Ta();
if(this._alignEl){var d=j(this._alignEl),e=d.pos();c=Ja(d[0].clientWidth/2-c/2);a=Ja(d[0].clientHeight/2-a/2);c=c<0?e.x:e.x+c;a=a<0?e.y:e.y+a}else{e=ya(this.doc);c=Ja(b.x+(e.clientWidth-c)/2);a=Ja(b.y+(e.clientHeight-a)/2)}if(!(ka&&qa<7||Ia)){c-=b.x;a-=b.y}return this.pos(c,a)},remove:function(){var a=this;if(ka&&qa<7||Ia)j(a.win).unbind("scroll");a.div.remove();l(a,function(b){a[b]=null});return this},show:function(){this.div.show();return this},hide:function(){this.div.hide();return this},draggable:function(a){var b=
this;a=a||{};a.moveEl=b.div;a.moveFn=function(c,d,e,f,i,k){if((c+=i)<0)c=0;if((d+=k)<0)d=0;b.pos(c,d)};Eb(a);return b}});j.WidgetClass=Ka;j.widget=Gb;var Hb="";if(la=document.getElementsByTagName("html"))Hb=la[0].dir;G(ab,Ka,{init:function(a){function b(){var s=Fb(c.iframe);s.open();if(k)s.domain=document.domain;s.write(Oc(d,e,f,i));s.close();c.win=c.iframe[0].contentWindow;c.doc=s;var w=fc(s);c.afterChange(function(){w.selection()});Ra&&j(s).click(function(A){if(j(A.target).name==="img"){w.selection(true);
w.range.selectNode(A.target);w.select()}});if(ka){c._mousedownHandler=function(){var A=w.range.cloneRange();A.shrink();A.isControl()&&c.blur()};j(document).mousedown(c._mousedownHandler);j(s).keydown(function(A){if(A.which==8){w.selection();var y=w.range;if(y.isControl()){y.collapse(true);j(y.startContainer.childNodes[y.startOffset]).remove();A.preventDefault()}}})}c.cmd=w;c.html($a(c.srcElement));if(ka){s.body.disabled=true;s.body.contentEditable=true;s.body.removeAttribute("disabled")}else s.designMode=
"on";a.afterCreate&&a.afterCreate.call(c)}var c=this;ab.parent.init.call(c,a);c.srcElement=j(a.srcElement);c.div.addClass("ke-edit");c.designMode=B(a.designMode,true);c.beforeGetHtml=a.beforeGetHtml;c.beforeSetHtml=a.beforeSetHtml;c.afterSetHtml=a.afterSetHtml;var d=B(a.themesPath,""),e=a.bodyClass,f=a.cssPath,i=a.cssData,k=location.protocol!="res:"&&location.host.replace(/:\d+/,"")!==document.domain,n="document.open();"+(k?'document.domain="'+document.domain+'";':"")+"document.close();";n=ka?' src="javascript:void(function(){'+
encodeURIComponent(n)+'}())"':"";c.iframe=j('<iframe class="ke-edit-iframe" hidefocus="true" frameborder="0"'+n+"></iframe>").css("width","100%");c.textarea=j('<textarea class="ke-edit-textarea" hidefocus="true"></textarea>').css("width","100%");c.tabIndex=isNaN(parseInt(a.tabIndex,10))?c.srcElement.attr("tabindex"):parseInt(a.tabIndex,10);c.iframe.attr("tabindex",c.tabIndex);c.textarea.attr("tabindex",c.tabIndex);c.width&&c.setWidth(c.width);c.height&&c.setHeight(c.height);c.designMode?c.textarea.hide():
c.iframe.hide();k&&c.iframe.bind("load",function(){c.iframe.unbind("load");ka?b():setTimeout(b,0)});c.div.append(c.iframe);c.div.append(c.textarea);c.srcElement.hide();!k&&b()},setWidth:function(a){this.width=a=r(a);this.div.css("width",a);return this},setHeight:function(a){this.height=a=r(a);this.div.css("height",a);this.iframe.css("height",a);if(ka&&qa<8||Ia)a=r(z(a)-2);this.textarea.css("height",a);return this},remove:function(){var a=this.doc;j(a.body).unbind();j(a).unbind();j(this.win).unbind();
this._mousedownHandler&&j(document).unbind("mousedown",this._mousedownHandler);$a(this.srcElement,this.html());this.srcElement.show();this.iframe.unbind();this.textarea.unbind();ab.parent.remove.call(this)},html:function(a,b){var c=this.doc;if(this.designMode){c=c.body;if(a===h){a=b?"<!doctype html><html>"+c.parentNode.innerHTML+"</html>":c.innerHTML;if(this.beforeGetHtml)a=this.beforeGetHtml(a);if(Sa&&a=="<br />")a="";return a}if(this.beforeSetHtml)a=this.beforeSetHtml(a);if(ka&&qa>=9)a=a.replace(/(<.*?checked=")checked(".*>)/ig,
"$1$2");j(c).html(a);this.afterSetHtml&&this.afterSetHtml();return this}if(a===h)return this.textarea.val();this.textarea.val(a);return this},design:function(a){if(a===h?!this.designMode:a){if(!this.designMode){a=this.html();this.designMode=true;this.textarea.hide();this.html(a);var b=this.iframe,c=z(this.height);b.height(c-2);b.show();setTimeout(function(){b.height(c)},0)}}else if(this.designMode){a=this.html();this.designMode=false;this.html(a);this.iframe.hide();this.textarea.show()}return this.focus()},
focus:function(){this.designMode?this.win.focus():this.textarea[0].focus();return this},blur:function(){if(ka){var a=j('<input type="text" style="float:left;width:0;height:0;padding:0;margin:0;border:0;" value="" />',this.div);this.div.append(a);a[0].focus();a.remove()}else this.designMode?this.win.blur():this.textarea[0].blur();return this},afterChange:function(a){function b(e){setTimeout(function(){a(e)},1)}var c=this.doc,d=c.body;j(c).keyup(function(e){!e.ctrlKey&&!e.altKey&&Mb[e.which]&&a(e)});
j(c).mouseup(a).contextmenu(a);j(this.win).blur(a);j(d).bind("paste",b);j(d).bind("cut",b);$(c).on("customChange",function(){a()});return this},trigger:function(){var a=this.doc;setTimeout(function(){$(a).trigger("customChange")},25)}});j.EditClass=ab;j.edit=gc;j.iframeDoc=Fb;G(lb,Ka,{init:function(a){function b(e){e=j(e);if(e.hasClass("ke-outline"))return e;if(e.hasClass("ke-toolbar-icon"))return e.parent()}function c(e,f){if(e=b(e.target))e.hasClass("ke-disabled")||e.hasClass("ke-selected")||e[f]("ke-on")}
var d=this;lb.parent.init.call(d,a);d.disableMode=B(a.disableMode,false);d.noDisableItemMap=J(B(a.noDisableItems,[]));d._itemMap={};d.div.addClass("ke-toolbar").bind("contextmenu,mousedown,mousemove",function(e){e.preventDefault()}).attr("unselectable","on");d.div.mouseover(function(e){c(e,"addClass")}).mouseout(function(e){c(e,"removeClass")}).click(function(e){var f=b(e.target);if(f)f.hasClass("ke-disabled")||d.options.click.call(this,e,f.attr("data-name"))})},get:function(a){if(this._itemMap[a])return this._itemMap[a];
return this._itemMap[a]=j("span.ke-icon-"+a,this.div).parent()},select:function(a){hc.call(this,a,function(b){b.addClass("ke-selected")});return self},unselect:function(a){hc.call(this,a,function(b){b.removeClass("ke-selected").removeClass("ke-on")});return self},enable:function(a){if(a=a.get?a:this.get(a)){a.removeClass("ke-disabled");a.opacity(1)}return this},disable:function(a){if(a=a.get?a:this.get(a)){a.removeClass("ke-selected").addClass("ke-disabled");a.opacity(0.5)}return this},disableAll:function(a,
b){var c=this,d=c.noDisableItemMap;if(b)d=J(b);if(a===h?!c.disableMode:a){j("span.ke-outline",c.div).each(function(){var e=j(this),f=e[0].getAttribute("data-name",2);d[f]||c.disable(e)});c.disableMode=true}else{j("span.ke-outline",c.div).each(function(){var e=j(this),f=e[0].getAttribute("data-name",2);d[f]||c.enable(e)});c.disableMode=false}return c}});j.ToolbarClass=lb;j.toolbar=ic;G(bb,Ka,{init:function(a){a.z=a.z||811213;bb.parent.init.call(this,a);this.centerLineMode=B(a.centerLineMode,true);
this.div.addClass("ke-menu").bind("click,mousedown",function(b){b.stopPropagation()}).attr("unselectable","on")},addItem:function(a){if(a.title==="-")this.div.append(j('<div class="ke-menu-separator"></div>'));else{var b=j('<div class="ke-menu-item" unselectable="on"></div>'),c=j('<div class="ke-inline-block ke-menu-item-left"></div>'),d=j('<div class="ke-inline-block ke-menu-item-right"></div>'),e=r(a.height),f=B(a.iconClass,"");this.div.append(b);if(e){b.css("height",e);d.css("line-height",e)}var i;
if(this.centerLineMode){i=j('<div class="ke-inline-block ke-menu-item-center"></div>');e&&i.css("height",e)}b.mouseover(function(){j(this).addClass("ke-menu-item-on");i&&i.addClass("ke-menu-item-center-on")}).mouseout(function(){j(this).removeClass("ke-menu-item-on");i&&i.removeClass("ke-menu-item-center-on")}).click(function(k){a.click.call(j(this));k.stopPropagation()}).append(c);i&&b.append(i);b.append(d);b="";if(a.checked)f="fa fa-checked ke-icon-checked";else if(f!=="")b=f.replace("ke-icon-",
"fa fa-");f!==""&&c.html('<span class="ke-inline-block ke-toolbar-icon ke-toolbar-icon-url '+b+" "+f+'"></span>');d.html(a.title);return this}},remove:function(){this.options.beforeRemove&&this.options.beforeRemove.call(this);j(".ke-menu-item",this.div[0]).unbind();bb.parent.remove.call(this);return this}});j.MenuClass=bb;j.menu=Ib;G(cb,Ka,{init:function(a){a.z=a.z||811213;cb.parent.init.call(this,a);var b=a.colors||[["#E53333","#E56600","#FF9900","#64451D","#DFC5A4","#FFE500"],["#009900","#006600",
"#99BB00","#B8D100","#60D978","#00D5FF"],["#337FE5","#003399","#4C33E5","#9933E5","#CC33E5","#EE33EE"],["#FFFFFF","#CCCCCC","#999999","#666666","#333333","#000000"]];this.selectedColor=(a.selectedColor||"").toLowerCase();this._cells=[];this.div.addClass("ke-colorpicker").bind("click,mousedown",function(i){i.stopPropagation()}).attr("unselectable","on");a=this.doc.createElement("table");this.div.append(a);a.className="ke-colorpicker-table";a.cellPadding=0;a.cellSpacing=0;a.border=0;var c=a.insertRow(0),
d=c.insertCell(0);d.colSpan=b[0].length;this._addAttr(d,"","ke-colorpicker-cell-top");for(var e=0;e<b.length;e++){c=a.insertRow(e+1);for(var f=0;f<b[e].length;f++){d=c.insertCell(f);this._addAttr(d,b[e][f],"ke-colorpicker-cell")}}},_addAttr:function(a,b,c){var d=this;a=j(a).addClass(c);d.selectedColor===b.toLowerCase()&&a.addClass("ke-colorpicker-cell-selected");a.attr("title",b||d.options.noColor);a.mouseover(function(){j(this).addClass("ke-colorpicker-cell-on")});a.mouseout(function(){j(this).removeClass("ke-colorpicker-cell-on")});
a.click(function(e){e.stop();d.options.click.call(j(this),b)});b?a.append(j('<div class="ke-colorpicker-cell-color" unselectable="on"></div>').css("background-color",b)):a.html(d.options.noColor);j(a).attr("unselectable","on");d._cells.push(a)},remove:function(){l(this._cells,function(){this.unbind()});cb.parent.remove.call(this);return this}});j.ColorPickerClass=cb;j.colorpicker=jc;G(Jb,{init:function(a){var b=j(a.button),c=a.fieldName||"file",d=a.url||"",e=b.val(),f=a.extraParams||{},i=b[0].className||
"",k=a.target||"kindeditor_upload_iframe_"+(new Date).getTime();if(a.uploadModule==10){a.afterError=a.afterError||function(w){j.popupMessage(w)};var n=[];c=['<div class="ke-inline-block '+i+'">',a.target?"":'<iframe name="'+k+'" style="display:none;"></iframe>',a.form?'<div class="ke-upload-area">':'<form class="ke-upload-area ke-form" method="post" enctype="multipart/form-data" target="'+k+'" action="'+d+'">','<span class="ke-button-common  ke-button-outer">',n.join(""),'<input type="button" class="ke-button-common ke-button" value="'+
e+'" />',"</span>",'<input type="file" class="ke-upload-file" name="'+c+'" tabindex="-1" />',a.form?"</div>":"</form>","</div>"].join("");c=j(c,b.doc);b.hide();b.before(c);this.div=c;this.button=b;this.iframe=a.target?j('iframe[name="'+k+'"]'):j("iframe",c);this.form=a.form?j(a.form):j("form",c);this.fileBox=j(".ke-upload-file",c);b=a.width||j(".ke-button-common",c).width()}else{if(a.uploadModule==20){a.afterError=a.afterError||function(w){j.popupMessage(w)};n=[]}else if(a.uploadModule==30){a.afterError=
a.afterError||function(w){j.popupMessage(w)};n=[]}else{a.afterError=a.afterError||function(w){j.popupMessage(w)};n=[];for(var s in f)n.push('<input type="hidden" name="'+s+'" value="'+f[s]+'" />')}c=['<div class="ke-inline-block '+i+'">',a.target?"":'<iframe name="'+k+'" style="display:none;"></iframe>',a.form?'<div class="ke-upload-area">':'<form class="ke-upload-area ke-form" method="post" enctype="multipart/form-data" target="'+k+'" action="'+d+'">','<span class="ke-button-common  ke-button-outer">',
n.join(""),'<input type="button" class="ke-button-common ke-button" value="'+e+'" />',"</span>",'<input type="file" class="ke-upload-file" name="'+c+'" tabindex="-1" />',a.form?"</div>":"</form>","</div>"].join("");c=j(c,b.doc);b.hide();b.before(c);this.div=c;this.button=b;this.iframe=a.target?j('iframe[name="'+k+'"]'):j("iframe",c);this.form=a.form?j(a.form):j("form",c);this.fileBox=j(".ke-upload-file",c);b=a.width||j(".ke-button-common",c).width()}j(".ke-upload-area",c).width(b);this.options=a},
submit:function(){var a=this,b=a.iframe;if(a.options.uploadModule==10){var c=a.fileBox[0].value.substring(a.fileBox[0].value.lastIndexOf("\\")+1),d=a.options.url||a.options.form[0].action,e="fileName="+encodeURIComponent(c);j.post_ajax(function(i){i=i.responseText;if(i!=""){i=JSON.parse(i);if(i.error==0){i=i.url;var k=i.substring(0,i.indexOf("?")),n=i.substring(i.indexOf("?")+1,i.length),s="";i=new FormData;n=n.split("&");for(var w=0;w<n.length;w++){var A=n[w].split("=");i.append(A[0],decodeURIComponent(A[1]));
if(A[0]=="key")s=decodeURIComponent(A[1])}i.append("file",a.fileBox[0].files[0]);j.post_ajax(function(y){if(y.status==200){j.popupMessage(KindEditor.lang("uploadSuccess"));y=document.createElement("form");a.fileBox.before(y);j(y).append(a.fileBox);y.reset();j(y).remove(true);y=j.iframeDoc(b);var K=y.getElementsByTagName("pre")[0],x="";x=K?K.innerHTML:y.body.innerHTML;C(x);y={};y.error=0;y.url=k+s;y.title=c;b[0].src="javascript:false";b.unbind();a.options.afterUpload.call(a,y)}},k,true,i)}else j.popupMessage(i.message)}},
d+"&"+e,true,"",true)}else if(a.options.uploadModule==20){c=a.fileBox[0].value.substring(a.fileBox[0].value.lastIndexOf("\\")+1);d=a.options.url||a.options.form[0].action;e="fileName="+encodeURIComponent(c);j.post_ajax(function(i){i=i.responseText;if(i!=""){i=JSON.parse(i);if(i.error==0){var k=i.url;i=k.substring(0,k.indexOf("?"));var n=k.substring(k.indexOf("?")+1,k.length);k=new FormData;n=n.split("&");for(var s=0;s<n.length;s++){var w=n[s].split("=");k.append(w[0],decodeURIComponent(w[1]))}k.append("file",
a.fileBox[0].files[0]);j.post_ajax(function(A){var y=A.getResponseHeader("etag");A=A.getResponseHeader("location");if(y!=h&&A!=h){j.popupMessage(KindEditor.lang("uploadSuccess"));y=document.createElement("form");a.fileBox.before(y);j(y).append(a.fileBox);y.reset();j(y).remove(true);y=j.iframeDoc(b);var K=y.getElementsByTagName("pre")[0],x="";x=K?K.innerHTML:y.body.innerHTML;C(x);y={};y.error=0;y.url=A;y.title=c;b[0].src="javascript:false";b.unbind();a.options.afterUpload.call(a,y)}},i,true,k)}else j.popupMessage(i.message)}},
d+"&"+e,true,"",true)}else if(a.options.uploadModule==30){c=a.fileBox[0].value.substring(a.fileBox[0].value.lastIndexOf("\\")+1);d=a.options.url||a.options.form[0].action;e="fileName="+encodeURIComponent(c);j.post_ajax(function(i){i=i.responseText;if(i!=""){i=JSON.parse(i);if(i.error==0){i=i.url;var k=i.substring(0,i.indexOf("?")),n=i.substring(i.indexOf("?")+1,i.length),s="";i=new FormData;n=n.split("&");for(var w=0;w<n.length;w++){var A=n[w].split("=");i.append(A[0],decodeURIComponent(A[1]));if(A[0]==
"key")s=decodeURIComponent(A[1])}i.append("file",a.fileBox[0].files[0]);j.post_ajax(function(y){if(y.status==200){j.popupMessage(KindEditor.lang("uploadSuccess"));y=document.createElement("form");a.fileBox.before(y);j(y).append(a.fileBox);y.reset();j(y).remove(true);y=j.iframeDoc(b);var K=y.getElementsByTagName("pre")[0],x="";x=K?K.innerHTML:y.body.innerHTML;C(x);y={};y.error=0;y.url=k+s;y.title=c;b[0].src="javascript:false";b.unbind();a.options.afterUpload.call(a,y)}},k,true,i)}else j.popupMessage(i.message)}},
d+"&"+e,true,"",true)}else{c=a.fileBox[0].value.substring(a.fileBox[0].value.lastIndexOf("\\")+1);d=a.options.url||a.options.form[0].action;e="fileName="+encodeURIComponent(c);var f=new FormData;f.append(a.options.fieldName,a.fileBox[0].files[0]);j.post_ajax(function(i){i=i.responseText;if(i!=""){i=JSON.parse(i);if(i.error==0){j.popupMessage(KindEditor.lang("uploadSuccess"));var k=document.createElement("form");a.fileBox.before(k);j(k).append(a.fileBox);k.reset();j(k).remove(true);k=j.iframeDoc(b);
var n=k.getElementsByTagName("pre")[0],s="";s=n?n.innerHTML:k.body.innerHTML;C(s);k={};k.error=0;k.url=i.url;k.title=i.title;b[0].src="javascript:false";b.unbind();a.options.afterUpload.call(a,k)}else j.popupMessage(i.message)}},d+"&"+e,true,f,true)}return a},remove:function(){this.fileBox&&this.fileBox.unbind();this.iframe.remove();this.div.remove();this.button.show();return this}});j.UploadButtonClass=Jb;j.uploadbutton=Pc;G(db,Ka,{init:function(a){var b=B(a.shadowMode,true);a.z=a.z||811213;a.shadowMode=
false;a.autoScroll=B(a.autoScroll,true);db.parent.init.call(this,a);var c=a.title,d=j(a.body,this.doc),e=a.previewBtn,f=a.yesBtn,i=a.noBtn,k=a.closeBtn,n=B(a.showMask,true);this.div.addClass("ke-dialog").bind("click,mousedown",function(A){A.stopPropagation()});var s=j('<div class="ke-dialog-content"></div>').appendTo(this.div);if(ka&&qa<7)this.iframeMask=j('<iframe src="about:blank" class="ke-dialog-shadow"></iframe>').appendTo(this.div);else b&&j('<div class="ke-dialog-shadow"></div>').appendTo(this.div);
b=j('<div class="ke-dialog-header"></div>');s.append(b);b.html(c);this.closeIcon=j('<span class="ke-dialog-icon-close fa fa-close" title="'+k.name+'"></span>').click(k.click);b.append(this.closeIcon);this.draggable({clickEl:b,beforeDrag:a.beforeDrag});a=j('<div class="ke-dialog-body"></div>');s.append(a);a.append(d);var w=j('<div class="ke-dialog-footer"></div>');if(e||f||i)s.append(w);l([{btn:e,name:"preview"},{btn:f,name:"yes"},{btn:i,name:"no"}],function(){if(this.btn){var A=Qc(this.btn);A.addClass("ke-dialog-"+
this.name);w.append(A)}});this.height&&a.height(z(this.height)-b.height()-w.height());this.div.width(this.div.width());this.div.height(this.div.height());this.mask=null;if(n){e=ya(this.doc);d=Math.max(e.scrollWidth,e.clientWidth);e=Math.max(e.scrollHeight,e.clientHeight);this.mask=Gb({x:0,y:0,z:this.z-1,cls:"ke-dialog-mask",width:d,height:e})}this.autoPos(this.div.width(),this.div.height());this.footerDiv=w;this.bodyDiv=a;this.headerDiv=b;this.isLoading=false},setMaskIndex:function(a){this.mask.div.css("z-index",
a)},showLoading:function(a){a=B(a,"");var b=this.bodyDiv;this.loading=j('<div class="ke-dialog-loading"><div class="ke-inline-block ke-dialog-loading-content" style="margin-top:'+Math.round(b.height()/3)+'px;">'+a+"</div></div>").width(b.width()).height(b.height()).css("top",this.headerDiv.height()+"px");b.css("visibility","hidden").after(this.loading);this.isLoading=true;return this},hideLoading:function(){this.loading&&this.loading.remove();this.bodyDiv.css("visibility","visible");this.isLoading=
false;return this},remove:function(){this.options.beforeRemove&&this.options.beforeRemove.call(this);this.mask&&this.mask.remove();this.iframeMask&&this.iframeMask.remove();this.closeIcon.unbind();j("input",this.div).unbind();j("button",this.div).unbind();this.footerDiv.unbind();this.bodyDiv.unbind();this.headerDiv.unbind();j("iframe",this.div).each(function(){j(this).remove()});db.parent.remove.call(this);return this}});j.DialogClass=db;j.dialog=kc;j.tabs=Rc;j.loadScript=Kb;j.loadStyle=Lb;j.ajax=
Sc;var Ga={},Ha={};eb.prototype={lang:function(a){return oc(a,this.langType)},loadPlugin:function(a,b){var c=this,d=this._pluginStatus;if(!d)d=this._pluginStatus={};if(Ga[a]){if(!t(Ga[a])){setTimeout(function(){c.loadPlugin(a,b)},100);return c}if(!d[a]){Ga[a].call(c,KindEditor);d[a]="inited"}b&&b.call(c);return c}Ga[a]="loading";Kb(c.pluginsPath+a+"/"+a+".js?ver="+encodeURIComponent(j.DEBUG?ob:pb),function(){setTimeout(function(){Ga[a]&&c.loadPlugin(a,b)},0)});return c},handler:function(a,b){var c=
this;c._handlers[a]||(c._handlers[a]=[]);if(t(b)){c._handlers[a].push(b);return c}l(c._handlers[a],function(){b=this.call(c,b)});return b},clickToolbar:function(a,b){var c=this,d="clickToolbar"+a;if(b===h){if(c._handlers[d])return c.handler(d);c.loadPlugin(a,function(){c.handler(d)});return c}return c.handler(d,b)},updateState:function(){var a=this;l("justifyleft,justifycenter,justifyright,justifyfull,insertorderedlist,insertunorderedlist,subscript,superscript,bold,italic,underline,strikethrough".split(","),
function(b,c){a.cmd.state(c)?a.toolbar.select(c):a.toolbar.unselect(c)});return a},addContextmenu:function(a){this._contextmenus.push(a);return this},afterCreate:function(a){return this.handler("afterCreate",a)},beforeRemove:function(a){return this.handler("beforeRemove",a)},beforeGetHtml:function(a){return this.handler("beforeGetHtml",a)},beforeSetHtml:function(a){return this.handler("beforeSetHtml",a)},afterSetHtml:function(a){return this.handler("afterSetHtml",a)},create:function(){function a(){n.height()===
0?setTimeout(a,100):b.resize(d,e,false)}var b=this,c=b.fullscreenMode;if(b.isCreated)return b;if(b.srcElement.data("kindeditor"))return b;b.srcElement.data("kindeditor","true");if(c)ya().style.overflow="hidden";else ya().style.overflow="";var d=c?ya().clientWidth+"px":b.width,e=c?ya().clientHeight+"px":b.height;if(ka&&qa<8||Ia)e=r(z(e)+2);var f=b.container=j(b.layout);c?j(document.body).append(f):b.srcElement.before(f);var i=j(".toolbar",f),k=j(".edit",f),n=b.statusbar=j(".statusbar",f);f.removeClass("container").addClass("ke-container ke-container-"+
b.themeType).css("width",d);if(c){f.css({position:"absolute",left:0,top:0,"background-color":"#fcfcfc","z-index":811211});if(!Sa)b._scrollPos=Ta();g.scrollTo(0,0);j(document.body).css({height:"1px",overflow:"hidden"});j(document.body.parentNode).css("overflow","hidden");b._fullscreenExecuted=true;b.fixToolBar&&stickybits(".ke-toolbar").cleanup()}else{f.css({"background-color":""});if(b._fullscreenExecuted){j(document.body).css({height:"",overflow:""});j(document.body.parentNode).css("overflow","")}b._scrollPos&&
g.scrollTo(b._scrollPos.x,b._scrollPos.y);j.loadScript(j.options.basePath+"stickybits.min.js",function(){setTimeout(function(){if(b.fixToolBar)document.querySelector(".el-main")?stickybits(".ke-toolbar",{scrollEl:".el-main",stickyBitStickyOffset:-20}):stickybits(".ke-toolbar",{stickyBitStickyOffset:-1})},0)})}var s=[];j.each(b.items,function(y,K){var x=false;j.each(b.noToolbarItems,function(L,ca){if(K==ca)x=true});if(x==false)if(K=="|")s.push('<span class="ke-inline-block ke-separator"></span>');
else if(K=="/")s.push('<div class="ke-hr"></div>');else{s.push('<span class="ke-outline" data-name="'+K+'" title="'+b.lang(K)+'" unselectable="on">');s.push('<span class="ke-toolbar-icon ke-toolbar-icon-url ke-icon-'+K+" fa fa-"+K+'" unselectable="on"></span></span>')}});i=b.toolbar=ic({src:i,html:s.join(""),noDisableItems:b.noDisableItems,click:function(y,K){y.stop();if(b.menu){y=b.menu.name;b.hideMenu();if(y===K)return}b.clickToolbar(K)}});var w=z(e)-i.div.height(),A=b.edit=gc({height:w>0&&z(e)>
b.minHeight?w:b.minHeight,src:k,srcElement:b.srcElement,designMode:b.designMode,themesPath:b.themesPath,bodyClass:b.bodyClass,cssPath:b.cssPath,cssData:b.cssData,beforeGetHtml:function(y){y=b.beforeGetHtml(y);y=La(nb(y));return Aa(y,b.filterMode?b.htmlTags:null,b.urlType,b.wellFormatMode,b.indentChar)},beforeSetHtml:function(y){y=Aa(y,b.filterMode?b.htmlTags:null,"",false);return b.beforeSetHtml(y)},afterSetHtml:function(){b.edit=A=this;b.afterSetHtml()},afterCreate:function(){b.edit=A=this;b.cmd=
A.cmd;b._docMousedownFn=function(){b.menu&&b.hideMenu()};j(A.doc,document).mousedown(b._docMousedownFn);Tc.call(b);Uc.call(b);Vc.call(b);Wc.call(b);A.afterChange(function(){if(A.designMode){b.updateState();b.addBookmark();b.options.afterChange&&b.options.afterChange.call(b)}});A.textarea.keyup(function(K){!K.ctrlKey&&!K.altKey&&tc[K.which]&&b.options.afterChange&&b.options.afterChange.call(b)});b.readonlyMode&&b.readonly();b.isCreated=true;if(b.initContent==="")b.initContent=b.html();if(b._undoStack.length>
0){var y=b._undoStack.pop();if(y.start){b.html(y.html);A.cmd.range.moveToBookmark(y);b.select()}}b.afterCreate();b.options.afterCreate&&b.options.afterCreate.call(b)}});n.removeClass("statusbar").addClass("ke-statusbar").append('<span class="ke-inline-block ke-statusbar-center-icon fa fa-angle-down"></span>').append('<span class="ke-inline-block ke-statusbar-right-icon fa fa-angle-rightBottomCorner"></span>');if(b._fullscreenResizeHandler){j(g).unbind("resize",b._fullscreenResizeHandler);b._fullscreenResizeHandler=
null}a();if(c){b._fullscreenResizeHandler=function(){b.isCreated&&b.resize(ya().clientWidth,ya().clientHeight,false)};j(g).bind("resize",b._fullscreenResizeHandler);i.select("fullscreen");n.first().css("visibility","hidden");n.last().css("visibility","hidden")}else{Sa&&j(g).bind("scroll",function(){b._scrollPos=Ta()});b.resizeType>0?Eb({moveEl:f,clickEl:n,moveFn:function(y,K,x,L,ca,F){L+=F;b.resize(null,L)}}):n.first().css("visibility","hidden");b.resizeType===2?Eb({moveEl:f,clickEl:n.last(),moveFn:function(y,
K,x,L,ca,F){x+=ca;L+=F;b.resize(x,L)}}):n.last().css("visibility","hidden")}return b},remove:function(){var a=this;if(!a.isCreated)return a;a.beforeRemove();a.srcElement.data("kindeditor","");a.menu&&a.hideMenu();l(a.dialogs,function(){a.hideDialog()});j(document).unbind("mousedown",a._docMousedownFn);a.toolbar.remove();a.edit.remove();a.statusbar.last().unbind();a.statusbar.unbind();a.container.remove();a.container=a.toolbar=a.edit=a.menu=null;a.dialogs=[];a.isCreated=false;return a},resize:function(a,
b,c){c=B(c,true);if(a){if(!/%/.test(a)){a=z(a);a=a<this.minWidth?this.minWidth:a}this.container.css("width",r(a));if(c)this.width=r(a)}if(b){b=z(b);a=z(b)-this.toolbar.div.height()-this.statusbar.height();a=a<this.minHeight?this.minHeight:a;this.edit.setHeight(a);if(c)this.height=r(b)}return this},select:function(){this.isCreated&&this.cmd.select();return this},html:function(a){if(a===h)return this.isCreated?this.edit.html():$a(this.srcElement);this.isCreated?this.edit.html(a):$a(this.srcElement,
a);this.isCreated&&this.cmd.selection();return this},bodyHtml:function(){return this.edit.doc.body.innerHTML},fullHtml:function(){return this.isCreated?this.edit.html(h,true):""},text:function(a){return a===h?q(this.html().replace(/<(?!img|video|iframe).*?>/ig,"").replace(/&nbsp;/ig," ")):this.html(H(a))},isEmpty:function(){return q(this.text().replace(/\r\n|\n|\r/,""))===""},isDirty:function(){return q(this.initContent.replace(/\r\n|\n|\r|t/g,""))!==q(this.html().replace(/\r\n|\n|\r|t/g,""))},selectedHtml:function(){var a=
this.isCreated?this.cmd.range.html():"";return a=La(nb(a))},count:function(a){a=(a||"html").toLowerCase();if(a==="html")return this.html().length;if(a==="text")return this.text().replace(/<(?:img|video|iframe).*?>/ig,"K").replace(/\r\n|\n|\r/g,"").length;return 0},exec:function(a){a=a.toLowerCase();var b=this.cmd,c=o(a,"selectall,copy,paste,print".split(","))<0;c&&this.addBookmark(false);b[a].apply(b,da(arguments,1));if(c){this.updateState();this.addBookmark(false);this.options.afterChange&&this.options.afterChange.call(this)}return this},
insertHtml:function(a,b){if(!this.isCreated)return this;a=this.beforeSetHtml(a);this.exec("inserthtml",a,b);return this},appendHtml:function(a){this.html(this.html()+a);if(this.isCreated){a=this.cmd;a.range.selectNodeContents(a.doc.body).collapse(false);a.select()}return this},sync:function(){$a(this.srcElement,this.html());return this},autoExpandContent:function(){var a=this;setTimeout(function(){a.edit.trigger()},100);j("img",a.cmd.doc.body).each(function(){var b=new Image;b.src=this.src;b.onload=
function(){a.edit.trigger()}});return this},focus:function(){this.isCreated?this.edit.focus():this.srcElement[0].focus();return this},blur:function(){this.isCreated?this.edit.blur():this.srcElement[0].blur();return this},addBookmark:function(a){a=B(a,true);var b=this.edit,c=b.doc.body,d=nb(c.innerHTML);if(a&&this._undoStack.length>0)if(Math.abs(d.length-La(this._undoStack[this._undoStack.length-1].html).length)<this.minChangeSize)return this;if(b.designMode&&!this._firstAddBookmark){b=this.cmd.range;
a=b.createBookmark(true);a.html=nb(c.innerHTML);b.moveToBookmark(a)}else a={html:d};this._firstAddBookmark=false;pc(this._undoStack,a);return this},undo:function(){return qc.call(this,this._undoStack,this._redoStack)},redo:function(){return qc.call(this,this._redoStack,this._undoStack)},fullscreen:function(a){this.fullscreenMode=a===h?!this.fullscreenMode:a;this.addBookmark(false);return this.remove().create()},readonly:function(a){a=B(a,true);var b=this,c=b.edit,d=c.doc;b.designMode?b.toolbar.disableAll(a,
[]):l(b.noDisableItems,function(){b.toolbar[a?"disable":"enable"](this)});if(ka)d.body.contentEditable=!a;else d.designMode=a?"off":"on";c.textarea[0].disabled=a},createMenu:function(a){var b=this.toolbar.get(a.name),c=b.pos();a.x=c.x;a.y=c.y+b.height();a.z=this.options.zIndex;a.shadowMode=B(a.shadowMode,this.shadowMode);if(a.selectedColor!==h){a.cls="ke-colorpicker-"+this.themeType;a.noColor=this.lang("noColor");this.menu=jc(a)}else{a.cls="ke-menu-"+this.themeType;a.centerLineMode=false;this.menu=
Ib(a)}return this.menu},hideMenu:function(){this.menu.remove();this.menu=null;return this},hideContextmenu:function(){this.contextmenu.remove();this.contextmenu=null;return this},createDialog:function(a){var b=this;a.z=b.options.zIndex;a.shadowMode=B(a.shadowMode,b.shadowMode);a.closeBtn=B(a.closeBtn,{name:b.lang("close"),click:function(){b.hideDialog();ka&&b.cmd&&b.cmd.select()}});a.noBtn=B(a.noBtn,{name:b.lang(a.yesBtn?"no":"close"),click:function(){b.hideDialog();ka&&b.cmd&&b.cmd.select()}});if(b.dialogAlignType!=
"page")a.alignEl=b.container;a.cls="ke-dialog-"+b.themeType;if(b.dialogs.length>0){var c=b.dialogs[b.dialogs.length-1];b.dialogs[0].setMaskIndex(c.z+2);a.z=c.z+3;a.showMask=false}a=kc(a);b.dialogs.push(a);return a},hideDialog:function(){this.dialogs.length>0&&this.dialogs.pop().remove();this.dialogs.length>0&&this.dialogs[0].setMaskIndex(this.dialogs[this.dialogs.length-1].z-1);return this},errorDialog:function(a){var b=this.createDialog({width:750,title:this.lang("uploadError"),body:'<div style="padding:10px 20px;"><iframe frameborder="0" style="width:708px;height:400px;"></iframe></div>'});
b=j("iframe",b.div);var c=j.iframeDoc(b);c.open();c.write(a);c.close();j(c.body).css("background-color","#FFF");b[0].contentWindow.focus();return this}};var fb=[];j.remove=function(a){gb(a,function(b){this.remove();fb.splice(b,1)})};j.sync=function(a){gb(a,function(){this.sync()})};j.html=function(a,b){gb(a,function(){this.html(b)})};j.insertHtml=function(a,b){gb(a,function(){this.insertHtml(b)})};j.appendHtml=function(a,b){gb(a,function(){this.appendHtml(b)})};ka&&qa<7&&Qa(document,"BackgroundImageCache",
true);j.EditorClass=eb;j.editor=Zc;j.create=rc;j.instances=fb;j.plugin=mc;j.lang=oc;j.textareaListener=function(a){return function(){a.fullscreenMode||a.edit.setHeight(this.scrollHeight)}};mc("core",function(a){var b=this,c={undo:"Z",redo:"Y",bold:"B",italic:"I",underline:"U",print:"P",selectall:"A"};b.afterSetHtml(function(){b.options.afterChange&&b.options.afterChange.call(b)});b.afterCreate(function(){if(b.syncType=="form"){for(var e=a(b.srcElement),f=false;e=e.parent();)if(e.name=="form"){f=true;
break}if(f){e.bind("submit",function(){b.sync();a(g).bind("unload",function(){b.edit.textarea.remove()})});var i=a('[type="reset"]',e);i.click(function(){b.html(b.initContent);b.cmd.selection()});b.beforeRemove(function(){e.unbind();i.unbind()})}}});b.clickToolbar("source",function(){if(b.edit.designMode){b.toolbar.disableAll(true);b.edit.design(false);b.toolbar.select("source");var e=b.edit.textarea[0].scrollHeight,f=a.removeUnit(b.height);b.fullscreenMode||(e<f?b.edit.setHeight(f):b.edit.setHeight(e));
if(b.edit.textarea[0].getAttribute("bindInputEventListener")!="true"){b.edit.textarea.doc.activeElement.addEventListener("input",a.textareaListener(b));b.edit.textarea[0].setAttribute("bindInputEventListener","true")}}else{b.toolbar.disableAll(false);b.edit.design(true);b.toolbar.unselect("source");Sa?setTimeout(function(){b.cmd.selection()},0):b.cmd.selection();b.edit.trigger()}b.designMode=b.edit.designMode});b.afterCreate(function(){b.designMode||b.toolbar.disableAll(true).select("source")});b.clickToolbar("fullscreen",
function(){b.fullscreen();var e=b.edit.textarea[0].scrollHeight,f=a.removeUnit(b.height);b.fullscreenMode||(e<f?b.edit.setHeight(f):b.edit.setHeight(e));!b.fullscreenMode&&b.edit.designMode&&b.edit.trigger()});if(b.fullscreenShortcut){var d=false;b.afterCreate(function(){a(b.edit.doc,b.edit.textarea).keyup(function(e){e.which==27&&setTimeout(function(){b.fullscreen()},0)});if(d){if(ka&&!b.designMode)return;b.focus()}d||(d=true)})}l("undo,redo".split(","),function(e,f){c[f]&&b.afterCreate(function(){ua(this.edit.doc,
c[f],function(){b.clickToolbar(f)})});b.clickToolbar(f,function(){b[f]()})});b.clickToolbar("formatblock",function(){var e=b.lang("formatblock.formatBlock"),f={h1:28,h2:24,h3:18,H4:14,p:12},i=b.cmd.val("formatblock"),k=b.createMenu({name:"formatblock",width:b.langType=="en"?200:150});l(e,function(n,s){var w="font-size:"+f[n]+"px;";if(n.charAt(0)==="h")w+="font-weight:bold;";k.addItem({title:'<span style="'+w+'" unselectable="on">'+s+"</span>",height:f[n]+12,checked:i===n||i===s,click:function(){b.select().exec("formatblock",
"<"+n+">").hideMenu()}})})});b.clickToolbar("fontname",function(){var e=b.cmd.val("fontname"),f=b.createMenu({name:"fontname",width:150});l(b.lang("fontname.fontName"),function(i,k){f.addItem({title:'<span style="font-family: '+i+';" unselectable="on">'+k+"</span>",checked:e===i.toLowerCase()||e===k.toLowerCase(),click:function(){b.exec("fontname",i).hideMenu()}})})});b.clickToolbar("fontsize",function(){var e=b.cmd.val("fontsize"),f=b.createMenu({name:"fontsize",width:150});l(b.fontSizeTable,function(i,
k){f.addItem({title:'<span style="font-size:'+k+';" unselectable="on">'+k+"</span>",height:z(k)+12,checked:e===k,click:function(){b.exec("fontsize",k).hideMenu()}})})});l("forecolor,hilitecolor".split(","),function(e,f){b.clickToolbar(f,function(){b.createMenu({name:f,selectedColor:b.cmd.val(f)||"default",colors:b.colorTable,click:function(i){b.exec(f,i).hideMenu()}})})});l("cut,copy,paste".split(","),function(e,f){b.clickToolbar(f,function(){b.focus();try{b.exec(f,null)}catch(i){a.popupMessage(b.lang(f+
"Error"))}})});b.clickToolbar("about",function(){var e='<div style="margin:20px;"><div>KindEditor '+pb+'</div><div>Copyright &copy; <a href="http://www.kindsoft.net/" target="_blank">kindsoft.net</a> All rights reserved.</div></div>';b.createDialog({name:"about",width:350,title:b.lang("about"),body:e})});b.plugin.getSelectedLink=function(){return b.cmd.commonAncestor("a")};b.plugin.getSelectedImage=function(){return mb(b.edit.cmd.range,function(e){return!/^ke-\w+$/i.test(e[0].className)})};b.plugin.getSelectedFlash=
function(){return mb(b.edit.cmd.range,function(e){return e[0].className=="ke-flash"})};b.plugin.getSelectedMedia=function(){return mb(b.edit.cmd.range,function(e){return e[0].className=="ke-media"||e[0].className=="ke-rm"})};b.plugin.getSelectedAnchor=function(){return mb(b.edit.cmd.range,function(e){return e[0].className=="ke-anchor"})};l("link,image,flash,media,anchor".split(","),function(e,f){var i=f.charAt(0).toUpperCase()+f.substr(1);l("edit,delete".split(","),function(k,n){b.addContextmenu({title:b.lang(n+
i),click:function(){b.loadPlugin(f,function(){b.plugin[f][n]();b.hideMenu()})},cond:b.plugin["getSelected"+i],width:150,iconClass:n=="edit"?"ke-icon-"+f:h})});b.addContextmenu({title:"-"})});b.plugin.getSelectedHide=function(){return b.cmd.commonAncestor("hide")};l("hide".split(","),function(e,f){var i=f.charAt(0).toUpperCase()+f.substr(1);l("edit,delete".split(","),function(k,n){b.addContextmenu({title:b.lang(n+i),click:function(){b.loadPlugin(f,function(){b.plugin[f][n]();b.hideMenu()})},cond:b.plugin["getSelected"+
i],width:150,iconClass:"ke-icon-"+f+"-"+n})});b.addContextmenu({title:"-"})});b.plugin.getSelectedCode=function(){return b.cmd.commonAncestor("pre")};l("code".split(","),function(e,f){var i=f.charAt(0).toUpperCase()+f.substr(1);l("edit".split(","),function(k,n){b.addContextmenu({title:b.lang(n+i),click:function(){b.loadPlugin(f,function(){b.plugin[f][n]();b.hideMenu()})},cond:b.plugin["getSelected"+i],width:150,iconClass:"ke-icon-"+f+"-"+n})});b.addContextmenu({title:"-"})});b.plugin.getSelectedTable=
function(){return b.cmd.commonAncestor("table")};b.plugin.getSelectedRow=function(){return b.cmd.commonAncestor("tr")};b.plugin.getSelectedCell=function(){return b.cmd.commonAncestor("td")};l("prop,cellprop,colinsertleft,colinsertright,rowinsertabove,rowinsertbelow,rowmerge,colmerge,rowsplit,colsplit,coldelete,rowdelete,insert,delete".split(","),function(e,f){e=o(f,["prop","delete"])<0?b.plugin.getSelectedCell:b.plugin.getSelectedTable;b.addContextmenu({title:b.lang("table"+f),click:function(){b.loadPlugin("table",
function(){b.plugin.table[f]();b.hideMenu()})},cond:e,width:170,iconClass:"ke-icon-table"+f})});b.addContextmenu({title:"-"});l("selectall,justifyleft,justifycenter,justifyright,justifyfull,insertorderedlist,insertunorderedlist,indent,outdent,subscript,superscript,hr,print,bold,italic,underline,strikethrough,removeformat,unlink".split(","),function(e,f){c[f]&&b.afterCreate(function(){ua(this.edit.doc,c[f],function(){b.cmd.selection();b.clickToolbar(f)})});b.clickToolbar(f,function(){b.focus().exec(f,
null)})});b.afterCreate(function(){function e(x){for(x=a(x.commonAncestor());x;){if(x.name=="pre")break;x=x.parent()}if(x!=null)return x.name;return""}function f(){s.range.moveToBookmark(w);s.select();if(Ra){a("div."+y,A).each(function(){a(this).after("<br />").remove(true)});a("span.Apple-style-span",A).remove(true);a("span.Apple-tab-span",A).remove(true);a("span[style]",A).each(function(){a(this).css("white-space")=="nowrap"&&a(this).remove(true)});a("meta",A).remove()}var x=A[0].innerHTML;A.remove();
if(x!==""){if(Ra)x=x.replace(/(<br>)\1/ig,"$1");if(e(b.cmd.range)=="pre"){x=x.replace(/&nbsp;/ig," ");x=x.replace(/\n\s*\n/g,"\n");x=x.replace(/<br[^>]*>/ig,"\n");x=x.replace(/<\/p><p[^>]*>/ig,"\n");x=x.replace(/<[^>]+>/g,"");x=x.replace(/ {2}/g," &nbsp;")}else{if(b.pasteType===2){x=x.replace(/(<(?:p|p\s[^>]*)>) *(<\/p>)/ig,"");if(/schemas-microsoft-com|worddocument|mso-\w+/i.test(x))x=Tb(x,b.filterMode?b.htmlTags:a.options.htmlTags);else{x=Aa(x,b.filterMode?b.htmlTags:null);x=b.beforeSetHtml(x)}}if(b.pasteType===
1){x=x.replace(/&nbsp;/ig," ");x=x.replace(/\n\s*\n/g,"\n");x=x.replace(/<br[^>]*>/ig,"\n");x=x.replace(/<\/p><p[^>]*>/ig,"\n");x=x.replace(/<[^>]+>/g,"");x=x.replace(/ {2}/g," &nbsp;");if(b.newlineTag=="p"){if(/\n/.test(x))x=x.replace(/^/,"<p>").replace(/$/,"<br /></p>").replace(/\n/g,"<br /></p><p>")}else x=x.replace(/\n/g,"<br />$&")}}b.insertHtml(x,true)}}function i(x,L){var ca=a.undef(b.uploadJson,b.basePath+"php/upload_json"),F=a.undef(b.filePostName,"imgFile");if(b.options.uploadModule==10){var U=
x.name;L=a.addParam(ca,L);ca="fileName="+encodeURIComponent(U);a.post_ajax(function(E){E=E.responseText;if(E!=""){E=JSON.parse(E);if(E.error==0){E=E.url;var N=E.substring(0,E.indexOf("?")),ja=E.substring(E.indexOf("?")+1,E.length);document.getElementById("kindeditor_pasteImageUpload_imageName").innerText=U;var oa="";E=new FormData;ja=ja.split("&");for(var S=0;S<ja.length;S++){var Y=ja[S].split("=");E.append(Y[0],decodeURIComponent(Y[1]));if(Y[0]=="key")oa=decodeURIComponent(Y[1])}E.append(F,x);a.post_ajax(function(ea){if(ea.status==
200){a.popupMessage(KindEditor.lang("uploadSuccess"));b.exec("insertimage",N+oa,U,h,h,h,h);a('<img src="'+N+oa+'" />',document).each(function(){var O=new Image;O.src=N+oa;O.onload=function(){b.edit.trigger()}})}},N,true,E)}else a.popupMessage(E.message)}},L+"&"+ca,true,"",true)}else if(b.options.uploadModule==20){U=x.name;L=a.addParam(ca,L);ca="fileName="+encodeURIComponent(U);a.post_ajax(function(E){E=E.responseText;if(E!=""){E=JSON.parse(E);if(E.error==0){var N=E.url;E=N.substring(0,N.indexOf("?"));
var ja=N.substring(N.indexOf("?")+1,N.length);document.getElementById("kindeditor_pasteImageUpload_imageName").innerText=U;N=new FormData;ja=ja.split("&");for(var oa=0;oa<ja.length;oa++){var S=ja[oa].split("=");N.append(S[0],decodeURIComponent(S[1]))}N.append(F,x);a.post_ajax(function(Y){var ea=Y.getResponseHeader("etag"),O=Y.getResponseHeader("location");if(ea!=h&&O!=h){a.popupMessage(KindEditor.lang("uploadSuccess"));b.exec("insertimage",O,U,h,h,h,h);a('<img src="'+O+'" />',document).each(function(){var ia=
new Image;ia.src=O;ia.onload=function(){b.edit.trigger()}})}},E,true,N)}else a.popupMessage(E.message)}},L+"&"+ca,true,"",true)}else if(b.options.uploadModule==30){U=x.name;L=a.addParam(ca,L);ca="fileName="+encodeURIComponent(U);a.post_ajax(function(E){E=E.responseText;if(E!=""){E=JSON.parse(E);if(E.error==0){E=E.url;var N=E.substring(0,E.indexOf("?")),ja=E.substring(E.indexOf("?")+1,E.length),oa="";document.getElementById("kindeditor_pasteImageUpload_imageName").innerText=U;E=new FormData;ja=ja.split("&");
for(var S=0;S<ja.length;S++){var Y=ja[S].split("=");E.append(Y[0],decodeURIComponent(Y[1]));if(Y[0]=="key")oa=decodeURIComponent(Y[1])}E.append(F,x);a.post_ajax(function(ea){if(ea.status==200){a.popupMessage(KindEditor.lang("uploadSuccess"));b.exec("insertimage",N+oa,U,h,h,h,h);a('<img src="'+N+oa+'" />',document).each(function(){var O=new Image;O.src=N+oa;O.onload=function(){b.edit.trigger()}})}},N,true,E)}else a.popupMessage(E.message)}},L+"&"+ca,true,"",true)}else{U=x.name;L=a.addParam(ca,L);ca=
"fileName="+encodeURIComponent(U);document.getElementById("kindeditor_pasteImageUpload_imageName").innerText=U;var ga=new FormData;ga.append(F,x);a.post_ajax(function(E){E=E.responseText;if(E!=""){var N=JSON.parse(E);if(N.error==0){b.exec("insertimage",N.url,N.title,h,h,h,h);a('<img src="'+N.url+'" />',document).each(function(){var ja=new Image;ja.src=N.url;ja.onload=function(){b.edit.trigger()}})}else a.popupMessage(N.message)}},L+"&"+ca,true,ga,true)}}function k(x,L){b.lang("pasteImageUpload.");
if(x=x.event.clipboardData.items){for(var ca=0,F=0;F<x.length;F++)if(x[F].kind==="file"&&x[F].type.indexOf("image/")!==-1){ca==0&&b.createDialog({name:"pasteImageUpload",width:450,height:200,title:b.lang("pasteImageUpload"),body:'<div style="padding:20px;"><div class="ke-dialog-row">\u56fe\u7247 <span id="kindeditor_pasteImageUpload_imageName"></span> \u4e0a\u4f20\u4e2d..</div><div style="color: #909399;position: absolute;left: 21px;bottom: 18px;">\u63d0\u793a\uff1a\u4e0a\u4f20\u5b8c\u6210\u4f1a\u81ea\u52a8\u5173\u95ed\u672c\u7a97\u53e3</div></div>'});
ca++;var U=x[F].getAsFile();i(U,"dir=image")}if(ca>0){setTimeout(function(){b.hideDialog().focus()},3E3);return L(true)}}return L(false)}var n=b.edit.doc,s,w,A,y="__kindeditor_paste__",K=false;a(n.body).bind("paste",function(x){if(b.pasteType===0)x.stop();else if(!K){var L=0,ca=0;K=true;k(x,function(F){if(F){x.preventDefault();setTimeout(function(){K=false},0);setTimeout(function(){b.edit.trigger()},0)}else{if(document.querySelector(".el-main")){L=document.querySelector(".el-main").scrollTop;$(".el-main").on("scroll.unable",
function(){$(document.querySelector(".el-main")).scrollTop(L)})}else{L=document.body.scrollTop+document.documentElement.scrollTop;$(document).on("scroll.unable",function(){$(document).scrollTop(L)})}document.querySelector(".el-main")?document.querySelector(".el-main").scrollTo(0,L):g.scrollTo(0,L);a("div."+y,n).remove();s=b.cmd.selection();w=s.range.createBookmark();A=a('<div class="'+y+'"></div>',n).css({position:"absolute",width:"1px",height:"1px",overflow:"hidden",left:"-1981px",top:a(w.start).pos().y+
"px","white-space":"nowrap"});a(n.body).append(A);if(ka){F=s.range.get(true);F.moveToElementText(A[0]);F.select();F.execCommand("paste");x.preventDefault()}else{s.range.selectNodeContents(A[0]);s.select();A[0].tabIndex=-1;A[0].focus()}setTimeout(function(){f();K=false},0);setTimeout(function(){b.edit.trigger()},0);setTimeout(function(){ca=L+a(w.start).pos().y},0);setTimeout(function(){if(document.querySelector(".el-main")){document.querySelector(".el-main").scrollTo(0,ca);$(document.querySelector(".el-main")).unbind("scroll.unable")}else{g.scrollTo(0,
ca);$(document).unbind("scroll.unable")}},200)}})}})});b.beforeGetHtml(function(e){if(ka&&qa<=8){e=e.replace(/<div\s+[^>]*data-ke-input-tag="([^"]*)"[^>]*>([\s\S]*?)<\/div>/ig,function(f,i){return unescape(i)});e=e.replace(/(<input)((?:\s+[^>]*)?>)/ig,function(f,i,k){if(!/\s+type="[^"]+"/i.test(f))return i+' type="text"'+k;return f})}return e.replace(/(<(?:noscript|noscript\s[^>]*)>)([\s\S]*?)(<\/noscript>)/ig,function(f,i,k,n){return i+C(k).replace(/\s+/g," ")+n}).replace(/<article\s+[^>]*type="([^"]*)"[^>]*>([\s\S]*?)<\/article>/ig,
function(f,i,k){return k}).replace(/<img[^>]*class="?ke-(flash|rm|media|video)"?[^>]*>/ig,function(f){f=ba(f);var i=wa(f.style||""),k=tb(f["data-ke-tag"]),n=B(i.width,"");i=B(i.height,"");if(/px/i.test(n))n=z(n);if(/px/i.test(i))i=z(i);k.width=B(f.width,n);k.height=B(f.height,i);return ub(k)}).replace(/<img[^>]*class="?ke-(iframe)"?[^>]*>/ig,function(f){f=ba(f);var i=wa(f.style||""),k=tb(f["data-ke-tag"]),n=B(i.width,"");i=B(i.height,"");if(/px/i.test(n))n=z(n);if(/px/i.test(i))i=z(i);k.width=B(f.width,
n);k.height=B(f.height,i);return vb(k)}).replace(/<img[^>]*class="?ke-anchor"?[^>]*>/ig,function(f){f=ba(f);return'<a name="'+unescape(f["data-ke-name"])+'"></a>'}).replace(/<div\s+[^>]*data-ke-script-attr="([^"]*)"[^>]*>([\s\S]*?)<\/div>/ig,function(f,i,k){return"<script"+unescape(i)+">"+unescape(k)+"<\/script>"}).replace(/<div\s+[^>]*data-ke-noscript-attr="([^"]*)"[^>]*>([\s\S]*?)<\/div>/ig,function(f,i,k){return"<noscript"+unescape(i)+">"+unescape(k)+"</noscript>"}).replace(/(<[^>]*)data-ke-src="([^"]*)"([^>]*>)/ig,
function(f,i,k){f=f.replace(/(\s+(?:href|src)=")[^"]*(")/i,function(n,s,w){return s+C(k)+w});return f=f.replace(/\s+data-ke-src="[^"]*"/i,"")}).replace(/(<[^>]+\s)data-ke-(on\w+="[^"]*"[^>]*>)/ig,function(f,i,k){return i+k})});b.beforeSetHtml(function(e){if(ka&&qa<=8)e=e.replace(/<input[^>]*>|<(select|button)[^>]*>[\s\S]*?<\/\1>/ig,function(f){var i=ba(f);if(wa(i.style||"").display=="none")return'<div class="ke-display-none" data-ke-input-tag="'+escape(f)+'"></div>';return f});return e.replace(/<video[^>]*src="([^"]+)"[^>]*>(?:<\/video>)?/ig,
function(f){f=ba(f);f.src=B(f.src,"");f.width=B(f.width,0);f.height=B(f.height,0);return Ub(b.themesPath+"common/blank.gif",f)}).replace(/<iframe[^>]*src="([^"]+)"[^>]*>(?:<\/iframe>)?/ig,function(f){f=ba(f);f.src=B(f.src,"");f.width=B(f.width,0);f.height=B(f.height,0);return Vb(b.themesPath+"common/blank.gif",f)}).replace(/<a[^>]*name="([^"]+)"[^>]*>(?:<\/a>)?/ig,function(f){var i=ba(f);if(i.href!==h)return f;return'<img class="ke-anchor" src="'+b.themesPath+'common/anchor.gif" data-ke-name="'+escape(i.name)+
'" />'}).replace(/<script([^>]*)>([\s\S]*?)<\/script>/ig,function(f,i,k){return'<div class="ke-script" data-ke-script-attr="'+escape(i)+'">'+escape(k)+"</div>"}).replace(/<noscript([^>]*)>([\s\S]*?)<\/noscript>/ig,function(f,i,k){return'<div class="ke-noscript" data-ke-noscript-attr="'+escape(i)+'">'+escape(k)+"</div>"}).replace(/(<[^>]*)(href|src)="([^"]*)"([^>]*>)/ig,function(f,i,k,n,s){if(f.match(/\sdata-ke-src="[^"]*"/i))return f;return f=i+k+'="'+n+'" data-ke-src="'+H(n)+'"'+s}).replace(/(<[^>]+\s)(on\w+="[^"]*"[^>]*>)/ig,
function(f,i,k){return i+"data-ke-"+k}).replace(/<table[^>]*\s+border="0"[^>]*>/ig,function(f){if(f.indexOf("ke-zeroborder")>=0)return f;return sa(f,"ke-zeroborder")}).replace(/(<(?:hide|hide\s[^>]*)>)([\s\S]*?)(<\/hide>)/ig,function(f,i,k,n){return'<article type="__kindeditor_temp_pre__">'+i+k+n+"</article>"})})})}})(window);
KindEditor.lang({source:"HTML\u4ee3\u7801",preview:"\u9884\u89c8",undo:"\u540e\u9000(Ctrl+Z)",redo:"\u524d\u8fdb(Ctrl+Y)",cut:"\u526a\u5207(Ctrl+X)",copy:"\u590d\u5236(Ctrl+C)",paste:"\u7c98\u8d34(Ctrl+V)",plainpaste:"\u7c98\u8d34\u4e3a\u65e0\u683c\u5f0f\u6587\u672c",wordpaste:"\u4eceWord\u7c98\u8d34",selectall:"\u5168\u9009(Ctrl+A)",justifyleft:"\u5de6\u5bf9\u9f50",justifycenter:"\u5c45\u4e2d",justifyright:"\u53f3\u5bf9\u9f50",justifyfull:"\u4e24\u7aef\u5bf9\u9f50",insertorderedlist:"\u7f16\u53f7",
insertunorderedlist:"\u9879\u76ee\u7b26\u53f7",indent:"\u589e\u52a0\u7f29\u8fdb",outdent:"\u51cf\u5c11\u7f29\u8fdb",subscript:"\u4e0b\u6807",superscript:"\u4e0a\u6807",formatblock:"\u6bb5\u843d",fontname:"\u5b57\u4f53",fontsize:"\u6587\u5b57\u5927\u5c0f",forecolor:"\u6587\u5b57\u989c\u8272",hilitecolor:"\u6587\u5b57\u80cc\u666f",bold:"\u7c97\u4f53(Ctrl+B)",italic:"\u659c\u4f53(Ctrl+I)",underline:"\u4e0b\u5212\u7ebf(Ctrl+U)",strikethrough:"\u5220\u9664\u7ebf",removeformat:"\u5220\u9664\u683c\u5f0f",
image:"\u56fe\u7247",multiimage:"\u6279\u91cf\u56fe\u7247\u4e0a\u4f20",pasteImageUpload:"\u7c98\u8d34\u56fe\u7247\u4e0a\u4f20",flash:"Flash",media:"\u89c6\u97f3\u9891",table:"\u8868\u683c",tablecell:"\u5355\u5143\u683c",hr:"\u63d2\u5165\u6a2a\u7ebf",emoticons:"\u63d2\u5165\u8868\u60c5",link:"\u8d85\u7ea7\u94fe\u63a5",unlink:"\u53d6\u6d88\u8d85\u7ea7\u94fe\u63a5",fullscreen:"\u5168\u5c4f\u663e\u793a",about:"\u5173\u4e8e",print:"\u6253\u5370(Ctrl+P)",filemanager:"\u6587\u4ef6\u7a7a\u95f4",code:"\u63d2\u5165\u7a0b\u5e8f\u4ee3\u7801",
map:"Google\u5730\u56fe",baidumap:"\u767e\u5ea6\u5730\u56fe",lineheight:"\u884c\u8ddd",clearhtml:"\u6e05\u7406HTML\u4ee3\u7801",pagebreak:"\u63d2\u5165\u5206\u9875\u7b26",quickformat:"\u4e00\u952e\u6392\u7248",insertfile:"\u63d2\u5165\u6587\u4ef6",template:"\u63d2\u5165\u6a21\u677f",anchor:"\u951a\u70b9",yes:"\u786e\u5b9a",no:"\u53d6\u6d88",close:"\u5173\u95ed",editImage:"\u56fe\u7247\u5c5e\u6027",deleteImage:"\u5220\u9664\u56fe\u7247",editFlash:"Flash\u5c5e\u6027",deleteFlash:"\u5220\u9664Flash",
editMedia:"\u89c6\u97f3\u9891\u5c5e\u6027",deleteMedia:"\u5220\u9664\u89c6\u97f3\u9891",editLink:"\u8d85\u7ea7\u94fe\u63a5\u5c5e\u6027",deleteLink:"\u53d6\u6d88\u8d85\u7ea7\u94fe\u63a5",editAnchor:"\u951a\u70b9\u5c5e\u6027",deleteAnchor:"\u5220\u9664\u951a\u70b9",tableprop:"\u8868\u683c\u5c5e\u6027",tablecellprop:"\u5355\u5143\u683c\u5c5e\u6027",tableinsert:"\u63d2\u5165\u8868\u683c",tabledelete:"\u5220\u9664\u8868\u683c",tablecolinsertleft:"\u5de6\u4fa7\u63d2\u5165\u5217",tablecolinsertright:"\u53f3\u4fa7\u63d2\u5165\u5217",
tablerowinsertabove:"\u4e0a\u65b9\u63d2\u5165\u884c",tablerowinsertbelow:"\u4e0b\u65b9\u63d2\u5165\u884c",tablerowmerge:"\u5411\u4e0b\u5408\u5e76\u5355\u5143\u683c",tablecolmerge:"\u5411\u53f3\u5408\u5e76\u5355\u5143\u683c",tablerowsplit:"\u62c6\u5206\u884c",tablecolsplit:"\u62c6\u5206\u5217",tablecoldelete:"\u5220\u9664\u5217",tablerowdelete:"\u5220\u9664\u884c",noColor:"\u65e0\u989c\u8272",pleaseSelectFile:"\u8bf7\u9009\u62e9\u6587\u4ef6\u3002",invalidImg:"\u8bf7\u8f93\u5165\u6709\u6548\u7684URL\u5730\u5740\u3002\n\u53ea\u5141\u8bb8jpg,gif,bmp,png\u683c\u5f0f\u3002",
invalidMedia:"\u8bf7\u8f93\u5165\u6709\u6548\u7684URL\u5730\u5740\u3002\n\u53ea\u5141\u8bb8swf,flv,mp3,wav,wma,wmv,mid,avi,mpg,asf,rm,rmvb\u683c\u5f0f\u3002",invalidWidth:"\u5bbd\u5ea6\u5fc5\u987b\u4e3a\u6570\u5b57\u3002",invalidHeight:"\u9ad8\u5ea6\u5fc5\u987b\u4e3a\u6570\u5b57\u3002",invalidBorder:"\u8fb9\u6846\u5fc5\u987b\u4e3a\u6570\u5b57\u3002",invalidUrl:"\u8bf7\u8f93\u5165\u6709\u6548\u7684URL\u5730\u5740\u3002",invalidRows:"\u884c\u6570\u4e3a\u5fc5\u9009\u9879\uff0c\u53ea\u5141\u8bb8\u8f93\u5165\u5927\u4e8e0\u7684\u6570\u5b57\u3002",
invalidCols:"\u5217\u6570\u4e3a\u5fc5\u9009\u9879\uff0c\u53ea\u5141\u8bb8\u8f93\u5165\u5927\u4e8e0\u7684\u6570\u5b57\u3002",invalidPadding:"\u8fb9\u8ddd\u5fc5\u987b\u4e3a\u6570\u5b57\u3002",invalidSpacing:"\u95f4\u8ddd\u5fc5\u987b\u4e3a\u6570\u5b57\u3002",invalidJson:"\u670d\u52a1\u5668\u53d1\u751f\u6545\u969c\u3002",invalidVideoCode:"\u8bf7\u8f93\u5165\u89c6\u9891\u4ee3\u7801\u3002",invalidVideoCodeFormat:"\u4ee3\u7801\u683c\u5f0f\u5982\uff1a&lt;iframe src=https://.. >&lt;/iframe>",uploadSuccess:"\u4e0a\u4f20\u6210\u529f\u3002",
cutError:"\u60a8\u7684\u6d4f\u89c8\u5668\u5b89\u5168\u8bbe\u7f6e\u4e0d\u5141\u8bb8\u4f7f\u7528\u526a\u5207\u64cd\u4f5c\uff0c\u8bf7\u4f7f\u7528\u5feb\u6377\u952e(Ctrl+X)\u6765\u5b8c\u6210\u3002",copyError:"\u60a8\u7684\u6d4f\u89c8\u5668\u5b89\u5168\u8bbe\u7f6e\u4e0d\u5141\u8bb8\u4f7f\u7528\u590d\u5236\u64cd\u4f5c\uff0c\u8bf7\u4f7f\u7528\u5feb\u6377\u952e(Ctrl+C)\u6765\u5b8c\u6210\u3002",pasteError:"\u60a8\u7684\u6d4f\u89c8\u5668\u5b89\u5168\u8bbe\u7f6e\u4e0d\u5141\u8bb8\u4f7f\u7528\u7c98\u8d34\u64cd\u4f5c\uff0c\u8bf7\u4f7f\u7528\u5feb\u6377\u952e(Ctrl+V)\u6765\u5b8c\u6210\u3002",
ajaxLoading:"\u52a0\u8f7d\u4e2d\uff0c\u8bf7\u7a0d\u5019 ...",uploadLoading:"\u4e0a\u4f20\u4e2d\uff0c\u8bf7\u7a0d\u5019 ...",uploadError:"\u4e0a\u4f20\u9519\u8bef",sendRequestFailed:"\u53d1\u9001\u8bf7\u6c42\u5931\u8d25\uff0c\u8bf7\u91cd\u8bd5",editHide:"\u9690\u85cf\u6807\u7b7e\u5c5e\u6027",deleteHide:"\u5220\u9664\u9690\u85cf\u6807\u7b7e",editCode:"\u4fee\u6539\u8bed\u8a00","plainpaste.comment":"\u8bf7\u4f7f\u7528\u5feb\u6377\u952e(Ctrl+V)\u628a\u5185\u5bb9\u7c98\u8d34\u5230\u4e0b\u9762\u7684\u65b9\u6846\u91cc\u3002",
"wordpaste.comment":"\u8bf7\u4f7f\u7528\u5feb\u6377\u952e(Ctrl+V)\u628a\u5185\u5bb9\u7c98\u8d34\u5230\u4e0b\u9762\u7684\u65b9\u6846\u91cc\u3002","code.pleaseInput":"\u8bf7\u8f93\u5165\u7a0b\u5e8f\u4ee3\u7801\u3002","link.url":"URL","link.linkType":"\u6253\u5f00\u7c7b\u578b","link.newWindow":"\u65b0\u7a97\u53e3","link.selfWindow":"\u5f53\u524d\u7a97\u53e3","flash.url":"URL","flash.width":"\u5bbd\u5ea6","flash.height":"\u9ad8\u5ea6","flash.upload":"\u4e0a\u4f20","flash.viewServer":"\u6587\u4ef6\u7a7a\u95f4",
"media.url":"URL","media.width":"\u5bbd\u5ea6","media.height":"\u9ad8\u5ea6","media.autostart":"\u81ea\u52a8\u64ad\u653e","media.upload":"\u4e0a\u4f20","media.viewServer":"\u6587\u4ef6\u7a7a\u95f4","image.remoteImage":"\u7f51\u7edc\u56fe\u7247","image.localImage":"\u672c\u5730\u4e0a\u4f20","image.remoteUrl":"\u56fe\u7247\u5730\u5740","image.localUrl":"\u4e0a\u4f20\u6587\u4ef6","image.size":"\u56fe\u7247\u5927\u5c0f","image.width":"\u5bbd","image.height":"\u9ad8","image.resetSize":"\u91cd\u7f6e\u5927\u5c0f",
"image.align":"\u5bf9\u9f50\u65b9\u5f0f","image.defaultAlign":"\u9ed8\u8ba4\u65b9\u5f0f","image.leftAlign":"\u5de6\u5bf9\u9f50","image.rightAlign":"\u53f3\u5bf9\u9f50","image.imgTitle":"\u56fe\u7247\u8bf4\u660e","image.upload":"\u6d4f\u89c8...","image.viewServer":"\u56fe\u7247\u7a7a\u95f4","multiimage.uploadDesc":"\u5141\u8bb8\u7528\u6237\u540c\u65f6\u4e0a\u4f20<%=uploadLimit%>\u5f20\u56fe\u7247\uff0c\u5355\u5f20\u56fe\u7247\u5bb9\u91cf\u4e0d\u8d85\u8fc7<%=sizeLimit%>","multiimage.startUpload":"\u5f00\u59cb\u4e0a\u4f20",
"multiimage.clearAll":"\u5168\u90e8\u6e05\u7a7a","multiimage.insertAll":"\u5168\u90e8\u63d2\u5165","multiimage.queueLimitExceeded":"\u6587\u4ef6\u6570\u91cf\u8d85\u8fc7\u9650\u5236\u3002","multiimage.fileExceedsSizeLimit":"\u6587\u4ef6\u5927\u5c0f\u8d85\u8fc7\u9650\u5236\u3002","multiimage.zeroByteFile":"\u65e0\u6cd5\u4e0a\u4f20\u7a7a\u6587\u4ef6\u3002","multiimage.invalidFiletype":"\u6587\u4ef6\u7c7b\u578b\u4e0d\u6b63\u786e\u3002","multiimage.unknownError":"\u53d1\u751f\u5f02\u5e38\uff0c\u65e0\u6cd5\u4e0a\u4f20\u3002",
"multiimage.pending":"\u7b49\u5f85\u4e0a\u4f20","multiimage.uploadError":"\u4e0a\u4f20\u5931\u8d25","filemanager.emptyFolder":"\u7a7a\u6587\u4ef6\u5939","filemanager.moveup":"\u79fb\u5230\u4e0a\u4e00\u7ea7\u6587\u4ef6\u5939","filemanager.viewType":"\u663e\u793a\u65b9\u5f0f\uff1a","filemanager.viewImage":"\u7f29\u7565\u56fe","filemanager.listImage":"\u8be6\u7ec6\u4fe1\u606f","filemanager.orderType":"\u6392\u5e8f\u65b9\u5f0f\uff1a","filemanager.fileName":"\u540d\u79f0","filemanager.fileSize":"\u5927\u5c0f",
"filemanager.fileType":"\u7c7b\u578b","insertfile.url":"URL","insertfile.title":"\u6587\u4ef6\u8bf4\u660e","insertfile.upload":"\u4e0a\u4f20","insertfile.viewServer":"\u6587\u4ef6\u7a7a\u95f4","table.cells":"\u5355\u5143\u683c\u6570","table.rows":"\u884c\u6570","table.cols":"\u5217\u6570","table.size":"\u5927\u5c0f","table.width":"\u5bbd\u5ea6","table.height":"\u9ad8\u5ea6","table.percent":"%","table.px":"px","table.space":"\u8fb9\u8ddd\u95f4\u8ddd","table.padding":"\u8fb9\u8ddd","table.spacing":"\u95f4\u8ddd",
"table.align":"\u5bf9\u9f50\u65b9\u5f0f","table.textAlign":"\u6c34\u5e73\u5bf9\u9f50","table.verticalAlign":"\u5782\u76f4\u5bf9\u9f50","table.alignDefault":"\u9ed8\u8ba4","table.alignLeft":"\u5de6\u5bf9\u9f50","table.alignCenter":"\u5c45\u4e2d","table.alignRight":"\u53f3\u5bf9\u9f50","table.alignTop":"\u9876\u90e8","table.alignMiddle":"\u4e2d\u90e8","table.alignBottom":"\u5e95\u90e8","table.alignBaseline":"\u57fa\u7ebf","table.border":"\u8fb9\u6846","table.borderWidth":"\u8fb9\u6846","table.borderColor":"\u989c\u8272",
"table.backgroundColor":"\u80cc\u666f\u989c\u8272","map.address":"\u5730\u5740: ","map.search":"\u641c\u7d22","baidumap.address":"\u5730\u5740: ","baidumap.search":"\u641c\u7d22","baidumap.insertDynamicMap":"\u63d2\u5165\u52a8\u6001\u5730\u56fe","anchor.name":"\u951a\u70b9\u540d\u79f0","formatblock.formatBlock":{h1:"\u6807\u9898 1",h2:"\u6807\u9898 2",h3:"\u6807\u9898 3",h4:"\u6807\u9898 4",p:"\u6b63 \u6587"},"fontname.fontName":{SimSun:"\u5b8b\u4f53",NSimSun:"\u65b0\u5b8b\u4f53",FangSong_GB2312:"\u4eff\u5b8b_GB2312",
KaiTi_GB2312:"\u6977\u4f53_GB2312",SimHei:"\u9ed1\u4f53","Microsoft YaHei":"\u5fae\u8f6f\u96c5\u9ed1",Arial:"Arial","Arial Black":"Arial Black","Times New Roman":"Times New Roman","Courier New":"Courier New",Tahoma:"Tahoma",Verdana:"Verdana"},"lineheight.lineHeight":[{"1":"\u5355\u500d\u884c\u8ddd"},{"1.5":"1.5\u500d\u884c\u8ddd"},{"2":"2\u500d\u884c\u8ddd"},{"2.5":"2.5\u500d\u884c\u8ddd"},{"3":"3\u500d\u884c\u8ddd"}],"template.selectTemplate":"\u53ef\u9009\u6a21\u677f","template.replaceContent":"\u66ff\u6362\u5f53\u524d\u5185\u5bb9",
"template.fileList":{"1.html":"\u56fe\u7247\u548c\u6587\u5b57","2.html":"\u8868\u683c","3.html":"\u9879\u76ee\u7f16\u53f7"}},"zh-CN");KindEditor.options.langType="zh-CN";
KindEditor.plugin("anchor",function(g){var h=this,u=h.lang("anchor.");h.plugin.anchor={edit:function(){var v=['<div style="padding:20px;"><div class="ke-dialog-row">','<label for="keName">'+u.name+"</label>",'<input class="ke-input-text" type="text" id="keName" name="name" value="" style="width:100px;" /></div></div>'].join("");v=h.createDialog({name:"anchor",width:300,title:h.lang("anchor"),body:v,yesBtn:{name:h.lang("yes"),click:function(){h.insertHtml('<a name="'+m.val()+'">').hideDialog().focus()}}}).div;
var m=g('input[name="name"]',v);(v=h.plugin.getSelectedAnchor())&&m.val(unescape(v.attr("data-ke-name")));m[0].focus();m[0].select()},"delete":function(){h.plugin.getSelectedAnchor().remove()}};h.clickToolbar("anchor",h.plugin.anchor.edit)});
KindEditor.plugin("autoheight",function(g){function h(){var o=m.edit,l=o.doc.body;o.iframe[0].scroll="no";l.style.overflowY="hidden"}function u(){if(!m.fullscreenMode){var o=m.edit,l=o.doc.body,q=t-m.toolbar.div.height()-m.statusbar.height()-30;o.iframe.height(q);m.resize(null,Math.max((g.IE?l.scrollHeight:l.offsetHeight)+m.toolbar.div.height()+m.statusbar.height()+30,q))}}function v(){t=g.removeUnit(m.height);m.edit.afterChange(u);m.fullscreenMode||h();u()}var m=this;if(m.autoHeightMode){var t=g.removeUnit(m.height);
m.isCreated?v():m.afterCreate(v)}});
KindEditor.plugin("baidumap",function(g){var h=this,u=h.lang("baidumap."),v=g.undef(h.mapWidth,558),m=g.undef(h.mapHeight,380);h.clickToolbar("baidumap",function(){function t(){z=C[0].contentWindow;H=g.iframeDoc(C)}var o=['<div style="padding:20px 20px;"><div class="ke-header"><div class="ke-left">',u.address+' <input id="kindeditor_plugin_map_address" name="address" class="ke-input-text" value="" style="width:200px;" /> ','<span class="ke-button-common ke-button-outer">','<input type="button" name="searchBtn" class="ke-button-common ke-button" value="'+
u.search+'" />','</span></div><div class="ke-right">','<input type="checkbox" id="keInsertDynamicMap" name="insertDynamicMap" value="1" /> <label for="keInsertDynamicMap">'+u.insertDynamicMap+"</label>",'</div><div class="ke-clearfix"></div></div>','<div class="ke-map" style="width:'+v+"px;height:"+(m+20)+'px;"></div>',"</div>"].join("");o=h.createDialog({name:"baidumap",width:v+51,title:h.lang("baidumap"),body:o,yesBtn:{name:h.lang("yes"),click:function(){var D=z.map,Q=D.getCenter();Q=Q.lng+","+
Q.lat;D=D.getZoom();D=[r[0].checked?h.pluginsPath+"baidumap/index.html":"http://api.map.baidu.com/staticimage","?center="+encodeURIComponent(Q),"&zoom="+encodeURIComponent(D),"&width="+v,"&height="+m,"&markers="+encodeURIComponent(Q),"&markerStyles="+encodeURIComponent("l,A")].join("");r[0].checked?h.insertHtml('<iframe src="'+D+'" frameborder="0" style="width:'+(v+2)+"px;height:"+(m+2)+'px;"></iframe>'):h.exec("insertimage",D);h.hideDialog().focus()}},beforeRemove:function(){p.remove();H&&H.write("");
C.remove()}});var l=o.div,q=g('[name="address"]',l),p=g('[name="searchBtn"]',l),r=g('[name="insertDynamicMap"]',o.div),z,H,C=g('<iframe class="ke-textarea" frameborder="0" src="'+h.pluginsPath+'baidumap/map.html" style="width:'+v+"px;height:"+m+'px;"></iframe>');C.bind("load",function(){C.unbind("load");g.IE?t():setTimeout(t,0)});g(".ke-map",l).replaceWith(C);p.click(function(){z.search(q.val())})})});
KindEditor.plugin("map",function(g){var h=this,u=h.lang("map.");h.clickToolbar("map",function(){function v(){l=p[0].contentWindow;q=g.iframeDoc(p)}var m=['<div style="padding:10px 20px;"><div class="ke-dialog-row">',u.address+' <input id="kindeditor_plugin_map_address" name="address" class="ke-input-text" value="" style="width:200px;" /> ','<span class="ke-button-common ke-button-outer">','<input type="button" name="searchBtn" class="ke-button-common ke-button" value="'+u.search+'" />','</span></div><div class="ke-map" style="width:558px;height:360px;"></div></div>'].join("");
m=h.createDialog({name:"map",width:600,title:h.lang("map"),body:m,yesBtn:{name:h.lang("yes"),click:function(){var r=l.map,z=r.getCenter().lat()+","+r.getCenter().lng(),H=r.getZoom();r=r.getMapTypeId();var C="http://maps.googleapis.com/maps/api/staticmap";C+="?center="+encodeURIComponent(z);C+="&zoom="+encodeURIComponent(H);C+="&size=558x360";C+="&maptype="+encodeURIComponent(r);C+="&markers="+encodeURIComponent(z);C+="&language="+h.langType;C+="&sensor=false";h.exec("insertimage",C).hideDialog().focus()}},
beforeRemove:function(){o.remove();q&&q.write("");p.remove()}}).div;var t=g('[name="address"]',m),o=g('[name="searchBtn"]',m),l,q;['<!doctype html><html><head>\n<meta name="viewport" content="initial-scale=1.0, user-scalable=no" />\n<style>\n\thtml { height: 100% }\n\tbody { height: 100%; margin: 0; padding: 0; background-color: #FFF }\n\t#map_canvas { height: 100% }\n</style>','<script src="http://maps.googleapis.com/maps/api/js?sensor=false&language='+h.langType+'"><\/script>','<script>\nvar map, geocoder;\nfunction initialize() {\n\tvar latlng = new google.maps.LatLng(31.230393, 121.473704);\n\tvar options = {\n\t\tzoom: 11,\n\t\tcenter: latlng,\n\t\tdisableDefaultUI: true,\n\t\tpanControl: true,\n\t\tzoomControl: true,\n\t\tmapTypeControl: true,\n\t\tscaleControl: true,\n\t\tstreetViewControl: false,\n\t\toverviewMapControl: true,\n\t\tmapTypeId: google.maps.MapTypeId.ROADMAP\n\t};\n\tmap = new google.maps.Map(document.getElementById("map_canvas"), options);\n\tgeocoder = new google.maps.Geocoder();\n\tgeocoder.geocode({latLng: latlng}, function(results, status) {\n\t\tif (status == google.maps.GeocoderStatus.OK) {\n\t\t\tif (results[3]) {\n\t\t\t\tparent.document.getElementById("kindeditor_plugin_map_address").value = results[3].formatted_address;\n\t\t\t}\n\t\t}\n\t});\n}\nfunction search(address) {\n\tif (!map) return;\n\tgeocoder.geocode({address : address}, function(results, status) {\n\t\tif (status == google.maps.GeocoderStatus.OK) {\n\t\t\tmap.setZoom(11);\n\t\t\tmap.setCenter(results[0].geometry.location);\n\t\t\tvar marker = new google.maps.Marker({\n\t\t\t\tmap: map,\n\t\t\t\tposition: results[0].geometry.location\n\t\t\t});\n\t\t} else {\n\t\t\talert("Invalid address: " + address);\n\t\t}\n\t});\n}\n<\/script>\n</head>\n<body onload="initialize();">\n<div id="map_canvas" style="width:100%; height:100%"></div>\n</body></html>'].join("\n");
var p=g('<iframe class="ke-textarea" frameborder="0" src="'+h.pluginsPath+'map/map.html" style="width:558px;height:360px;"></iframe>');p.bind("load",function(){p.unbind("load");g.IE?v():setTimeout(v,0)});g(".ke-map",m).replaceWith(p);o.click(function(){l.search(t.val())})})});
KindEditor.plugin("clearhtml",function(g){var h=this;h.clickToolbar("clearhtml",function(){h.focus();var u=h.html();u=u.replace(/(<script[^>]*>)([\s\S]*?)(<\/script>)/ig,"");u=u.replace(/(<style[^>]*>)([\s\S]*?)(<\/style>)/ig,"");u=g.formatHtml(u,{a:["href","target"],video:["src","width","height","loop","autoplay","muted",".width",".height","align","poster","preload","controls"],iframe:["src","frameborder","width","height",".width",".height","scrolling","border","allow","frameborder","framespacing",
"allowfullscreen"],img:["src","width","height","border","alt","title",".width",".height"],table:["border"],"td,th":["rowspan","colspan"],"div,hr,br,tbody,tr,p,ol,ul,li,blockquote,h1,h2,h3,h4,h5,h6":[]});h.html(u);h.cmd.selection(true);h.addBookmark()})});
KindEditor.plugin("code",function(g){var h=this;h.plugin.code={add:function(){var u=h.lang("code."),v=h.createDialog({name:"code",width:600,title:h.lang("code"),body:'<div style="padding:20px 20px;"><div class="ke-dialog-row"><select class="ke-code-type"><option value="js">JavaScript</option><option value="html">HTML</option><option value="css">CSS</option><option value="java">Java</option><option value="py">Python</option><option value="php">PHP</option><option value="cpp">C/C++</option><option value="bsh">Shell</option><option value="go">Go</option><option value="rb">Ruby</option><option value="pl">Perl</option><option value="cs">C#</option><option value="xml">XML</option><option value="">\u5176\u5b83</option></select></div><textarea class="ke-textarea" style="width:549px;height:260px;"></textarea><div style="color: #909399;position: absolute;left: 21px;bottom: 18px;">\u63d0\u793a\uff1aShift + \u56de\u8f66 \u6362\u884c\u4e0d\u6362\u6bb5</div></div>',yesBtn:{name:h.lang("yes"),
click:function(){var t=g(h.cmd.range.commonAncestor());if(t.name!="body"&&t.name!="hide"&&t.name!="pre"&&t.name!="code"){t=g("<span></span>",h.cmd.doc);t.html("&#8203;");h.cmd.range.insertNode(t[0]).selectNodeContents(t[0]);h.cmd.select();h.cmd.removeformat()}var o=g(".ke-code-type",v.div).val();t=m.val();o='<pre class="prettyprint'+(o===""?"":" lang-"+o)+'"><code>'+g.escape(t)+"</code></pre> ";if(g.trim(t)===""){g.popupMessage(u.pleaseInput);m[0].focus()}else{h.hideDialog();t=g(o,h.cmd.doc);h.cmd.range.insertNode(t[0]).selectNodeContents(t[0]).collapse(false);
h.cmd.select();h.edit.trigger()}}}}),m=g("textarea",v.div);m[0].focus()},edit:function(){h.lang("code.");var u=h.plugin.getSelectedCode(),v="";if(u.name=="pre")v=u.attr("class");else if(u.parent().name=="pre")v=u.parent().attr("class");if(v!=""){var m=[];m=v.split("-");v="";if(m.length==2)v=m[1];m=['<div style="padding:10px 20px;"><div class="ke-dialog-row"><select class="ke-code-type">','<option value="js" '+(v=="js"?' selected = "selected"':"")+">JavaScript</option>",'<option value="html" '+(v==
"html"?' selected = "selected"':"")+">HTML</option>",'<option value="css" '+(v=="css"?' selected = "selected"':"")+">CSS</option>",'<option value="java" '+(v=="java"?' selected = "selected"':"")+">Java</option>",'<option value="py" '+(v=="py"?' selected = "selected"':"")+">Python</option>",'<option value="php" '+(v=="php"?' selected = "selected"':"")+">PHP</option>",'<option value="cpp" '+(v=="cpp"?' selected = "selected"':"")+">C/C++</option>",'<option value="bsh" '+(v=="bsh"?' selected = "selected"':
"")+">Shell</option>",'<option value="go" '+(v=="go"?' selected = "selected"':"")+">Go</option>",'<option value="rb" '+(v=="rb"?' selected = "selected"':"")+">Ruby</option>",'<option value="pl" '+(v=="pl"?' selected = "selected"':"")+">Perl</option>",'<option value="cs" '+(v=="cs"?' selected = "selected"':"")+">C#</option>",'<option value="xml" '+(v=="xml"?' selected = "selected"':"")+">XML</option>",'<option value="" '+(v==""?' selected = "selected"':"")+">\u5176\u5b83</option>","</select></div></div>"].join("");
var t=h.createDialog({name:"code",width:450,title:"\u4fee\u6539\u7a0b\u5e8f\u8bed\u8a00",body:m,yesBtn:{name:h.lang("yes"),click:function(){var l=g(".ke-code-type",t.div).val();o.val();l=l===""?"":" lang-"+l;if(u.name=="pre")u.attr("class","prettyprint"+l);else u.parent().name=="pre"&&u.parent().attr("class","prettyprint"+l);h.hideDialog()}}}),o=g("textarea",t.div)}}};h.clickToolbar("code",h.plugin.code.add)});
KindEditor.plugin("emoticons",function(g){var h=this,u=h.emoticonsPath||h.pluginsPath+"emoticons/twemoji/",v=h.allowPreviewEmoticons===undefined?true:h.allowPreviewEmoticons,m=1;h.clickToolbar("emoticons",function(){function t(V,Z,I){B?V.mouseover(function(){if(Z>Q){B.css("left",0);B.css("right","")}else{B.css("left","");B.css("right",0)}M.attr("src",u+I+".svg");g(this).addClass("ke-on")}):V.mouseover(function(){g(this).addClass("ke-on")});V.mouseout(function(){g(this).removeClass("ke-on")});V.click(function(P){h.insertHtml('<img width="32px" height="32px" src="'+
u+I+'.svg" border="0" alt="" />').hideMenu().focus();P.stop()})}function o(V,Z){var I=document.createElement("table");Z.append(I);if(B){g(I).mouseover(function(){B.show("block")});g(I).mouseout(function(){B.hide()});da.push(g(I))}I.className="ke-table";I.cellPadding=0;I.cellSpacing=0;I.border=0;V=(V-1)*C+H;for(Z=0;Z<r;Z++)for(var P=I.insertRow(Z),aa=0;aa<z;aa++){var R=g(P.insertCell(aa));R.addClass("ke-cell");t(R,aa,V);var W=g('<span class="ke-img"></span>').css("background-position","4px 4px").css("background-size",
"32px 32px").css("background-image","url("+u+V+".svg)");R.append(W);da.push(R);V++}return I}function l(){g.each(da,function(){this.unbind()})}function q(V,Z){V.click(function(I){l();T.parentNode.removeChild(T);G.remove();T=o(Z,J);p(Z);m=Z;I.stop()})}function p(V){G=g('<div class="ke-page"></div>');J.append(G);for(var Z=1;Z<=D;Z++){if(V!==Z){var I=g('<a href="javascript:;">'+Z+"</a>");q(I,Z);G.append(I);da.push(I)}else G.append(g("@<span>"+Z+"</span>"));G.append(g("@&nbsp;"))}}var r=5,z=9,H=0,C=r*
z,D=Math.ceil(148/C),Q=Math.floor(z/2),J=g('<div class="ke-plugin-emoticons"></div>'),da=[];h.createMenu({name:"emoticons",beforeRemove:function(){l()}}).div.append(J);var B,M;if(v){B=g('<div class="ke-preview"></div>').css("right",0);M=g('<img class="ke-preview-img" width="64px" height="64px" src="'+u+H+'.svg" />');J.append(B);B.append(M)}var T=o(m,J),G;p(m)})});
KindEditor.plugin("filemanager",function(g){function h(l,q,p){return l+" ("+Math.ceil(q/1024)+"KB, "+p+")"}function u(l,q){q.is_dir?l.attr("title",q.filename):l.attr("title",h(q.filename,q.filesize,q.datetime))}var v=this,m=g.undef(v.fileManagerJson,v.basePath+"php/file_manager_json.php"),t=v.pluginsPath+"filemanager/images/",o=v.lang("filemanager.");v.plugin.filemanagerDialog=function(l){function q(I,P,aa){I="path="+I+"&order="+P+"&dir="+Q;B.showLoading(v.lang("ajaxLoading"));g.ajax(g.addParam(m,
I+"&"+(new Date).getTime()),function(R){B.hideLoading();aa(R)})}function p(I,P,aa,R){var W=g.formatUrl(P.current_url+aa.filename,"absolute"),X=encodeURIComponent(P.current_dir_path+aa.filename+"/");aa.is_dir?I.click(function(){q(X,V.val(),R)}):I.click(function(){da.call(this,W,aa.filename)});Z.push(I)}function r(I,P){function aa(){G.val()=="VIEW"?q(I.current_dir_path,V.val(),H):q(I.current_dir_path,V.val(),z)}g.each(Z,function(){this.unbind()});T.unbind();G.unbind();V.unbind();I.current_dir_path&&
T.click(function(){q(I.moveup_dir_path,V.val(),P)});G.change(aa);V.change(aa);M.html("")}function z(I){r(I,z);var P=document.createElement("table");P.className="ke-table";P.cellPadding=0;P.cellSpacing=0;P.border=0;M.append(P);for(var aa=I.file_list,R=0,W=aa.length;R<W;R++){var X=aa[R],ha=g(P.insertRow(R));ha.mouseover(function(){g(this).addClass("ke-on")}).mouseout(function(){g(this).removeClass("ke-on")});var fa=g('<img src="'+(t+(X.is_dir?"folder-16.gif":"file-16.gif"))+'" width="16" height="16" alt="'+
X.filename+'" align="absmiddle" />');fa=g(ha[0].insertCell(0)).addClass("ke-cell ke-name").append(fa).append(document.createTextNode(" "+X.filename));if(!X.is_dir||X.has_file){ha.css("cursor","pointer");fa.attr("title",X.filename);p(fa,I,X,z)}else fa.attr("title",o.emptyFolder);g(ha[0].insertCell(1)).addClass("ke-cell ke-size").html(X.is_dir?"-":Math.ceil(X.filesize/1024)+"KB");g(ha[0].insertCell(2)).addClass("ke-cell ke-datetime").html(X.datetime)}}function H(I){r(I,H);for(var P=I.file_list,aa=0,
R=P.length;aa<R;aa++){var W=P[aa],X=g('<div class="ke-inline-block ke-item"></div>');M.append(X);var ha=g('<div class="ke-inline-block ke-photo"></div>').mouseover(function(){g(this).addClass("ke-on")}).mouseout(function(){g(this).removeClass("ke-on")});X.append(ha);var fa=I.current_url+W.filename;fa=g('<img src="'+(W.is_dir?t+"folder-64.gif":W.is_photo?fa:t+"file-64.gif")+'" width="80" height="80" alt="'+W.filename+'" />');if(!W.is_dir||W.has_file){ha.css("cursor","pointer");u(ha,W);p(ha,I,W,H)}else ha.attr("title",
o.emptyFolder);ha.append(fa);X.append('<div class="ke-name" title="'+W.filename+'">'+W.filename+"</div>")}}var C=g.undef(l.width,650),D=g.undef(l.height,510),Q=g.undef(l.dirName,""),J=g.undef(l.viewType,"VIEW").toUpperCase(),da=l.clickFn;l=['<div style="padding:10px 20px;"><div class="ke-plugin-filemanager-header"><div class="ke-left">','<img class="ke-inline-block" name="moveupImg" src="'+t+'go-up.gif" width="16" height="16" border="0" alt="" /> ','<a class="ke-inline-block" name="moveupLink" href="javascript:;">'+
o.moveup+"</a>",'</div><div class="ke-right">',o.viewType+' <select class="ke-inline-block" name="viewType">','<option value="VIEW">'+o.viewImage+"</option>",'<option value="LIST">'+o.listImage+"</option>","</select> ",o.orderType+' <select class="ke-inline-block" name="orderType">','<option value="NAME">'+o.fileName+"</option>",'<option value="SIZE">'+o.fileSize+"</option>",'<option value="TYPE">'+o.fileType+"</option>",'</select></div><div class="ke-clearfix"></div></div><div class="ke-plugin-filemanager-body"></div></div>'].join("");
var B=v.createDialog({name:"filemanager",width:C,height:D,title:v.lang("filemanager"),body:l});C=B.div;var M=g(".ke-plugin-filemanager-body",C);g('[name="moveupImg"]',C);var T=g('[name="moveupLink"]',C);g('[name="viewServer"]',C);var G=g('[name="viewType"]',C),V=g('[name="orderType"]',C),Z=[];G.val(J);q("",V.val(),J=="VIEW"?H:z);return B}});
KindEditor.plugin("flash",function(g){var h=this,u=h.lang("flash."),v=g.undef(h.allowFlashUpload,true),m=g.undef(h.allowFileManager,false),t=g.undef(h.formatUploadUrl,true),o=g.undef(h.extraFileUploadParams,{}),l=g.undef(h.filePostName,"imgFile"),q=g.undef(h.uploadJson,h.basePath+"php/upload_json.php"),p=g.undef(h.uploadModule,h.basePath+"php/upload_json.php");h.plugin.flash={edit:function(){var r=['<div style="padding:20px;"><div class="ke-dialog-row">','<label for="keUrl" style="width:60px;">'+
u.url+"</label>",'<input class="ke-input-text" type="text" id="keUrl" name="url" value="" style="width:160px;" /> &nbsp;','<input type="button" class="ke-upload-button" value="'+u.upload+'" /> &nbsp;','<span class="ke-button-common ke-button-outer">','<input type="button" class="ke-button-common ke-button" name="viewServer" value="'+u.viewServer+'" />','</span></div><div class="ke-dialog-row">','<label for="keWidth" style="width:60px;">'+u.width+"</label>",'<input type="text" id="keWidth" class="ke-input-text ke-input-number" name="width" value="550" maxlength="4" /> </div><div class="ke-dialog-row">',
'<label for="keHeight" style="width:60px;">'+u.height+"</label>",'<input type="text" id="keHeight" class="ke-input-text ke-input-number" name="height" value="400" maxlength="4" /> </div></div>'].join(""),z=h.createDialog({name:"flash",width:450,title:h.lang("flash"),body:r,yesBtn:{name:h.lang("yes"),click:function(){var B=g.trim(C.val()),M=D.val(),T=Q.val();if(B=="http://"||g.invalidUrl(B)){g.popupMessage(h.lang("invalidUrl"));C[0].focus()}else if(/^\d*$/.test(M))if(/^\d*$/.test(T)){B=g.mediaImg(h.themesPath+
"common/blank.gif",{src:B,type:g.mediaType(".swf"),width:M,height:T,quality:"high"});h.insertHtml(B).hideDialog().focus()}else{g.popupMessage(h.lang("invalidHeight"));Q[0].focus()}else{g.popupMessage(h.lang("invalidWidth"));D[0].focus()}}}}),H=z.div,C=g('[name="url"]',H);r=g('[name="viewServer"]',H);var D=g('[name="width"]',H),Q=g('[name="height"]',H);C.val("http://");if(v){var J=g.uploadbutton({button:g(".ke-upload-button",H)[0],fieldName:l,extraParams:o,url:g.addParam(q,"dir=flash"),uploadModule:p,
afterUpload:function(B){z.hideLoading();if(B.error===0){var M=B.url;if(t)M=g.formatUrl(M,"absolute");C.val(M);h.afterUpload&&h.afterUpload.call(h,M,B,"flash");g.popupMessage(h.lang("uploadSuccess"))}else g.popupMessage(B.message)},afterError:function(B){z.hideLoading();h.errorDialog(B)}});J.fileBox.change(function(){z.showLoading(h.lang("uploadLoading"));J.submit()})}else g(".ke-upload-button",H).hide();m?r.click(function(){h.loadPlugin("filemanager",function(){h.plugin.filemanagerDialog({viewType:"LIST",
dirName:"flash",clickFn:function(B){if(h.dialogs.length>1){g('[name="url"]',H).val(B);h.afterSelectFile&&h.afterSelectFile.call(h,B);h.hideDialog()}}})})}):r.hide();if(r=h.plugin.getSelectedFlash()){var da=g.mediaAttrs(r.attr("data-ke-tag"));C.val(da.src);D.val(g.removeUnit(r.css("width"))||da.width||0);Q.val(g.removeUnit(r.css("height"))||da.height||0)}C[0].focus();C[0].select()},"delete":function(){h.plugin.getSelectedFlash().remove();h.addBookmark()}};h.clickToolbar("flash",h.plugin.flash.edit)});
KindEditor.plugin("image",function(g){var h=this,u=g.undef(h.allowImageUpload,true),v=g.undef(h.allowImageRemote,true),m=g.undef(h.formatUploadUrl,true),t=g.undef(h.allowFileManager,false),o=g.undef(h.uploadJson,h.basePath+"php/upload_json.php"),l=g.undef(h.uploadModule,0),q=g.undef(h.imageTabIndex,0),p=h.pluginsPath+"image/images/",r=g.undef(h.extraFileUploadParams,{}),z=g.undef(h.filePostName,"imgFile"),H=g.undef(h.fillDescAfterUploadImage,false),C=h.lang("image.");h.plugin.imageDialog=function(D){function Q(ba,
sa){W.val(ba);X.val(sa);va=ba;wa=sa}g.undef(D.imageWidth,"");g.undef(D.imageHeight,"");g.undef(D.imageTitle,"");g.undef(D.imageAlign,"");var J=g.undef(D.showRemote,true),da=g.undef(D.showLocal,true),B=g.undef(D.tabIndex,0),M=D.clickFn,T="kindeditor_upload_iframe_"+(new Date).getTime(),G=[];for(var V in r)G.push('<input type="hidden" name="'+V+'" value="'+r[V]+'" />');G=['<div style="padding:20px;"><div class="tabs"></div><div class="tab1" style="display:none;"><div class="ke-dialog-row">','<label for="remoteUrl" style="width:60px;">'+
C.remoteUrl+"</label>",'<input type="text" id="remoteUrl" class="ke-input-text" name="url" value="" style="width:200px;" /> &nbsp;<span class="ke-button-common ke-button-outer">','<input type="button" class="ke-button-common ke-button" name="viewServer" value="'+C.viewServer+'" />','</span></div><div class="ke-dialog-row" style="display: none;">','<label for="remoteWidth" style="width:60px;">'+C.size+"</label>",C.width+' <input type="text" id="remoteWidth" class="ke-input-text ke-input-number" name="width" value="" maxlength="4" /> ',
C.height+' <input type="text" class="ke-input-text ke-input-number" name="height" value="" maxlength="4" /> ','<img class="ke-refresh-btn" src="'+p+'refresh.png" width="16" height="16" alt="" style="cursor:pointer;" title="'+C.resetSize+'" />','</div><div class="ke-dialog-row" style="display: none;">','<label style="width:60px;">'+C.align+"</label>",'<input type="radio" name="align" class="ke-inline-block" value="" checked="checked" /> <img name="defaultImg" src="'+p+'align_top.gif" width="23" height="25" alt="" />',
' <input type="radio" name="align" class="ke-inline-block" value="left" /> <img name="leftImg" src="'+p+'align_left.gif" width="23" height="25" alt="" />',' <input type="radio" name="align" class="ke-inline-block" value="right" /> <img name="rightImg" src="'+p+'align_right.gif" width="23" height="25" alt="" />','</div><div class="ke-dialog-row">','<label for="remoteTitle" style="width:60px;">'+C.imgTitle+"</label>",'<input type="text" id="remoteTitle" class="ke-input-text" name="title" value="" style="width:200px;" /></div></div><div class="tab2" style="display:none;">',
'<iframe name="'+T+'" style="display:none;"></iframe>','<form class="ke-upload-area ke-form" method="post" enctype="multipart/form-data" target="'+T+'" action="'+g.addParam(o,"dir=image")+'">','<div class="ke-dialog-row">',G.join(""),'<label style="width:60px;">'+C.localUrl+"</label>",'<input type="text" name="localUrl" class="ke-input-text" tabindex="-1" style="width:200px;" readonly="true" /> &nbsp;','<input type="button" class="ke-upload-button" value="'+C.upload+'" />',"</div></form></div></div>"].join("");
var Z=h.createDialog({name:"image",width:da||t?450:400,height:da&&J?300:250,title:h.lang("image"),body:G,yesBtn:{name:h.lang("yes"),click:function(){if(!Z.isLoading)if(da&&J&&ra&&ra.selectedIndex===1||!J)if(ua.fileBox.val()=="")g.popupMessage(h.lang("pleaseSelectFile"));else{Z.showLoading(h.lang("uploadLoading"));ua.submit();aa.val("")}else{var ba=g.trim(P.val()),sa=W.val(),Ma=X.val(),Na=fa.val(),Aa="";ma.each(function(){if(this.checked){Aa=this.value;return false}});if(ba=="http://"||g.invalidUrl(ba)){g.popupMessage(h.lang("invalidUrl"));
P[0].focus()}else if(/^\d*$/.test(sa))if(/^\d*$/.test(Ma))M.call(h,ba,Na,sa,Ma,0,Aa);else{g.popupMessage(h.lang("invalidHeight"));X[0].focus()}else{g.popupMessage(h.lang("invalidWidth"));W[0].focus()}}}},beforeRemove:function(){R.unbind();W.unbind();X.unbind();ha.unbind()}}),I=Z.div,P=g('[name="url"]',I),aa=g('[name="localUrl"]',I),R=g('[name="viewServer"]',I),W=g('.tab1 [name="width"]',I),X=g('.tab1 [name="height"]',I),ha=g(".ke-refresh-btn",I),fa=g('.tab1 [name="title"]',I),ma=g('.tab1 [name="align"]',
I),ra;if(J&&da){ra=g.tabs({src:g(".tabs",I),afterSelect:function(){}});ra.add({title:C.remoteImage,panel:g(".tab1",I)});ra.add({title:C.localImage,panel:g(".tab2",I)});ra.select(B)}else if(J)g(".tab1",I).show();else da&&g(".tab2",I).show();var ua=g.uploadbutton({button:g(".ke-upload-button",I)[0],fieldName:z,form:g(".ke-form",I),target:T,width:70,uploadModule:l,afterUpload:function(ba){Z.hideLoading();if(ba.error===0){var sa=ba.url;if(m)sa=g.formatUrl(sa,"absolute");h.afterUpload&&h.afterUpload.call(h,
sa,ba,"image");if(H){g(".ke-dialog-row #remoteUrl",I).val(sa);g(".ke-tabs-li",I)[0].click();g(".ke-refresh-btn",I).click()}else M.call(h,sa,ba.title,ba.width,ba.height,ba.border,ba.align);g('<img src="'+sa+'" />',document).each(function(){var Ma=new Image;Ma.src=this.src;Ma.onload=function(){h.edit.trigger()}})}else g.popupMessage(ba.message)},afterError:function(ba){Z.hideLoading();h.errorDialog(ba)}});ua.fileBox.change(function(){aa.val(ua.fileBox.val())});t?R.click(function(){h.loadPlugin("filemanager",
function(){h.plugin.filemanagerDialog({viewType:"VIEW",dirName:"image",clickFn:function(ba){if(h.dialogs.length>1){g('[name="url"]',I).val(ba);h.afterSelectFile&&h.afterSelectFile.call(h,ba);h.hideDialog()}}})})}):R.hide();var va=0,wa=0;ha.click(function(){var ba=g('<img src="'+P.val()+'" />',document).css({position:"absolute",visibility:"hidden",top:0,left:"-1000px"});ba.bind("load",function(){Q(ba.width(),ba.height());ba.remove()});g(document.body).append(ba)});W.change(function(){va>0&&X.val(Math.round(wa/
va*parseInt(this.value,10)))});X.change(function(){wa>0&&W.val(Math.round(va/wa*parseInt(this.value,10)))});P.val(D.imageUrl);Q(D.imageWidth,D.imageHeight);fa.val(D.imageTitle);ma.each(function(){if(this.value===D.imageAlign){this.checked=true;return false}});if(J&&B===0){P[0].focus();P[0].select()}return Z};h.plugin.image={edit:function(){var D=h.plugin.getSelectedImage();h.plugin.imageDialog({imageUrl:D?D.attr("data-ke-src"):"http://",imageWidth:D?D.width():"",imageHeight:D?D.height():"",imageTitle:D?
D.attr("title"):"",imageAlign:D?D.attr("align"):"",showRemote:v,showLocal:u,tabIndex:D?0:q,clickFn:function(Q,J,da,B,M,T){if(D){D.attr("src",Q);D.attr("data-ke-src",Q);D.attr("width",da);D.attr("height",B);D.attr("title",J);D.attr("align",T);D.attr("alt",J)}else h.exec("insertimage",Q,J,da,B,M,T);setTimeout(function(){h.hideDialog().focus()},0);h.edit.trigger()}})},"delete":function(){var D=h.plugin.getSelectedImage();if(D.parent().name=="a")D=D.parent();D.remove();h.addBookmark()}};h.clickToolbar("image",
h.plugin.image.edit)});
KindEditor.plugin("insertfile",function(g){var h=this,u=g.undef(h.allowFileUpload,true),v=g.undef(h.allowFileManager,false),m=g.undef(h.formatUploadUrl,true),t=g.undef(h.uploadJson,h.basePath+"php/upload_json.php"),o=g.undef(h.uploadModule,0),l=g.undef(h.extraFileUploadParams,{}),q=g.undef(h.filePostName,"imgFile"),p=h.lang("insertfile.");h.plugin.fileDialog=function(r){var z=g.undef(r.fileUrl,"http://"),H=g.undef(r.fileTitle,""),C=r.clickFn;r=['<div style="padding:20px;"><div class="ke-dialog-row">','<label for="keUrl" style="width:60px;">'+
p.url+"</label>",'<input type="text" id="keUrl" name="url" class="ke-input-text" style="width:160px;" /> &nbsp;','<input type="button" class="ke-upload-button" value="'+p.upload+'" /> &nbsp;','<span class="ke-button-common ke-button-outer">','<input type="button" class="ke-button-common ke-button" name="viewServer" value="'+p.viewServer+'" />','</span></div><div class="ke-dialog-row">','<label for="keTitle" style="width:60px;">'+p.title+"</label>",'<input type="text" id="keTitle" class="ke-input-text" name="title" value="" style="width:160px;" /></div></div></form></div>'].join("");
var D=h.createDialog({name:"insertfile",width:450,title:h.lang("insertfile"),body:r,yesBtn:{name:h.lang("yes"),click:function(){var M=g.trim(J.val()),T=da.val();if(M=="http://"||g.invalidUrl(M)){g.popupMessage(h.lang("invalidUrl"));J[0].focus()}else{if(g.trim(T)==="")T=M;C.call(h,M,T);h.edit.trigger()}}}}),Q=D.div,J=g('[name="url"]',Q);r=g('[name="viewServer"]',Q);var da=g('[name="title"]',Q);if(u){var B=g.uploadbutton({button:g(".ke-upload-button",Q)[0],fieldName:q,url:g.addParam(t,"dir=file"),extraParams:l,
uploadModule:o,afterUpload:function(M){D.hideLoading();if(M.error===0){var T=M.url;if(m)T=g.formatUrl(T,"absolute");J.val(T);da.val(M.title);h.afterUpload&&h.afterUpload.call(h,T,M,"insertfile");g.popupMessage(h.lang("uploadSuccess"))}else g.popupMessage(M.message)},afterError:function(M){D.hideLoading();h.errorDialog(M)}});B.fileBox.change(function(){D.showLoading(h.lang("uploadLoading"));B.submit()})}else g(".ke-upload-button",Q).hide();v?r.click(function(){h.loadPlugin("filemanager",function(){h.plugin.filemanagerDialog({viewType:"LIST",
dirName:"file",clickFn:function(M){if(h.dialogs.length>1){g('[name="url"]',Q).val(M);h.afterSelectFile&&h.afterSelectFile.call(h,M);h.hideDialog()}}})})}):r.hide();J.val(z);da.val(H);J[0].focus();J[0].select()};h.clickToolbar("insertfile",function(){h.plugin.fileDialog({clickFn:function(r,z){h.insertHtml('<a class="ke-insertfile" href="'+r+'" data-ke-src="'+r+'" target="_blank">'+z+"</a>").hideDialog().focus()}})})});
KindEditor.plugin("lineheight",function(g){var h=this,u=h.lang("lineheight.");h.clickToolbar("lineheight",function(){var v="",m=h.cmd.commonNode({"*":".line-height"});if(m)v=m.css("line-height");var t=h.createMenu({name:"lineheight",width:150});g.each(u.lineHeight,function(o,l){g.each(l,function(q,p){t.addItem({title:p,checked:v===q,click:function(){h.cmd.toggle('<span style="line-height:'+q+';"></span>',{span:".line-height="+q});h.updateState();h.addBookmark();h.hideMenu()}})})})})});
KindEditor.plugin("link",function(g){var h=this;h.plugin.link={edit:function(){var u=h.lang("link."),v='<div style="padding:20px;"><div class="ke-dialog-row"><label for="keUrl" style="width:60px;">'+u.url+'</label><input class="ke-input-text" type="text" id="keUrl" name="url" value="" style="width:260px;" /></div><div class="ke-dialog-row""><label for="keType" style="width:60px;">'+u.linkType+'</label><select id="keType" name="type"></select></div></div>';v=h.createDialog({name:"link",width:450,title:h.lang("link"),
body:v,yesBtn:{name:h.lang("yes"),click:function(){var o=g.trim(m.val());if(o=="http://"||g.invalidUrl(o)){g.popupMessage(h.lang("invalidUrl"));m[0].focus()}else h.exec("createlink",o,t.val()).hideDialog().focus()}}}).div;var m=g('input[name="url"]',v),t=g('select[name="type"]',v);m.val("http://");t[0].options[0]=new Option(u.newWindow,"_blank");t[0].options[1]=new Option(u.selfWindow,"");h.cmd.selection();if(u=h.plugin.getSelectedLink()){h.cmd.range.selectNode(u[0]);h.cmd.select();m.val(u.attr("data-ke-src"));
t.val(u.attr("target"))}m[0].focus();m[0].select()},"delete":function(){h.exec("unlink",null)}};h.clickToolbar("link",h.plugin.link.edit)});
KindEditor.plugin("media",function(g){var h=this,u=h.lang("media."),v=g.undef(h.allowMediaUpload,true),m=g.undef(h.allowFileManager,false),t=g.undef(h.formatUploadUrl,true),o=g.undef(h.extraFileUploadParams,{}),l=g.undef(h.filePostName,"imgFile"),q=g.undef(h.uploadJson,h.basePath+"php/upload_json.php"),p=g.undef(h.uploadModule,0),r=g.undef(h.items,[]),z=g.undef(0,0);h.plugin.media={edit:function(){var H=['<div style="padding:20px;"><div class="tabs"></div><div class="tab1" style="display:none;"><textarea name="videoCode" class="ke-textarea" style="width:449px;height:130px;" placeholder="\u4ee3\u7801\u683c\u5f0f\u5982\uff1a<iframe src=https://.. ></iframe>"></textarea></div><div class="tab2" style="display:none;"><div class="ke-dialog-row">',
'<label for="keUrl" style="width:60px;">'+u.url+"</label>",'<input class="ke-input-text" type="text" id="keUrl" name="url" value="" style="width:160px;" /> &nbsp;','<input type="button" class="ke-upload-button" value="'+u.upload+'" /> &nbsp;','<span class="ke-button-common ke-button-outer">','<input type="button" class="ke-button-common ke-button" name="viewServer" value="'+u.viewServer+'" />','</span></div><div class="ke-dialog-row" style="display: none;">','<label for="keWidth" style="width:60px;">'+
u.width+"</label>",'<input type="text" id="keWidth" class="ke-input-text ke-input-number" name="width" value="550" maxlength="4" /></div><div class="ke-dialog-row" style="display: none;">','<label for="keHeight" style="width:60px;">'+u.height+"</label>",'<input type="text" id="keHeight" class="ke-input-text ke-input-number" name="height" value="400" maxlength="4" /></div><div class="ke-dialog-row" style="display: none;">','<label for="keAutostart">'+u.autostart+"</label>",'<input type="checkbox" id="keAutostart" name="autostart" value="" /> </div></div></div>'].join(""),
C=h.createDialog({name:"media",width:500,height:320,title:h.lang("media"),body:H,yesBtn:{name:h.lang("yes"),click:function(){var R=g.trim(Q.val()),W=da.val(),X=B.val(),ha=M.val();if(G==0)if(g.trim(ha)===""){g.popupMessage(h.lang("invalidVideoCode"));M[0].focus()}else{var fa=g(g(ha).get(0));if(fa==null){g.popupMessage(h.lang("invalidVideoCodeFormat"));M[0].focus()}else if(fa.name.toLowerCase()=="iframe"){R=fa.attr("src");W=fa.attr("scrolling");X=fa.attr("border");ha=fa.attr("allow");var ma=fa.attr("frameborder"),
ra=fa.attr("framespacing");fa=fa.attr("allowfullscreen");R=g.embedVideoImg(h.themesPath+"common/blank.gif",{src:R,scrolling:W,border:X,allow:ha,width:"",height:"",frameborder:ma,framespacing:ra,allowfullscreen:fa});h.insertHtml(R).hideDialog().focus();h.edit.trigger()}else{g.popupMessage(h.lang("invalidVideoCodeFormat"));M[0].focus()}}else if(R=="http://"||g.invalidUrl(R)){g.popupMessage(h.lang("invalidUrl"));Q[0].focus()}else if(/^\d*$/.test(W))if(/^\d*$/.test(X)){R=g.mediaImg(h.themesPath+"common/blank.gif",
{src:R,type:g.mediaType(R),width:W,height:X,controls:"controls"});h.insertHtml(R).hideDialog().focus();h.edit.trigger()}else{g.popupMessage(h.lang("invalidHeight"));B[0].focus()}else{g.popupMessage(h.lang("invalidWidth"));da[0].focus()}}}}),D=C.div,Q=g('[name="url"]',D),J=g('[name="viewServer"]',D),da=g('[name="width"]',D),B=g('[name="height"]',D);H=g('[name="autostart"]',D);var M=g('[name="videoCode"]',D);Q.val("http://");var T,G;T=g.tabs({src:g(".tabs",D),afterSelect:function(R){G=R}});for(var V=
"",Z="",I=0;I<r.length;I++){var P=r[I];if(P=="embedVideo")V="\u5d4c\u5165\u89c6\u9891";if(P=="uploadVideo")Z="\u4e0a\u4f20\u89c6\u9891"}T.add({title:V,panel:g(".tab1",D)});T.add({title:Z,panel:g(".tab2",D)});if(Z!="")z=1;if(V!="")z=0;T.select(z);g(".tab"+(z+1),D).show();if(v){var aa=g.uploadbutton({button:g(".ke-upload-button",D)[0],fieldName:l,extraParams:o,width:60,url:g.addParam(q,"dir=media"),uploadModule:p,afterUpload:function(R){C.hideLoading();if(R.error===0){var W=R.url;if(t)W=g.formatUrl(W,
"absolute");Q.val(W);h.afterUpload&&h.afterUpload.call(h,W,R,"media");g.popupMessage(h.lang("uploadSuccess"))}else g.popupMessage(R.message)},afterError:function(R){C.hideLoading();h.errorDialog(R)}});aa.fileBox.change(function(){C.showLoading(h.lang("uploadLoading"));aa.submit()})}else g(".ke-upload-button",D).hide();m?J.click(function(){h.loadPlugin("filemanager",function(){h.plugin.filemanagerDialog({viewType:"LIST",dirName:"media",clickFn:function(R){if(h.dialogs.length>1){g('[name="url"]',D).val(R);
h.afterSelectFile&&h.afterSelectFile.call(h,R);h.hideDialog()}}})})}):J.hide();if(J=h.plugin.getSelectedMedia()){T=g.mediaAttrs(J.attr("data-ke-tag"));Q.val(T.src);da.val(g.removeUnit(J.css("width"))||T.width||0);B.val(g.removeUnit(J.css("height"))||T.height||0);H[0].checked=T.autostart==="true"}Q[0].focus();Q[0].select()},"delete":function(){h.plugin.getSelectedMedia().remove();h.addBookmark()}};h.clickToolbar("media",h.plugin.media.edit)});
(function(g){function h(u){this.init(u)}g.extend(h,{init:function(u){function v(o,l){g(".ke-status > div",o).hide();g(".ke-message",o).addClass("ke-error").show().html(g.escape(l))}var m=this;u.afterError=u.afterError||function(o){g.popupMessage(o)};m.options=u;m.progressbars={};m.div=g(u.container).html('<div class="ke-swfupload"><div class="ke-swfupload-top"><div class="ke-inline-block ke-swfupload-button ke-swfupload-desc"><span class="ke-button-common ke-button-outer ke-swfupload-selectFile"><input type="button" class="ke-button-common ke-button" value="\u6dfb\u52a0\u56fe\u7247" /></span></div><div class="ke-inline-block ke-swfupload-desc"></div></div><div class="ke-swfupload-body"></div></div>');
m.bodyDiv=g(".ke-swfupload-body",m.div);if(u.uploadModule==10){var t=WebUploader.create({swf:u.flashUrl,server:"",method:"POST",pick:g(".ke-swfupload-button > span",m.div)[0],compress:false,auto:true,accept:{title:m.options.fileTypesDesc,extensions:m.options.extensions,mimeTypes:"image/*"}});t.on("uploadBeforeSend",function(o,l){var q="fileName="+encodeURIComponent(l.name);g.post_ajax(function(p){p=p.responseText;if(p!=""){p=JSON.parse(p);if(p.error==0){var r=p.url;p=r.substring(0,r.indexOf("?"));
r=r.substring(r.indexOf("?")+1,r.length);o.blob.source.server=p;p=r.split("&");for(r=0;r<p.length;r++){var z=p[r].split("=");l[z[0]]=decodeURIComponent(z[1]);if(z[0]=="key")o.blob.source.key=decodeURIComponent(z[1])}}else g.popupMessage(p.message)}},u.uploadUrl+"&"+q,false,"",true)});t.on("fileQueued",function(o){o.url=m.options.fileIconUrl;m.appendFile(o);o=g('div[data-id="'+o.id+'"]',m.bodyDiv);g(".ke-status > div",o).hide();g(".ke-progressbar",o).show()});t.on("uploadProgress",function(o,l){l=
Math.round(l*100);o=m.progressbars[o.id];o.bar.css("width",Math.round(l*80/100)+"px");o.percent.html(l+"%")});t.on("uploadSuccess",function(o,l){var q=g('div[data-id="'+o.id+'"]',m.bodyDiv).eq(0);if(l._headers!=undefined){l=o.source.source.server+o.source.source.key;var p={};p.error=0;p.url=l;p.title=o.name;o.url=l;g(".ke-img",q).attr("src",l).attr("data-status","complete").data("data",p);g(".ke-status > div",q).hide()}else v(q,g.DEBUG?l.message:m.options.errorMessage)});t.on("uploadError",function(o){if(o){o=
g('div[data-id="'+o.id+'"]',m.bodyDiv).eq(0);v(o,m.options.errorMessage)}});t.on("uploadComplete",function(o){o=g('div[data-id="'+o.id+'"]',m.bodyDiv);g(".ke-progressbar",o).hide()})}else if(u.uploadModule==20){t=WebUploader.create({swf:u.flashUrl,server:"",method:"POST",pick:g(".ke-swfupload-button > span",m.div)[0],compress:false,auto:true,accept:{title:m.options.fileTypesDesc,extensions:m.options.extensions,mimeTypes:"image/*"}});t.on("uploadBeforeSend",function(o,l){var q="fileName="+encodeURIComponent(l.name);
g.post_ajax(function(p){p=p.responseText;if(p!=""){p=JSON.parse(p);if(p.error==0){var r=p.url;p=r.substring(0,r.indexOf("?"));r=r.substring(r.indexOf("?")+1,r.length);o.blob.source.server=p;p=r.split("&");for(r=0;r<p.length;r++){var z=p[r].split("=");l[z[0]]=decodeURIComponent(z[1])}}else g.popupMessage(p.message)}},u.uploadUrl+"&"+q,false,"",true)});t.on("fileQueued",function(o){o.url=m.options.fileIconUrl;m.appendFile(o);o=g('div[data-id="'+o.id+'"]',m.bodyDiv);g(".ke-status > div",o).hide();g(".ke-progressbar",
o).show()});t.on("uploadProgress",function(o,l){l=Math.round(l*100);o=m.progressbars[o.id];o.bar.css("width",Math.round(l*80/100)+"px");o.percent.html(l+"%")});t.on("uploadSuccess",function(o,l){var q=g('div[data-id="'+o.id+'"]',m.bodyDiv).eq(0);if(l._headers.etag!=undefined){var p={};p.error=0;p.url=l._headers.location;p.title=o.name;o.url=l._headers.location;g(".ke-img",q).attr("src",l._headers.location).attr("data-status","complete").data("data",p);g(".ke-status > div",q).hide()}else v(q,g.DEBUG?
l.message:m.options.errorMessage)});t.on("uploadError",function(o){if(o){o=g('div[data-id="'+o.id+'"]',m.bodyDiv).eq(0);v(o,m.options.errorMessage)}});t.on("uploadComplete",function(o){o=g('div[data-id="'+o.id+'"]',m.bodyDiv);g(".ke-progressbar",o).hide()})}else if(u.uploadModule==30){t=WebUploader.create({swf:u.flashUrl,server:"",method:"POST",pick:g(".ke-swfupload-button > span",m.div)[0],compress:false,auto:true,accept:{title:m.options.fileTypesDesc,extensions:m.options.extensions,mimeTypes:"image/*"}});
t.on("uploadBeforeSend",function(o,l){var q="fileName="+encodeURIComponent(l.name);g.post_ajax(function(p){p=p.responseText;if(p!=""){p=JSON.parse(p);if(p.error==0){var r=p.url;p=r.substring(0,r.indexOf("?"));r=r.substring(r.indexOf("?")+1,r.length);o.blob.source.server=p;p=r.split("&");for(r=0;r<p.length;r++){var z=p[r].split("=");l[z[0]]=decodeURIComponent(z[1]);if(z[0]=="key")o.blob.source.key=decodeURIComponent(z[1])}}else g.popupMessage(p.message)}},u.uploadUrl+"&"+q,false,"",true)});t.on("fileQueued",
function(o){o.url=m.options.fileIconUrl;m.appendFile(o);o=g('div[data-id="'+o.id+'"]',m.bodyDiv);g(".ke-status > div",o).hide();g(".ke-progressbar",o).show()});t.on("uploadProgress",function(o,l){l=Math.round(l*100);o=m.progressbars[o.id];o.bar.css("width",Math.round(l*80/100)+"px");o.percent.html(l+"%")});t.on("uploadSuccess",function(o,l){var q=g('div[data-id="'+o.id+'"]',m.bodyDiv).eq(0);if(l._headers!=undefined){l=o.source.source.server+o.source.source.key;var p={};p.error=0;p.url=l;p.title=o.name;
o.url=l;g(".ke-img",q).attr("src",l).attr("data-status","complete").data("data",p);g(".ke-status > div",q).hide()}else v(q,g.DEBUG?l.message:m.options.errorMessage)});t.on("uploadError",function(o){if(o){o=g('div[data-id="'+o.id+'"]',m.bodyDiv).eq(0);v(o,m.options.errorMessage)}});t.on("uploadComplete",function(o){o=g('div[data-id="'+o.id+'"]',m.bodyDiv);g(".ke-progressbar",o).hide()})}else{t=WebUploader.create({swf:u.flashUrl,server:u.uploadUrl,pick:g(".ke-swfupload-button > span",m.div)[0],compress:false,
auto:true,fileVal:u.filePostName,accept:{title:m.options.fileTypesDesc,extensions:m.options.extensions,mimeTypes:"image/*"}});t.on("uploadBeforeSend",function(o,l,q){o=window.sessionStorage.getItem("oauth2Token");if(o!=null){o=JSON.parse(o);$.extend(q,{Authorization:"Bearer "+o.access_token})}o=g.getCookie("XSRF-TOKEN");$.extend(q,{"X-XSRF-TOKEN":o})});t.on("fileQueued",function(o){o.url=m.options.fileIconUrl;m.appendFile(o);o=g('div[data-id="'+o.id+'"]',m.bodyDiv);g(".ke-status > div",o).hide();
g(".ke-progressbar",o).show()});t.on("uploadProgress",function(o,l){l=Math.round(l*100);o=m.progressbars[o.id];o.bar.css("width",Math.round(l*80/100)+"px");o.percent.html(l+"%")});t.on("uploadSuccess",function(o,l){var q=g('div[data-id="'+o.id+'"]',m.bodyDiv).eq(0);if(l.error!==0)v(q,g.DEBUG?l.message:m.options.errorMessage);else{o.url=l.url;g(".ke-img",q).attr("src",o.url).attr("data-status","complete").data("data",l);g(".ke-status > div",q).hide()}});t.on("uploadError",function(o){if(o){o=g('div[data-id="'+
o.id+'"]',m.bodyDiv).eq(0);v(o,m.options.errorMessage)}});t.on("uploadComplete",function(o){o=g('div[data-id="'+o.id+'"]',m.bodyDiv);g(".ke-progressbar",o).hide()})}m.swfu=t},getUrlList:function(){var u=[];g(".ke-img",self.bodyDiv).each(function(){var v=g(this);v.attr("data-status")=="complete"&&u.push(v.data("data"))});return u},removeFile:function(u){this.swfu.cancelFile(u);u=g('div[data-id="'+u+'"]',this.bodyDiv);g(".ke-photo",u).unbind();g(".ke-delete",u).unbind();u.remove()},removeFiles:function(){var u=
this;g(".ke-item",u.bodyDiv).each(function(){u.removeFile(g(this).attr("data-id"))})},appendFile:function(u){var v=this,m=g('<div class="ke-inline-block ke-item" data-id="'+u.id+'"></div>');v.bodyDiv.append(m);var t=g('<div class="ke-inline-block ke-photo"></div>').mouseover(function(){g(this).addClass("ke-on")}).mouseout(function(){g(this).removeClass("ke-on")});m.append(t);var o=g('<img src="'+u.url+'" class="ke-img" data-status="queued" width="80" height="80" alt="'+u.name+'" />');t.append(o);
g('<span class="ke-delete fa fa-close"></span>').appendTo(t).click(function(){v.removeFile(u.id)});o=g('<div class="ke-status"></div>').appendTo(t);g('<div class="ke-progressbar"><div class="ke-progressbar-bar"><div class="ke-progressbar-bar-inner"></div></div><div class="ke-progressbar-percent">0%</div></div>').hide().appendTo(o);g('<div class="ke-message">'+v.options.pendingMessage+"</div>").appendTo(o);m.append('<div class="ke-name">'+u.name+"</div>");v.progressbars[u.id]={bar:g(".ke-progressbar-bar-inner",
t),percent:g(".ke-progressbar-percent",t)}},remove:function(){this.removeFiles();this.swfu.destroy();this.div.html("")}});g.swfupload=function(u,v){return new h(u,v)}})(KindEditor);
KindEditor.plugin("multiimage",function(g){var h=this;g.undef(h.formatUploadUrl,true);var u=g.undef(h.uploadJson,h.basePath+"php/upload_json.php");g.undef(h.uploadModule,0);var v=h.pluginsPath+"multiimage/images/",m=g.undef(h.extensions,"jpg,jpeg,png,gif,png,bmp"),t=g.undef(h.imageSizeLimit,"1MB"),o=g.undef(h.fileNumLimit,20),l=g.undef(h.filePostName,"imgFile"),q=h.lang("multiimage.");g.loadScript(g.options.pluginsPath+"multiimage/webuploader.min.js");g.loadStyle(g.options.pluginsPath+"multiimage/webuploader.css");
h.plugin.multiImageDialog=function(p){var r=p.clickFn;p=g.tmpl(q.uploadDesc,{uploadLimit:o,sizeLimit:t});var z=h.createDialog({name:"multiimage",width:650,height:540,title:h.lang("multiimage"),body:'<div style="padding:20px;"><div class="swfupload"></div></div>',previewBtn:{name:q.insertAll,click:function(){r.call(h,H.getUrlList());for(var C=0;C<H.getUrlList().length;C++){var D=H.getUrlList()[C];g('<img src="'+D.url+'" />',document).each(function(){var Q=new Image;Q.src=D.url;Q.onload=function(){h.edit.trigger()}})}}},
yesBtn:{name:q.clearAll,click:function(){H.removeFiles()}},beforeRemove:function(){if(!g.IE||g.V<=8)H.remove()}}),H=g.swfupload({container:g(".swfupload",z.div),buttonImageUrl:v+(h.langType=="zh-CN"?"select-files-zh-CN.png":"select-files-en.png"),buttonWidth:h.langType=="zh-CN"?72:88,buttonHeight:23,fileIconUrl:v+"image.png",uploadModule:h.uploadModule,uploadDesc:p,startButtonValue:q.startUpload,uploadUrl:g.addParam(u,"dir=image"),flashUrl:h.pluginsPath+"multiimage/Uploader.swf",filePostName:l,fileTypes:"*.jpg;*.jpeg;*.gif;*.png;*.bmp",
extensions:m,fileTypesDesc:"Image Files",fileNumLimit:o,fileSizeLimit:t,postParams:g.undef(h.extraFileUploadParams,{}),queueLimitExceeded:q.queueLimitExceeded,fileExceedsSizeLimit:q.fileExceedsSizeLimit,zeroByteFile:q.zeroByteFile,invalidFiletype:q.invalidFiletype,unknownError:q.unknownError,pendingMessage:q.pending,errorMessage:q.uploadError,afterError:function(C){h.errorDialog(C)}});return z};h.clickToolbar("multiimage",function(){WebUploader.Uploader.support()?h.plugin.multiImageDialog({clickFn:function(p){if(p.length!==
0){g.each(p,function(r,z){h.afterUpload&&h.afterUpload.call(h,z.url,z,"multiimage");h.exec("insertimage",z.url,z.title,z.width,z.height,z.border,z.align)});setTimeout(function(){h.hideDialog().focus()},0)}}}):g.popupMessage("\u60a8\u7684\u6d4f\u89c8\u5668\u4e0d\u652f\u6301\u6279\u91cf\u4e0a\u4f20\uff01\u5982\u679c\u4f60\u4f7f\u7528\u7684\u662f IE \u6d4f\u89c8\u5668\uff0c\u8bf7\u5c1d\u8bd5\u5347\u7ea7 Flash \u64ad\u653e\u5668")})});
KindEditor.plugin("pagebreak",function(g){var h=this,u=g.undef(h.pagebreakHtml,'<hr style="page-break-after: always;" class="ke-pagebreak" />');h.clickToolbar("pagebreak",function(){var v=h.cmd,m=v.range;h.focus();var t=h.newlineTag=="br"||g.WEBKIT?"":'<span id="__kindeditor_tail_tag__"></span>';h.insertHtml(u+t);if(t!==""){t=g("#__kindeditor_tail_tag__",h.edit.doc);m.selectNodeContents(t[0]);t.removeAttr("id");v.select()}})});
KindEditor.plugin("plainpaste",function(g){var h=this;h.clickToolbar("plainpaste",function(){var u='<div style="padding:10px 20px;"><div style="margin-bottom:10px;">'+h.lang("plainpaste.").comment+'</div><textarea class="ke-textarea" style="width:408px;height:260px;"></textarea></div>';u=h.createDialog({name:"plainpaste",width:450,title:h.lang("plainpaste"),body:u,yesBtn:{name:h.lang("yes"),click:function(){var m=v.val();m=g.escape(m);m=m.replace(/ {2}/g," &nbsp;");m=h.newlineTag=="p"?m.replace(/^/,
"<p>").replace(/$/,"</p>").replace(/\n/g,"</p><p>"):m.replace(/\n/g,"<br />$&");h.insertHtml(m).hideDialog().focus()}}});var v=g("textarea",u.div);v[0].focus()})});
KindEditor.plugin("preview",function(g){var h=this;h.clickToolbar("preview",function(){h.lang("preview.");var u=h.createDialog({name:"preview",width:750,title:h.lang("preview"),body:'<div style="padding:20px 20px;"><iframe class="ke-textarea" frameborder="0" style="width:699px;height:400px;"></iframe></div>'});u=g("iframe",u.div);var v=g.iframeDoc(u);v.open();v.write(h.fullHtml());v.close();g(v.body).css("background-color","#FFF");u[0].contentWindow.focus()})});
KindEditor.plugin("quickformat",function(g){function h(m){for(m=m.first();m&&m.first();)m=m.first();return m}var u=this,v=g.toMap("blockquote,center,div,h1,h2,h3,h4,h5,h6,p");u.clickToolbar("quickformat",function(){u.focus();for(var m=u.edit.doc,t=u.cmd.range,o=g(m.body).first(),l,q=[],p=[],r=t.createBookmark(true);o;){l=o.next();var z=h(o);if(!z||z.name!="img"){if(v[o.name]){o.html(o.html().replace(/^(\s|&nbsp;|\u3000)+/ig,""));o.css("text-indent","2em")}else p.push(o);if(!l||v[l.name]||v[o.name]&&
!v[l.name]){p.length>0&&q.push(p);p=[]}}o=l}g.each(q,function(H,C){var D=g('<p style="text-indent:2em;"></p>',m);C[0].before(D);g.each(C,function(Q,J){D.append(J)})});t.moveToBookmark(r);u.addBookmark()})});
KindEditor.plugin("table",function(g){function h(l,q){q=q.toUpperCase();l.css("background-color",q);l.css("color",q==="#000000"?"#FFFFFF":"#000000");l.html(q)}function u(l,q){function p(){g.each(o,function(){this.remove()});o=[];g(document).unbind("click,mousedown",p);l.unbind("click,mousedown",p)}q.bind("click,mousedown",function(r){r.stopPropagation()});q.click(function(){p();var r=g(this),z=r.pos();z=g.colorpicker({x:z.x,y:z.y+r.height(),z:811214,selectedColor:g(this).html(),colors:m.colorTable,
noColor:m.lang("noColor"),shadowMode:m.shadowMode,click:function(H){h(r,H);p()}});o.push(z);g(document).bind("click,mousedown",p);l.bind("click,mousedown",p)})}function v(l,q,p){for(var r=l=0,z=q.cells.length;r<z;r++){if(q.cells[r]==p)break;l+=q.cells[r].rowSpan-1}return p.cellIndex-l}var m=this,t=m.lang("table."),o=[];m.plugin.table={prop:function(l){var q=['<div style="padding:20px;"><div class="ke-dialog-row">','<label for="keRows" style="width:90px;">'+t.cells+"</label>",t.rows+' <input type="text" id="keRows" class="ke-input-text ke-input-number" name="rows" value="" maxlength="4" /> &nbsp; ',
t.cols+' <input type="text" class="ke-input-text ke-input-number" name="cols" value="" maxlength="4" />','</div><div class="ke-dialog-row">','<label for="keWidth" style="width:90px;">'+t.size+"</label>",t.width+' <input type="text" id="keWidth" class="ke-input-text ke-input-number" name="width" value="" maxlength="4" /> &nbsp; ','<select name="widthType">','<option value="%">'+t.percent+"</option>",'<option value="px">'+t.px+"</option>","</select> &nbsp; ",t.height+' <input type="text" class="ke-input-text ke-input-number" name="height" value="" maxlength="4" /> &nbsp; ',
'<select name="heightType">','<option value="%">'+t.percent+"</option>",'<option value="px">'+t.px+"</option>",'</select></div><div class="ke-dialog-row">','<label for="kePadding" style="width:90px;">'+t.space+"</label>",t.padding+' <input type="text" id="kePadding" class="ke-input-text ke-input-number" name="padding" value="" maxlength="4" /> &nbsp; ',t.spacing+' <input type="text" class="ke-input-text ke-input-number" name="spacing" value="" maxlength="4" />','</div><div class="ke-dialog-row">',
'<label for="keAlign" style="width:90px;">'+t.align+"</label>",'<select id="keAlign" name="align">','<option value="">'+t.alignDefault+"</option>",'<option value="left">'+t.alignLeft+"</option>",'<option value="center">'+t.alignCenter+"</option>",'<option value="right">'+t.alignRight+"</option>",'</select></div><div class="ke-dialog-row">','<label for="keBorder" style="width:90px;">'+t.border+"</label>",t.borderWidth+' <input type="text" id="keBorder" class="ke-input-text ke-input-number" name="border" value="" maxlength="4" /> &nbsp; ',
t.borderColor+' <span class="ke-inline-block ke-input-color"></span>','</div><div class="ke-dialog-row">','<label for="keBgColor" style="width:90px;">'+t.backgroundColor+"</label>",'<span class="ke-inline-block ke-input-color"></span></div></div>'].join(""),p=m.cmd.range.createBookmark();q=m.createDialog({name:"table",width:500,title:m.lang("table"),body:q,beforeRemove:function(){T.unbind()},yesBtn:{name:m.lang("yes"),click:function(){var Z=r.val(),I=z.val(),P=H.val(),aa=C.val(),R=D.val(),W=Q.val(),
X=J.val(),ha=da.val(),fa=B.val(),ma=M.val(),ra=g(T[0]).html()||"",ua=g(T[1]).html()||"";if(Z==0||!/^\d+$/.test(Z)){g.popupMessage(m.lang("invalidRows"));r[0].focus()}else if(I==0||!/^\d+$/.test(I)){g.popupMessage(m.lang("invalidRows"));z[0].focus()}else if(/^\d*$/.test(P))if(/^\d*$/.test(aa))if(/^\d*$/.test(X))if(/^\d*$/.test(ha))if(/^\d*$/.test(ma)){if(G){P!==""?G.width(P+R):G.css("width","");G[0].width!==undefined&&G.removeAttr("width");aa!==""?G.height(aa+W):G.css("height","");G[0].height!==undefined&&
G.removeAttr("height");G.css("background-color",ua);G[0].bgColor!==undefined&&G.removeAttr("bgColor");if(X!=="")G[0].cellPadding=X;else G.removeAttr("cellPadding");if(ha!=="")G[0].cellSpacing=ha;else G.removeAttr("cellSpacing");if(fa!=="")G[0].align=fa;else G.removeAttr("align");ma!==""?G.attr("border",ma):G.removeAttr("border");ma===""||ma==="0"?G.addClass("ke-zeroborder"):G.removeClass("ke-zeroborder");ra!==""?G.attr("borderColor",ra):G.removeAttr("borderColor");m.hideDialog().focus();m.cmd.range.moveToBookmark(p);
m.cmd.select()}else{var va="";if(P!=="")va+="width:"+P+R+";";if(aa!=="")va+="height:"+aa+W+";";if(ua!=="")va+="background-color:"+ua+";";P="<table";if(va!=="")P+=' style="'+va+'"';if(X!=="")P+=' cellpadding="'+X+'"';if(ha!=="")P+=' cellspacing="'+ha+'"';if(fa!=="")P+=' align="'+fa+'"';if(ma!=="")P+=' border="'+ma+'"';if(ma===""||ma==="0")P+=' class="ke-zeroborder"';if(ra!=="")P+=' bordercolor="'+ra+'"';P+=">";for(X=0;X<Z;X++){P+="<tr>";for(ha=0;ha<I;ha++)P+="<td>"+(g.IE?"&nbsp;":"<br />")+"</td>";
P+="</tr>"}P+="</table>";g.IE||(P+="<br />");m.insertHtml(P);m.select().hideDialog().focus()}m.addBookmark()}else{g.popupMessage(m.lang("invalidBorder"));M[0].focus()}else{g.popupMessage(m.lang("invalidSpacing"));da[0].focus()}else{g.popupMessage(m.lang("invalidPadding"));J[0].focus()}else{g.popupMessage(m.lang("invalidHeight"));C[0].focus()}else{g.popupMessage(m.lang("invalidWidth"));H[0].focus()}}}}).div;var r=g('[name="rows"]',q).val(3),z=g('[name="cols"]',q).val(2),H=g('[name="width"]',q).val(100),
C=g('[name="height"]',q),D=g('[name="widthType"]',q),Q=g('[name="heightType"]',q),J=g('[name="padding"]',q).val(2),da=g('[name="spacing"]',q).val(0),B=g('[name="align"]',q),M=g('[name="border"]',q).val(1),T=g(".ke-input-color",q);u(q,T.eq(0));u(q,T.eq(1));h(T.eq(0),"#000000");h(T.eq(1),"");r[0].focus();r[0].select();var G;if(!l)if(G=m.plugin.getSelectedTable()){r.val(G[0].rows.length);z.val(G[0].rows.length>0?G[0].rows[0].cells.length:0);r.attr("disabled",true);z.attr("disabled",true);var V;l=G[0].style.width||
G[0].width;q=G[0].style.height||G[0].height;if(l!==undefined&&(V=/^(\d+)((?:px|%)*)$/.exec(l))){H.val(V[1]);D.val(V[2])}else H.val("");if(q!==undefined&&(V=/^(\d+)((?:px|%)*)$/.exec(q))){C.val(V[1]);Q.val(V[2])}J.val(G[0].cellPadding||"");da.val(G[0].cellSpacing||"");B.val(G[0].align||"");M.val(G[0].border===undefined?"":G[0].border);h(T.eq(0),g.toHex(G.attr("borderColor")||""));h(T.eq(1),g.toHex(G[0].style.backgroundColor||G[0].bgColor||""));H[0].focus();H[0].select()}},cellprop:function(){var l=
['<div style="padding:20px;"><div class="ke-dialog-row">','<label for="keWidth" style="width:90px;">'+t.size+"</label>",t.width+' <input type="text" id="keWidth" class="ke-input-text ke-input-number" name="width" value="" maxlength="4" /> &nbsp; ','<select name="widthType">','<option value="%">'+t.percent+"</option>",'<option value="px">'+t.px+"</option>","</select> &nbsp; ",t.height+' <input type="text" class="ke-input-text ke-input-number" name="height" value="" maxlength="4" /> &nbsp; ','<select name="heightType">',
'<option value="%">'+t.percent+"</option>",'<option value="px">'+t.px+"</option>",'</select></div><div class="ke-dialog-row">','<label for="keAlign" style="width:90px;">'+t.align+"</label>",t.textAlign+' <select id="keAlign" name="textAlign">','<option value="">'+t.alignDefault+"</option>",'<option value="left">'+t.alignLeft+"</option>",'<option value="center">'+t.alignCenter+"</option>",'<option value="right">'+t.alignRight+"</option>","</select> ",t.verticalAlign+' <select name="verticalAlign">',
'<option value="">'+t.alignDefault+"</option>",'<option value="top">'+t.alignTop+"</option>",'<option value="middle">'+t.alignMiddle+"</option>",'<option value="bottom">'+t.alignBottom+"</option>",'<option value="baseline">'+t.alignBaseline+"</option>",'</select></div><div class="ke-dialog-row">','<label for="keBorder" style="width:90px;">'+t.border+"</label>",t.borderWidth+' <input type="text" id="keBorder" class="ke-input-text ke-input-number" name="border" value="" maxlength="4" /> &nbsp; ',t.borderColor+
' <span class="ke-inline-block ke-input-color"></span>','</div><div class="ke-dialog-row">','<label for="keBgColor" style="width:90px;">'+t.backgroundColor+"</label>",'<span class="ke-inline-block ke-input-color"></span></div></div>'].join(""),q=m.cmd.range.createBookmark();l=m.createDialog({name:"table",width:500,title:m.lang("tablecell"),body:l,beforeRemove:function(){B.unbind()},yesBtn:{name:m.lang("yes"),click:function(){var G=p.val(),V=r.val(),Z=z.val(),I=H.val();C.val();D.val();var P=Q.val(),
aa=J.val(),R=da.val(),W=g(B[0]).html()||"",X=g(B[1]).html()||"";if(/^\d*$/.test(G))if(/^\d*$/.test(V))if(/^\d*$/.test(R)){M.css({width:G!==""?G+Z:"",height:V!==""?V+I:"","background-color":X,"text-align":P,"vertical-align":aa,"border-width":R,"border-style":R!==""?"solid":"","border-color":W});m.hideDialog().focus();m.cmd.range.moveToBookmark(q);m.cmd.select();m.addBookmark()}else{g.popupMessage(m.lang("invalidBorder"));da[0].focus()}else{g.popupMessage(m.lang("invalidHeight"));r[0].focus()}else{g.popupMessage(m.lang("invalidWidth"));
p[0].focus()}}}}).div;var p=g('[name="width"]',l).val(100),r=g('[name="height"]',l),z=g('[name="widthType"]',l),H=g('[name="heightType"]',l),C=g('[name="padding"]',l).val(2),D=g('[name="spacing"]',l).val(0),Q=g('[name="textAlign"]',l),J=g('[name="verticalAlign"]',l),da=g('[name="border"]',l).val(1),B=g(".ke-input-color",l);u(l,B.eq(0));u(l,B.eq(1));h(B.eq(0),"#000000");h(B.eq(1),"");p[0].focus();p[0].select();var M=m.plugin.getSelectedCell(),T=M[0].style.height||M[0].height||"";if(l=/^(\d+)((?:px|%)*)$/.exec(M[0].style.width||
M[0].width||"")){p.val(l[1]);z.val(l[2])}else p.val("");if(l=/^(\d+)((?:px|%)*)$/.exec(T)){r.val(l[1]);H.val(l[2])}Q.val(M[0].style.textAlign||"");J.val(M[0].style.verticalAlign||"");if(l=M[0].style.borderWidth||"")l=parseInt(l);da.val(l);h(B.eq(0),g.toHex(M[0].style.borderColor||""));h(B.eq(1),g.toHex(M[0].style.backgroundColor||""));p[0].focus();p[0].select()},insert:function(){this.prop(true)},"delete":function(){var l=m.plugin.getSelectedTable();m.cmd.range.setStartBefore(l[0]).collapse(true);
m.cmd.select();l.remove();m.addBookmark()},colinsert:function(l){var q=m.plugin.getSelectedTable()[0],p=m.plugin.getSelectedRow()[0],r=m.plugin.getSelectedCell()[0];l=r.cellIndex+l;l+=q.rows[0].cells.length-p.cells.length;p=0;for(var z=q.rows.length;p<z;p++){var H=q.rows[p];l=H.insertCell(l);l.innerHTML=g.IE?"":"<br />";l=v(q,H,l)}m.cmd.range.selectNodeContents(r).collapse(true);m.cmd.select();m.addBookmark()},colinsertleft:function(){this.colinsert(0)},colinsertright:function(){this.colinsert(1)},
rowinsert:function(l){var q=m.plugin.getSelectedTable()[0],p=m.plugin.getSelectedRow()[0],r=m.plugin.getSelectedCell()[0],z=p.rowIndex;if(l===1)z=p.rowIndex+(r.rowSpan-1)+l;for(var H=q.insertRow(z),C=0,D=p.cells.length;C<D;C++){if(p.cells[C].rowSpan>1)D-=p.cells[C].rowSpan-1;var Q=H.insertCell(C);if(l===1&&p.cells[C].colSpan>1)Q.colSpan=p.cells[C].colSpan;Q.innerHTML=g.IE?"":"<br />"}for(p=z;p>=0;p--){l=q.rows[p].cells;if(l.length>C){for(q=r.cellIndex;q>=0;q--)if(l[q].rowSpan>1)l[q].rowSpan+=1;break}}m.cmd.range.selectNodeContents(r).collapse(true);
m.cmd.select();m.addBookmark()},rowinsertabove:function(){this.rowinsert(0)},rowinsertbelow:function(){this.rowinsert(1)},rowmerge:function(){var l=m.plugin.getSelectedTable()[0],q=m.plugin.getSelectedRow()[0],p=m.plugin.getSelectedCell()[0],r=q.rowIndex+p.rowSpan;q=l.rows[r];if(!(l.rows.length<=r)){l=p.cellIndex;if(!(q.cells.length<=l)){r=q.cells[l];if(p.colSpan===r.colSpan){p.rowSpan+=r.rowSpan;q.deleteCell(l);m.cmd.range.selectNodeContents(p).collapse(true);m.cmd.select();m.addBookmark()}}}},colmerge:function(){m.plugin.getSelectedTable();
var l=m.plugin.getSelectedRow()[0],q=m.plugin.getSelectedCell()[0],p=q.cellIndex+1;if(!(l.cells.length<=p)){var r=l.cells[p];if(q.rowSpan===r.rowSpan){q.colSpan+=r.colSpan;l.deleteCell(p);m.cmd.range.selectNodeContents(q).collapse(true);m.cmd.select();m.addBookmark()}}},rowsplit:function(){var l=m.plugin.getSelectedTable()[0],q=m.plugin.getSelectedRow()[0],p=m.plugin.getSelectedCell()[0],r=q.rowIndex;if(p.rowSpan!==1){var z=v(l,q,p);q=1;for(var H=p.rowSpan;q<H;q++){var C=l.rows[r+q];z=C.insertCell(z);
if(p.colSpan>1)z.colSpan=p.colSpan;z.innerHTML=g.IE?"":"<br />";z=v(l,C,z)}g(p).removeAttr("rowSpan");m.cmd.range.selectNodeContents(p).collapse(true);m.cmd.select();m.addBookmark()}},colsplit:function(){m.plugin.getSelectedTable();var l=m.plugin.getSelectedRow()[0],q=m.plugin.getSelectedCell()[0],p=q.cellIndex;if(q.colSpan!==1){for(var r=1,z=q.colSpan;r<z;r++){var H=l.insertCell(p+r);if(q.rowSpan>1)H.rowSpan=q.rowSpan;H.innerHTML=g.IE?"":"<br />"}g(q).removeAttr("colSpan");m.cmd.range.selectNodeContents(q).collapse(true);
m.cmd.select();m.addBookmark()}},coldelete:function(){for(var l=m.plugin.getSelectedTable()[0],q=m.plugin.getSelectedRow()[0],p=m.plugin.getSelectedCell()[0].cellIndex,r=0,z=l.rows.length;r<z;r++){var H=l.rows[r],C=H.cells[p];if(C.colSpan>1){C.colSpan-=1;C.colSpan===1&&g(C).removeAttr("colSpan")}else H.deleteCell(p);if(C.rowSpan>1)r+=C.rowSpan-1}if(q.cells.length===0){m.cmd.range.setStartBefore(l).collapse(true);m.cmd.select();g(l).remove()}else m.cmd.selection(true);m.addBookmark()},rowdelete:function(){var l=
m.plugin.getSelectedTable()[0],q=m.plugin.getSelectedRow()[0],p=m.plugin.getSelectedCell()[0];q=q.rowIndex;for(p=p.rowSpan-1;p>=0;p--)l.deleteRow(q+p);if(l.rows.length===0){m.cmd.range.setStartBefore(l).collapse(true);m.cmd.select();g(l).remove()}else m.cmd.selection(true);m.addBookmark()}};m.clickToolbar("table",m.plugin.table.prop)});
KindEditor.plugin("template",function(g){function h(m){return v+m+"?ver="+encodeURIComponent(g.DEBUG?g.TIME:g.VERSION)}var u=this;u.lang("template.");var v=u.pluginsPath+"template/html/";u.clickToolbar("template",function(){var m=u.lang("template."),t=['<div style="padding:20px 20px;">','<div class="ke-header">','<div class="ke-left">',m.selectTemplate+" <select>"];g.each(m.fileList,function(p,r){t.push('<option value="'+p+'">'+r+"</option>")});html=[t.join(""),'</select></div><div class="ke-right">',
'<input type="checkbox" id="keReplaceFlag" name="replaceFlag" value="1" /> <label for="keReplaceFlag">'+m.replaceContent+"</label>",'</div><div class="ke-clearfix"></div></div><iframe class="ke-textarea" frameborder="0" style="width:449px;height:260px;background-color:#FFF;"></iframe></div>'].join("");m=u.createDialog({name:"template",width:500,title:u.lang("template"),body:html,yesBtn:{name:u.lang("yes"),click:function(){var p=g.iframeDoc(q);u[l[0].checked?"html":"insertHtml"](p.body.innerHTML).hideDialog().focus()}}});
var o=g("select",m.div),l=g('[name="replaceFlag"]',m.div),q=g("iframe",m.div);l[0].checked=true;q.attr("src",h(o.val()));o.change(function(){q.attr("src",h(this.value))})})});
KindEditor.plugin("wordpaste",function(g){var h=this;h.clickToolbar("wordpaste",function(){var u='<div style="padding:10px 20px;"><div style="margin-bottom:10px;">'+h.lang("wordpaste.").comment+'</div><iframe class="ke-textarea" frameborder="0" style="width:408px;height:260px;"></iframe></div>';u=h.createDialog({name:"wordpaste",width:450,title:h.lang("wordpaste"),body:u,yesBtn:{name:h.lang("yes"),click:function(){var m=v.body.innerHTML;m=g.clearMsWord(m,h.filterMode?h.htmlTags:g.options.htmlTags);
h.insertHtml(m).hideDialog().focus()}}}).div;u=g("iframe",u);var v=g.iframeDoc(u);if(!g.IE)v.designMode="on";v.open();v.write("<!doctype html><html><head><title>WordPaste</title></head>");v.write('<body style="background-color:#FFF;font-size:12px;margin:2px;">');g.IE||v.write("<br />");v.write("</body></html>");v.close();if(g.IE)v.body.contentEditable="true";u[0].contentWindow.focus()})});
