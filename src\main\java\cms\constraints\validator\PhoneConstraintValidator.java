package cms.constraints.validator;

import cms.constraints.PhoneNumber;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/4/21 10:49
 */
public class PhoneConstraintValidator implements ConstraintValidator<PhoneNumber, String> {

    private static final Pattern PATTERN = Pattern.compile("^[1]\\d{10}$");

    @Override
    public void initialize(PhoneNumber constraintAnnotation) {

    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        return Optional.ofNullable(value).map(String::trim).filter(StringUtils::isNotEmpty).map(o -> {
            Matcher matcher = PATTERN.matcher(o);
            return matcher.find();
        }).orElse(true);
    }
}
