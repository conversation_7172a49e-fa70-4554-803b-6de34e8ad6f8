package cms.constraints.validator;

import cms.constraints.PatternString;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/4/21 12:03
 */
public class PatternStringConstraintValidator implements ConstraintValidator<PatternString, String> {

    private String regex;

    @Override
    public void initialize(PatternString constraintAnnotation) {
        this.regex = constraintAnnotation.regex();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        Pattern PATTERN = Pattern.compile(regex);
        return Optional.ofNullable(value).map(String::trim).filter(StringUtils::isNotEmpty).map(o -> {
            Matcher matcher = PATTERN.matcher(o);
            return matcher.find();
        }).orElse(true);
    }
}
