package cms.bean.setting;

import cms.bean.BaseUserDel;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/28 11:33
 */
@Table(name = "emailRemindSetting", uniqueConstraints = {@UniqueConstraint(columnNames = {"remindType"})})
@Entity
@Data
public class EmailRemindSetting extends BaseUserDel implements Serializable {

    private static final long serialVersionUID = 3128993157307005006L;

    @Column(nullable = false)
    private Integer remindType;

    @Column(nullable = false)
    private Integer sendNum = 1;

    @Column(nullable = false)
    private String titleTemplate;

    @Column(nullable = false)
    private String contentTemplate;
}
