package cms.web.action.api;

import cms.bean.RequestResult;
import cms.bean.api.TopicApi;
import cms.bean.api.TopicCommentApi;
import cms.handle.CustomException;
import cms.service.topic.impl.TopicFormService;
import cms.service.user.impl.UserLoginService;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static cms.constant.ErrorCode.C_2_0002_0002;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/5/29 15:25
 */
@Controller
@RequestMapping("api/control/topic")
@Validated
@Slf4j
public class TopicApiController {

    @Autowired
    private TopicFormService topicFormService;


    @Value("${bbs.justUrlTitle}")
    private String justUrlTitle;


    @Deprecated
    @ResponseBody
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public RequestResult add(@RequestBody TopicApi topicApi, HttpServletRequest httpServletRequest) {
        try {
            Long id = topicFormService.addByApi(httpServletRequest, topicApi);
            Map<String, Object> map = MapUtil.builder(new HashMap<String, Object>(4)).put("id", id).put("url", String.format("%s/api/control/topic/justId/%s", justUrlTitle, id)).build();
            return RequestResult.ok(map);
        } catch (CustomException e) {
            e.setResultCode(Boolean.TRUE);
            throw e;
        }
    }

    @Deprecated
    @ResponseBody
    @RequestMapping(value = "/addTopicComment", method = RequestMethod.POST)
    public RequestResult addTopicComment(@RequestBody TopicCommentApi topicApi, HttpServletRequest httpServletRequest) {
        try {
            Optional.ofNullable(topicApi).map(v -> Optional.of(v).filter(o -> null != o.getTopicInfo()).orElseThrow(() -> new CustomException(C_2_0002_0002, "topicInfo"))).map(TopicCommentApi::getTopicApi).ifPresent(o -> topicFormService.addByApi(httpServletRequest, o));
        } catch (CustomException e) {
            e.setResultCode(Boolean.TRUE);
        }
        return RequestResult.ok("操作成功！");
    }
}
